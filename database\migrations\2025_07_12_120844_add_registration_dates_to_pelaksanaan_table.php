<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pelaksanaan', function (Blueprint $table) {
            // Menambahkan kolom tanggal buka dan tutup pendaftaran
            $table->date('tanggal_buka_pendaftaran')->after('tanggal_selesai');
            $table->date('tanggal_tutup_pendaftaran')->after('tanggal_buka_pendaftaran');

            // Menghapus kolom batas_pendaftaran yang lama jika ada
            if (Schema::hasColumn('pelaksanaan', 'batas_pendaftaran')) {
                $table->dropColumn('batas_pendaftaran');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pelaksanaan', function (Blueprint $table) {
            // Mengembalikan kolom batas_pendaftaran
            $table->date('batas_pendaftaran')->after('alamat_lengkap');

            // Menghapus kolom yang ditambahkan
            $table->dropColumn(['tanggal_buka_pendaftaran', 'tanggal_tutup_pendaftaran']);
        });
    }
};
