<template>
  <div class="border rounded-lg p-4 space-y-3">
    <div class="flex items-center justify-between">
      <div>
        <h4 class="font-medium">{{ verificationType.name }}</h4>
        <p class="text-sm text-gray-500">{{ verificationType.description }}</p>
      </div>
      <Badge :variant="getVerificationStatusVariant()">
        {{ getVerificationStatusLabel() }}
      </Badge>
    </div>

    <!-- Verification Details -->
    <div v-if="existingVerification" class="space-y-2">
      <div class="p-3 bg-gray-50 rounded border">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium">Status Verifikasi</span>
          <Badge :variant="getStatusVariant(existingVerification.status)">
            {{ getStatusLabel(existingVerification.status) }}
          </Badge>
        </div>
        
        <div v-if="existingVerification.verified_by" class="text-xs text-gray-500 mb-2">
          Diverifikasi oleh: {{ existingVerification.verified_by.nama_lengkap }} • 
          {{ formatDate(existingVerification.verified_at) }}
        </div>
        
        <div v-if="existingVerification.notes" class="text-sm text-gray-700">
          <strong>Catatan:</strong> {{ existingVerification.notes }}
        </div>
        
        <!-- Verification Data -->
        <div v-if="existingVerification.verification_data" class="mt-2">
          <details class="text-sm">
            <summary class="cursor-pointer text-blue-600 hover:text-blue-800">
              Lihat Data Verifikasi
            </summary>
            <pre class="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">{{ JSON.stringify(existingVerification.verification_data, null, 2) }}</pre>
          </details>
        </div>
      </div>
    </div>

    <!-- Verification Actions -->
    <div v-if="canVerify" class="flex items-center space-x-2">
      <Button
        @click="showVerificationForm = !showVerificationForm"
        size="sm"
        variant="outline"
      >
        <Icon name="edit" class="w-4 h-4 mr-2" />
        {{ existingVerification ? 'Update Verifikasi' : 'Verifikasi' }}
      </Button>
    </div>

    <!-- Verification Form -->
    <div v-if="showVerificationForm" class="mt-4 p-4 border rounded bg-gray-50">
      <form @submit.prevent="handleVerification" class="space-y-3">
        <!-- Status Selection -->
        <div>
          <Label for="status">Status Verifikasi</Label>
          <Select v-model="form.status" required>
            <SelectTrigger>
              <SelectValue placeholder="Pilih status verifikasi" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="verified">
                <div class="flex items-center space-x-2">
                  <Icon name="check" class="w-4 h-4 text-green-600" />
                  <span>Terverifikasi</span>
                </div>
              </SelectItem>
              <SelectItem value="rejected">
                <div class="flex items-center space-x-2">
                  <Icon name="x" class="w-4 h-4 text-red-600" />
                  <span>Ditolak</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- Notes -->
        <div>
          <Label for="notes">Catatan Verifikasi</Label>
          <Textarea
            id="notes"
            v-model="form.notes"
            placeholder="Tambahkan catatan verifikasi"
            rows="2"
            :required="form.status === 'rejected'"
          />
        </div>

        <!-- NIK Verification Specific Fields -->
        <div v-if="verificationType.code === 'nik'" class="space-y-2">
          <Label>Data NIK</Label>
          <div class="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span class="text-gray-500">NIK:</span>
              <span class="ml-2 font-mono">{{ registration.peserta.nik }}</span>
            </div>
            <div>
              <span class="text-gray-500">Nama:</span>
              <span class="ml-2">{{ registration.peserta.nama_lengkap }}</span>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center space-x-2">
          <Button 
            type="submit" 
            size="sm"
            :disabled="!form.status || isSubmitting"
            :class="{
              'bg-green-600 hover:bg-green-700': form.status === 'verified',
              'bg-red-600 hover:bg-red-700': form.status === 'rejected'
            }"
          >
            <Icon 
              v-if="isSubmitting" 
              name="loader" 
              class="w-4 h-4 mr-2 animate-spin" 
            />
            {{ isSubmitting ? 'Memproses...' : 'Simpan Verifikasi' }}
          </Button>
          <Button 
            type="button" 
            size="sm" 
            variant="outline"
            @click="cancelVerification"
          >
            Batal
          </Button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  verificationType: any
  registration: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'verify': [data: { verification_type_id: number; status: string; notes: string; verification_data?: any }]
}>()

// Reactive data
const showVerificationForm = ref(false)
const isSubmitting = ref(false)
const form = ref({
  status: '',
  notes: ''
})

// Computed
const existingVerification = computed(() => {
  return props.registration.peserta.verifications?.find(
    (v: any) => v.verification_type_id === props.verificationType.id_verification_type
  )
})

const canVerify = computed(() => {
  return ['submitted', 'paid'].includes(props.registration.status_pendaftaran)
})

// Watch for existing verification to populate form
watch(showVerificationForm, (newValue) => {
  if (newValue && existingVerification.value) {
    form.value = {
      status: existingVerification.value.status,
      notes: existingVerification.value.notes || ''
    }
  } else if (!newValue) {
    form.value = {
      status: '',
      notes: ''
    }
  }
})

// Methods
const handleVerification = async () => {
  if (!form.value.status) return
  if (form.value.status === 'rejected' && !form.value.notes.trim()) return

  isSubmitting.value = true
  
  try {
    const verificationData: any = {}
    
    // Add specific data based on verification type
    if (props.verificationType.code === 'nik') {
      verificationData.nik = props.registration.peserta.nik
      verificationData.nama_lengkap = props.registration.peserta.nama_lengkap
    }
    
    emit('verify', {
      verification_type_id: props.verificationType.id_verification_type,
      status: form.value.status,
      notes: form.value.notes,
      verification_data: verificationData
    })
    
    showVerificationForm.value = false
  } finally {
    isSubmitting.value = false
  }
}

const cancelVerification = () => {
  showVerificationForm.value = false
  form.value = {
    status: '',
    notes: ''
  }
}

const getVerificationStatusVariant = () => {
  if (!existingVerification.value) return 'secondary'
  
  const status = existingVerification.value.status
  if (status === 'verified') return 'default'
  if (status === 'rejected') return 'destructive'
  return 'outline'
}

const getVerificationStatusLabel = () => {
  if (!existingVerification.value) return 'Belum Diverifikasi'
  
  const status = existingVerification.value.status
  if (status === 'verified') return 'Terverifikasi'
  if (status === 'rejected') return 'Ditolak'
  return 'Pending'
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'pending': 'outline',
    'verified': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'pending': 'Pending',
    'verified': 'Terverifikasi',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
