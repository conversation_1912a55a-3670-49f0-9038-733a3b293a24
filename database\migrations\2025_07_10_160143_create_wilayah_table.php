<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wilayah', function (Blueprint $table) {
            $table->id('id_wilayah');
            $table->string('kode_wilayah', 10)->unique();
            $table->string('nama_wilayah', 100);
            $table->enum('level_wilayah', ['provinsi', 'kabupaten', 'kota']);
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->enum('status', ['aktif', 'non_aktif'])->default('aktif');
            $table->timestamps();

            $table->foreign('parent_id')->references('id_wilayah')->on('wilayah')->onDelete('set null');
            $table->index(['level_wilayah', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wilayah');
    }
};
