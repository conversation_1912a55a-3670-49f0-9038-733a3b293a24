<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Detail Jenis Dokumen" />
    <Heading title="Detail Jenis Dokumen" />

    <div class="max-w-6xl mx-auto space-y-6">
      <!-- Document Type Information -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between mb-6">
            <div>
              <h2 class="text-2xl font-bold text-gray-900">{{ documentType.name }}</h2>
              <p class="text-gray-600">{{ documentType.description }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <Badge :variant="documentType.is_active ? 'default' : 'destructive'">
                {{ documentType.is_active ? 'Aktif' : 'Non Aktif' }}
              </Badge>
              <Badge :variant="documentType.is_required_default ? 'default' : 'secondary'">
                {{ documentType.is_required_default ? 'Required Default' : 'Optional Default' }}
              </Badge>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <Label class="text-sm font-medium text-gray-500">Kode</Label>
              <p class="mt-1 text-sm font-mono bg-gray-100 px-2 py-1 rounded">{{ documentType.code }}</p>
            </div>

            <div>
              <Label class="text-sm font-medium text-gray-500">Jenis File</Label>
              <div class="mt-1 flex flex-wrap gap-1">
                <span 
                  v-for="fileType in documentType.allowed_file_types" 
                  :key="fileType"
                  class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {{ fileType.toUpperCase() }}
                </span>
              </div>
            </div>

            <div>
              <Label class="text-sm font-medium text-gray-500">Ukuran Maksimal</Label>
              <p class="mt-1 text-sm">{{ formatFileSize(documentType.max_file_size) }}</p>
            </div>

            <div>
              <Label class="text-sm font-medium text-gray-500">Urutan Tampil</Label>
              <p class="mt-1 text-sm">{{ documentType.sort_order }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Usage Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="users" class="w-8 h-8 text-blue-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Golongan Menggunakan</p>
                <p class="text-2xl font-bold text-gray-900">{{ documentType.golongan_requirements?.length || 0 }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="file" class="w-8 h-8 text-green-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Dokumen Terupload</p>
                <p class="text-2xl font-bold text-gray-900">{{ documentType.dokumen_peserta?.length || 0 }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="calendar" class="w-8 h-8 text-purple-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Dibuat</p>
                <p class="text-sm font-bold text-gray-900">{{ formatDate(documentType.created_at) }}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Golongan Requirements -->
      <Card v-if="documentType.golongan_requirements?.length > 0">
        <CardContent class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Golongan yang Menggunakan</h3>
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Golongan
                  </th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cabang Lomba
                  </th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Wajib
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="requirement in documentType.golongan_requirements" :key="requirement.id_requirement">
                  <td class="px-4 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ requirement.golongan?.nama_golongan }}</div>
                      <div class="text-sm text-gray-500">{{ requirement.golongan?.kode_golongan }}</div>
                    </div>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ requirement.golongan?.cabang_lomba?.nama_cabang }}
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <Badge :variant="requirement.golongan?.status === 'aktif' ? 'default' : 'secondary'">
                      {{ requirement.golongan?.status }}
                    </Badge>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <Badge :variant="requirement.is_required ? 'default' : 'secondary'">
                      {{ requirement.is_required ? 'Wajib' : 'Opsional' }}
                    </Badge>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex items-center justify-between">
        <Button
          variant="outline"
          @click="$inertia.visit(route('admin.document-types.index'))"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Kembali ke Daftar
        </Button>

        <div class="flex items-center space-x-2">
          <Button
            variant="outline"
            @click="toggleStatus"
            :class="documentType.is_active ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'"
          >
            <Icon :name="documentType.is_active ? 'x' : 'check'" class="w-4 h-4 mr-2" />
            {{ documentType.is_active ? 'Nonaktifkan' : 'Aktifkan' }}
          </Button>

          <Button @click="$inertia.visit(route('admin.document-types.edit', documentType.id_document_type))">
            <Icon name="edit" class="w-4 h-4 mr-2" />
            Edit
          </Button>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/Icon.vue'

const props = defineProps({
  documentType: Object
})

const breadcrumbItems = [
  { label: 'Dashboard', href: route('admin.dashboard') },
  { label: 'Manajemen Jenis Dokumen', href: route('admin.document-types.index') },
  { label: 'Detail Jenis Dokumen', href: null }
]

const formatFileSize = (sizeInKB) => {
  if (!sizeInKB) return ''
  if (sizeInKB < 1024) {
    return `${sizeInKB} KB`
  }
  return `${(sizeInKB / 1024).toFixed(1)} MB`
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const toggleStatus = () => {
  router.post(route('admin.document-types.toggle-status', props.documentType.id_document_type), {}, {
    preserveScroll: true
  })
}
</script>
