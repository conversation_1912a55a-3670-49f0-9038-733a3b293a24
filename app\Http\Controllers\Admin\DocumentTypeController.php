<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DocumentType;
use App\Traits\HasVerificationPermissions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class DocumentTypeController extends Controller
{
    use HasVerificationPermissions;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $this->requireSuperAdminAccess();

        $query = DocumentType::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by active status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $documentTypes = $query->ordered()->paginate(20);

        return Inertia::render('Admin/DocumentTypes/Index', [
            'documentTypes' => $documentTypes,
            'filters' => $request->only(['search', 'status'])
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $this->requireSuperAdminAccess();

        return Inertia::render('Admin/DocumentTypes/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->requireSuperAdminAccess();

        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'code' => 'required|string|max:50|unique:document_types,code',
            'description' => 'nullable|string|max:1000',
            'allowed_file_types' => 'required|array|min:1',
            'allowed_file_types.*' => 'required|string|in:jpg,jpeg,png,pdf,doc,docx',
            'max_file_size' => 'required|integer|min:100|max:10240', // 100KB to 10MB
            'is_required_default' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        DocumentType::create($validated);

        return redirect()->route('admin.document-types.index')
            ->with('success', 'Jenis dokumen berhasil dibuat.');
    }

    /**
     * Display the specified resource.
     */
    public function show(DocumentType $documentType): Response
    {
        $this->requireSuperAdminAccess();

        $documentType->load(['golonganRequirements.golongan.cabangLomba']);

        return Inertia::render('Admin/DocumentTypes/Show', [
            'documentType' => $documentType
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DocumentType $documentType): Response
    {
        $this->requireSuperAdminAccess();

        return Inertia::render('Admin/DocumentTypes/Edit', [
            'documentType' => $documentType
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, DocumentType $documentType)
    {
        $this->requireSuperAdminAccess();

        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'code' => 'required|string|max:50|unique:document_types,code,' . $documentType->id_document_type . ',id_document_type',
            'description' => 'nullable|string|max:1000',
            'allowed_file_types' => 'required|array|min:1',
            'allowed_file_types.*' => 'required|string|in:jpg,jpeg,png,pdf,doc,docx',
            'max_file_size' => 'required|integer|min:100|max:10240',
            'is_required_default' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        $documentType->update($validated);

        return redirect()->route('admin.document-types.index')
            ->with('success', 'Jenis dokumen berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DocumentType $documentType)
    {
        $this->requireSuperAdminAccess();

        // Check if document type is being used
        if ($documentType->dokumenPeserta()->exists()) {
            return back()->withErrors(['error' => 'Jenis dokumen tidak dapat dihapus karena masih digunakan oleh dokumen peserta.']);
        }

        if ($documentType->golonganRequirements()->exists()) {
            return back()->withErrors(['error' => 'Jenis dokumen tidak dapat dihapus karena masih digunakan dalam persyaratan golongan.']);
        }

        $documentType->delete();

        return redirect()->route('admin.document-types.index')
            ->with('success', 'Jenis dokumen berhasil dihapus.');
    }

    /**
     * Toggle active status
     */
    public function toggleStatus(DocumentType $documentType)
    {
        $this->requireSuperAdminAccess();

        $documentType->update([
            'is_active' => !$documentType->is_active
        ]);

        $status = $documentType->is_active ? 'diaktifkan' : 'dinonaktifkan';

        return back()->with('success', "Jenis dokumen berhasil {$status}.");
    }
}
