<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Verifikasi NIK Peserta" />
    <Heading title="Verifikasi NIK Peserta" />

    <div class="space-y-6">
      <!-- Search and Filters -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama peserta, NIK..."
                @input="search"
              />
            </div>
            <div class="flex items-end">
              <Button @click="clearFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="users" class="w-8 h-8 text-blue-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Peserta</p>
                <p class="text-2xl font-bold text-gray-900">{{ participants.total }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="clock" class="w-8 h-8 text-yellow-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Menunggu Verifikasi</p>
                <p class="text-2xl font-bold text-gray-900">{{ pendingCount }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="check-circle" class="w-8 h-8 text-green-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Terverifikasi</p>
                <p class="text-2xl font-bold text-gray-900">{{ verifiedCount }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="x-circle" class="w-8 h-8 text-red-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Ditolak</p>
                <p class="text-2xl font-bold text-gray-900">{{ rejectedCount }}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Bulk Actions -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-600">
              {{ selectedParticipants.length }} peserta dipilih
            </div>
            <div class="flex items-center space-x-2">
              <Button
                @click="openBulkDialog('verify')"
                :disabled="selectedParticipants.length === 0"
                variant="outline"
                class="text-green-600 hover:text-green-700"
              >
                <Icon name="check" class="w-4 h-4 mr-2" />
                Verifikasi Terpilih
              </Button>
              <Button
                @click="openBulkDialog('reject')"
                :disabled="selectedParticipants.length === 0"
                variant="outline"
                class="text-red-600 hover:text-red-700"
              >
                <Icon name="x" class="w-4 h-4 mr-2" />
                Tolak Terpilih
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Participants List -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left">
                    <Checkbox
                      :checked="allSelected"
                      @update:checked="toggleSelectAll"
                    />
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Peserta
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    NIK
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Golongan
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status NIK
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="participant in participants.data" :key="participant.id_peserta" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Checkbox
                      :checked="selectedParticipants.includes(participant.id_peserta)"
                      @update:checked="toggleParticipantSelection(participant.id_peserta)"
                    />
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ participant.nama_peserta }}</div>
                      <div class="text-sm text-gray-500">{{ participant.tempat_lahir }}, {{ formatDate(participant.tanggal_lahir) }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-mono text-gray-900">{{ participant.nik }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ participant.pendaftaran?.[0]?.golongan?.nama_golongan }}</div>
                      <div class="text-sm text-gray-500">{{ participant.pendaftaran?.[0]?.golongan?.cabang_lomba?.nama_cabang }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center space-x-2">
                      <Badge
                        :variant="isPending(participant.id_peserta) ? 'secondary' : 'warning'"
                        class="transition-all duration-200"
                      >
                        <Icon
                          v-if="isPending(participant.id_peserta)"
                          name="loader-2"
                          class="w-3 h-3 mr-1 animate-spin"
                        />
                        {{ getStatusText(participant) }}
                      </Badge>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-2">
                      <Button
                        @click="verifyNik(participant, 'verified')"
                        size="sm"
                        class="bg-green-600 hover:bg-green-700"
                        :disabled="isPending(participant.id_peserta)"
                      >
                        <Icon
                          :name="isPending(participant.id_peserta) ? 'loader-2' : 'check'"
                          :class="['w-4 h-4 mr-1', { 'animate-spin': isPending(participant.id_peserta) }]"
                        />
                        Valid
                      </Button>
                      <Button
                        @click="verifyNik(participant, 'rejected')"
                        size="sm"
                        variant="destructive"
                        :disabled="isPending(participant.id_peserta)"
                      >
                        <Icon
                          :name="isPending(participant.id_peserta) ? 'loader-2' : 'x'"
                          :class="['w-4 h-4 mr-1', { 'animate-spin': isPending(participant.id_peserta) }]"
                        />
                        Invalid
                      </Button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Menampilkan {{ participants.from }} sampai {{ participants.to }} dari {{ participants.total }} hasil
        </div>
        <Pagination
          :current-page="participants.current_page"
          :last-page="participants.last_page"
          :links="participants.links"
        />
      </div>
    </div>

    <!-- Verification Notes Dialog -->
    <Dialog v-model:open="notesDialog.open">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Catatan Verifikasi</DialogTitle>
          <DialogDescription>
            Tambahkan catatan untuk verifikasi NIK {{ notesDialog.participant?.nama_peserta }}
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4">
          <div>
            <Label for="notes">Catatan</Label>
            <Textarea
              id="notes"
              v-model="notesDialog.notes"
              placeholder="Tambahkan catatan verifikasi..."
              rows="3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="notesDialog.open = false">Batal</Button>
          <Button @click="submitVerification">Simpan</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Bulk Operation Dialog -->
    <BulkOperationDialog
      v-model:open="bulkDialog.open"
      :operation-type="bulkDialog.operationType"
      :selected-count="selectedParticipants.length"
      :is-processing="bulkDialog.isProcessing"
      :processed-count="bulkDialog.processedCount"
      :current-operation="bulkDialog.currentOperation"
      :errors="bulkDialog.errors"
      @confirm="handleBulkOperation"
      @cancel="closeBulkDialog"
    />

    <!-- Notification -->
    <VerificationNotification
      :show="notification.show"
      :type="notification.type"
      :title="notification.title"
      :message="notification.message"
      :show-progress="notification.showProgress"
      :progress="notification.progress"
      :errors="notification.errors"
      @close="notification.show = false"
    />
  </AppLayout>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import { debounce } from 'lodash'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import Pagination from '@/components/Pagination.vue'
import Icon from '@/components/Icon.vue'
import BulkOperationDialog from '@/components/BulkOperationDialog.vue'
import VerificationNotification from '@/components/VerificationNotification.vue'
import { useRealTimeVerification, useOptimisticUpdates } from '@/composables/useRealTimeVerification'

const props = defineProps({
  participants: Object,
  nikVerificationType: Object,
  filters: Object
})

const breadcrumbItems = [
  { label: 'Dashboard', href: route('admin.dashboard') },
  { label: 'Verifikasi Peserta', href: route('admin.participant-verifications.index') },
  { label: 'Verifikasi NIK', href: null }
]

const filters = reactive({
  search: props.filters?.search || ''
})

const selectedParticipants = ref([])

// Real-time verification stats
const { stats, refreshStats } = useRealTimeVerification(props.nikVerificationType, filters)

// Optimistic updates for better UX
const { addPendingOperation, removePendingOperation, isPending, getPendingOperation } = useOptimisticUpdates()

const notesDialog = reactive({
  open: false,
  participant: null,
  status: null,
  notes: ''
})

// Bulk operation dialog state
const bulkDialog = reactive({
  open: false,
  operationType: 'verify', // 'verify' or 'reject'
  isProcessing: false,
  processedCount: 0,
  currentOperation: '',
  errors: []
})

// Notification state
const notification = reactive({
  show: false,
  type: 'info',
  title: '',
  message: '',
  showProgress: false,
  progress: null,
  errors: []
})

const allSelected = computed(() => {
  return props.participants.data.length > 0 && selectedParticipants.value.length === props.participants.data.length
})

// Use real-time stats for counts
const verifiedCount = computed(() => stats.value.verified_count)
const rejectedCount = computed(() => stats.value.rejected_count)
const pendingCount = computed(() => stats.value.pending_verification)

const search = debounce(() => {
  router.get(route('admin.participant-verifications.nik-verification'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const clearFilters = () => {
  filters.search = ''
  search()
}

const toggleSelectAll = (checked) => {
  if (checked) {
    selectedParticipants.value = props.participants.data.map(p => p.id_peserta)
  } else {
    selectedParticipants.value = []
  }
}

const toggleParticipantSelection = (participantId) => {
  const index = selectedParticipants.value.indexOf(participantId)
  if (index > -1) {
    selectedParticipants.value.splice(index, 1)
  } else {
    selectedParticipants.value.push(participantId)
  }
}

const verifyNik = (participant, status) => {
  notesDialog.participant = participant
  notesDialog.status = status
  notesDialog.notes = ''
  notesDialog.open = true
}

const submitVerification = () => {
  const participantId = notesDialog.participant.id_peserta
  const participantName = notesDialog.participant.nama_peserta
  const statusText = notesDialog.status === 'verified' ? 'diverifikasi' : 'ditolak'

  // Add optimistic update
  addPendingOperation(participantId, {
    status: notesDialog.status,
    type: 'individual'
  })

  router.post(route('admin.participant-verifications.verify', participantId), {
    verification_type_id: props.nikVerificationType.id_verification_type,
    status: notesDialog.status,
    notes: notesDialog.notes
  }, {
    onSuccess: () => {
      notesDialog.open = false
      refreshStats() // Refresh real-time stats

      // Show success notification
      showNotification('success', 'Verifikasi Berhasil', `NIK ${participantName} berhasil ${statusText}`)
    },
    onError: (errors) => {
      // Remove optimistic update on error
      removePendingOperation(participantId)

      // Show error notification
      showNotification('error', 'Verifikasi Gagal', `Gagal memverifikasi NIK ${participantName}`, Object.values(errors).flat())
    },
    onFinish: () => {
      // Remove optimistic update when done
      setTimeout(() => {
        removePendingOperation(participantId)
      }, 1000)
    }
  })
}

// Helper function to show notifications
const showNotification = (type, title, message, errors = []) => {
  notification.show = true
  notification.type = type
  notification.title = title
  notification.message = message
  notification.showProgress = false
  notification.progress = null
  notification.errors = errors
}

// Enhanced bulk operations
const openBulkDialog = (operationType) => {
  bulkDialog.operationType = operationType
  bulkDialog.open = true
  bulkDialog.errors = []
  bulkDialog.processedCount = 0
  bulkDialog.isProcessing = false
}

const closeBulkDialog = () => {
  if (!bulkDialog.isProcessing) {
    bulkDialog.open = false
    bulkDialog.errors = []
    bulkDialog.processedCount = 0
  }
}

const handleBulkOperation = (data) => {
  bulkDialog.isProcessing = true
  bulkDialog.errors = []
  bulkDialog.processedCount = 0

  const status = data.operationType === 'verify' ? 'verified' : 'rejected'
  const operationText = status === 'verified' ? 'verifikasi' : 'penolakan'

  // Show progress notification
  notification.show = true
  notification.type = 'info'
  notification.title = `Memproses ${operationText} massal...`
  notification.message = `Memproses ${selectedParticipants.value.length} peserta`
  notification.showProgress = true
  notification.progress = 0
  notification.errors = []

  // Add optimistic updates for selected participants
  selectedParticipants.value.forEach(id => {
    addPendingOperation(id, { status, type: 'bulk' })
  })

  router.post(route('admin.participant-verifications.bulk-verify'), {
    participant_ids: selectedParticipants.value,
    verification_type_id: props.nikVerificationType.id_verification_type,
    status: status,
    notes: data.notes || `Bulk ${status === 'verified' ? 'verification' : 'rejection'} NIK`
  }, {
    onSuccess: (page) => {
      // Clear selections and refresh stats
      selectedParticipants.value = []
      refreshStats()

      // Handle bulk result if available
      if (page.props.flash?.bulk_result) {
        const result = page.props.flash.bulk_result
        bulkDialog.processedCount = result.success_count

        // Update notification with results
        notification.type = result.error_count > 0 ? 'warning' : 'success'
        notification.title = `${operationText.charAt(0).toUpperCase() + operationText.slice(1)} massal selesai`
        notification.message = `${result.success_count} peserta berhasil diproses`
        notification.showProgress = false
        notification.progress = 100

        if (result.errors && result.errors.length > 0) {
          bulkDialog.errors = result.errors
          notification.errors = result.errors
          notification.message += `, ${result.error_count} gagal`
        }
      }

      // Close dialog after a short delay to show completion
      setTimeout(() => {
        bulkDialog.isProcessing = false
        bulkDialog.open = false
      }, 1500)
    },
    onError: (errors) => {
      bulkDialog.errors = Object.values(errors).flat()
      bulkDialog.isProcessing = false

      // Update notification with error
      notification.type = 'error'
      notification.title = `Gagal memproses ${operationText} massal`
      notification.message = 'Terjadi kesalahan saat memproses operasi'
      notification.showProgress = false
      notification.errors = Object.values(errors).flat()
    },
    onFinish: () => {
      // Remove optimistic updates
      selectedParticipants.value.forEach(id => {
        removePendingOperation(id)
      })
    }
  })
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('id-ID')
}

const getStatusText = (participant) => {
  if (isPending(participant.id_peserta)) {
    const operation = getPendingOperation(participant.id_peserta)
    if (operation?.status === 'verified') {
      return 'Memverifikasi...'
    } else if (operation?.status === 'rejected') {
      return 'Menolak...'
    }
    return 'Memproses...'
  }
  return 'Menunggu Verifikasi'
}

// Keyboard shortcuts for better UX
const handleKeydown = (event) => {
  // Ctrl/Cmd + A to select all
  if ((event.ctrlKey || event.metaKey) && event.key === 'a' && !event.target.matches('input, textarea')) {
    event.preventDefault()
    toggleSelectAll(true)
  }

  // Escape to clear selections or close dialogs
  if (event.key === 'Escape') {
    if (bulkDialog.open && !bulkDialog.isProcessing) {
      closeBulkDialog()
    } else if (notesDialog.open) {
      notesDialog.open = false
    } else if (selectedParticipants.value.length > 0) {
      selectedParticipants.value = []
    }
  }

  // Ctrl/Cmd + Enter to trigger bulk verify when participants are selected
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter' && selectedParticipants.value.length > 0) {
    event.preventDefault()
    openBulkDialog('verify')
  }
}

// Set up keyboard shortcuts
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
