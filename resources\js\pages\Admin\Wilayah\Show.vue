<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head :title="`Detail Wilayah: ${wilayah.nama_wilayah}`" />
    <Heading :title="`Detail Wilayah: ${wilayah.nama_wilayah}`" />

    <div class="max-w-4xl mx-auto space-y-6">
      <!-- Wilayah Information -->
      <Card>
        <CardHeader>
          <div class="flex justify-between items-start">
            <div>
              <CardTitle class="flex items-center gap-3">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Icon name="map-pin" class="w-6 h-6 text-blue-600" />
                </div>
                {{ wilayah.nama_wilayah }}
              </CardTitle>
              <CardDescription>
                {{ wilayah.kode_wilayah }} • {{ levels[wilayah.level_wilayah] || wilayah.level_wilayah }}
              </CardDescription>
            </div>
            <div class="flex gap-2">
              <Badge :variant="getLevelVariant(wilayah.level_wilayah)">
                {{ levels[wilayah.level_wilayah] || wilayah.level_wilayah }}
              </Badge>
              <Badge :variant="getStatusVariant(wilayah.status)">
                {{ getStatusLabel(wilayah.status) }}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">Kode Wilayah</Label>
                <p class="text-sm">{{ wilayah.kode_wilayah }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Nama Wilayah</Label>
                <p class="text-sm">{{ wilayah.nama_wilayah }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Level Wilayah</Label>
                <p class="text-sm">{{ levels[wilayah.level_wilayah] || wilayah.level_wilayah }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Status</Label>
                <p class="text-sm">{{ getStatusLabel(wilayah.status) }}</p>
              </div>
            </div>
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">Wilayah Induk</Label>
                <p class="text-sm">{{ wilayah.parent?.nama_wilayah || '-' }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Dibuat</Label>
                <p class="text-sm">{{ formatDate(wilayah.created_at) }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Diperbarui</Label>
                <p class="text-sm">{{ formatDate(wilayah.updated_at) }}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Children Wilayah -->
      <Card v-if="wilayah.children && wilayah.children.length > 0">
        <CardHeader>
          <CardTitle>Anak Wilayah</CardTitle>
          <CardDescription>
            Wilayah yang berada di bawah {{ wilayah.nama_wilayah }}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="child in wilayah.children"
              :key="child.id_wilayah"
              class="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
              @click="$inertia.visit(route('admin.wilayah.show', child.id_wilayah))"
            >
              <div class="flex justify-between items-start mb-2">
                <h4 class="font-medium">{{ child.nama_wilayah }}</h4>
                <Badge :variant="getStatusVariant(child.status)" class="text-xs">
                  {{ getStatusLabel(child.status) }}
                </Badge>
              </div>
              <p class="text-sm text-gray-500">{{ child.kode_wilayah }}</p>
              <p class="text-xs text-gray-400 mt-1">{{ levels[child.level_wilayah] || child.level_wilayah }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Users in this Wilayah -->
      <Card v-if="wilayah.users && wilayah.users.length > 0">
        <CardHeader>
          <CardTitle>Pengguna di Wilayah Ini</CardTitle>
          <CardDescription>
            Daftar pengguna yang terdaftar di {{ wilayah.nama_wilayah }}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div
              v-for="user in wilayah.users"
              :key="user.id_user"
              class="flex justify-between items-center p-3 border rounded-lg"
            >
              <div>
                <div class="font-medium">{{ user.nama_lengkap }}</div>
                <div class="text-sm text-gray-500">{{ user.username }} • {{ user.email }}</div>
              </div>
              <div class="flex gap-2">
                <Badge :variant="getRoleVariant(user.role)">
                  {{ getRoleLabel(user.role) }}
                </Badge>
                <Badge :variant="getStatusVariant(user.status)">
                  {{ getStatusLabel(user.status) }}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Peserta in this Wilayah -->
      <Card v-if="wilayah.peserta && wilayah.peserta.length > 0">
        <CardHeader>
          <CardTitle>Peserta dari Wilayah Ini</CardTitle>
          <CardDescription>
            Daftar peserta yang berasal dari {{ wilayah.nama_wilayah }}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div
              v-for="peserta in wilayah.peserta"
              :key="peserta.id_peserta"
              class="flex justify-between items-center p-3 border rounded-lg"
            >
              <div>
                <div class="font-medium">{{ peserta.nama_lengkap }}</div>
                <div class="text-sm text-gray-500">{{ peserta.nik }}</div>
              </div>
              <div class="flex gap-2">
                <Badge :variant="peserta.jenis_kelamin === 'L' ? 'default' : 'secondary'">
                  {{ peserta.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}
                </Badge>
                <Badge :variant="peserta.status_peserta === 'approved' ? 'default' : 'secondary'">
                  {{ peserta.status_peserta }}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between">
        <Button
          variant="outline"
          @click="$inertia.visit(route('admin.wilayah.index'))"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Kembali
        </Button>
        <div class="flex gap-2">
          <Button
            variant="outline"
            @click="$inertia.visit(route('admin.wilayah.create'))"
          >
            <Icon name="plus" class="w-4 h-4 mr-2" />
            Tambah Anak Wilayah
          </Button>
          <Button @click="$inertia.visit(route('admin.wilayah.edit', wilayah.id_wilayah))">
            <Icon name="edit" class="w-4 h-4 mr-2" />
            Edit Wilayah
          </Button>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface Wilayah {
  id_wilayah: number
  kode_wilayah: string
  nama_wilayah: string
  level_wilayah: string
  status: string
  created_at: string
  updated_at: string
  parent?: {
    nama_wilayah: string
  }
  children?: Array<{
    id_wilayah: number
    kode_wilayah: string
    nama_wilayah: string
    level_wilayah: string
    status: string
  }>
  users?: Array<{
    id_user: number
    username: string
    email: string
    nama_lengkap: string
    role: string
    status: string
  }>
  peserta?: Array<{
    id_peserta: number
    nama_lengkap: string
    nik: string
    jenis_kelamin: string
    status_peserta: string
  }>
}

const props = defineProps<{
  wilayah: Wilayah
}>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Wilayah', href: '/admin/wilayah' },
  { title: 'Detail Wilayah', href: `/admin/wilayah/${props.wilayah.id_wilayah}` }
]

const levels: Record<string, string> = {
  provinsi: 'Provinsi',
  kabupaten: 'Kabupaten',
  kota: 'Kota'
}

const getLevelVariant = (level: string) => {
  const variants: Record<string, string> = {
    provinsi: 'destructive',
    kabupaten: 'default',
    kota: 'secondary'
  }
  return variants[level] || 'secondary'
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    aktif: 'default',
    non_aktif: 'secondary'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    aktif: 'Aktif',
    non_aktif: 'Non Aktif'
  }
  return labels[status] || status
}

const getRoleVariant = (role: string) => {
  const variants: Record<string, string> = {
    superadmin: 'destructive',
    admin: 'default',
    admin_daerah: 'secondary',
    dewan_hakim: 'outline',
    peserta: 'secondary'
  }
  return variants[role] || 'secondary'
}

const getRoleLabel = (role: string) => {
  const labels: Record<string, string> = {
    superadmin: 'Super Admin',
    admin: 'Admin',
    admin_daerah: 'Admin Daerah',
    dewan_hakim: 'Dewan Hakim',
    peserta: 'Peserta'
  }
  return labels[role] || role
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>
