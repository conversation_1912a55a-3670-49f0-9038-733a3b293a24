<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CompetitionController;
use App\Http\Controllers\Api\ParticipantController;
use App\Http\Controllers\Api\RegistrationController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\DocumentController;
use App\Http\Controllers\Api\JudgeController;
use App\Http\Controllers\Api\ScoringController;
use App\Http\Controllers\Api\ReportController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public API Routes
Route::prefix('v1')->group(function () {
    // Authentication
    Route::post('/auth/login', [AuthController::class, 'login']);
    Route::post('/auth/register', [AuthController::class, 'register']);
    Route::post('/auth/forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('/auth/reset-password', [AuthController::class, 'resetPassword']);
    
    // Public Competition Information
    Route::get('/competitions', [CompetitionController::class, 'index']);
    Route::get('/competitions/{id}', [CompetitionController::class, 'show']);
    Route::get('/competitions/{id}/categories', [CompetitionController::class, 'categories']);
    Route::get('/categories/{id}/groups', [CompetitionController::class, 'groups']);
    
    // Public Results (if enabled)
    Route::get('/results/public', [ReportController::class, 'publicResults']);
});

// Protected API Routes
Route::prefix('v1')->middleware(['auth:sanctum'])->group(function () {
    // Authentication
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::get('/auth/user', [AuthController::class, 'user']);
    Route::put('/auth/profile', [AuthController::class, 'updateProfile']);
    Route::post('/auth/change-password', [AuthController::class, 'changePassword']);
    
    // Participant Routes
    Route::middleware(['role:peserta'])->prefix('participant')->group(function () {
        Route::get('/dashboard', [ParticipantController::class, 'dashboard']);
        Route::get('/profile', [ParticipantController::class, 'profile']);
        Route::put('/profile', [ParticipantController::class, 'updateProfile']);
        
        // Registration Management
        Route::get('/registrations', [RegistrationController::class, 'index']);
        Route::post('/registrations', [RegistrationController::class, 'store']);
        Route::get('/registrations/{id}', [RegistrationController::class, 'show']);
        Route::put('/registrations/{id}', [RegistrationController::class, 'update']);
        Route::post('/registrations/{id}/submit', [RegistrationController::class, 'submit']);
        Route::delete('/registrations/{id}', [RegistrationController::class, 'destroy']);
        
        // Document Management
        Route::get('/registrations/{id}/documents', [DocumentController::class, 'index']);
        Route::post('/registrations/{id}/documents', [DocumentController::class, 'store']);
        Route::get('/documents/{id}/download', [DocumentController::class, 'download']);
        Route::put('/documents/{id}', [DocumentController::class, 'update']);
        Route::delete('/documents/{id}', [DocumentController::class, 'destroy']);
        
        // Payment Management
        Route::get('/payments', [PaymentController::class, 'index']);
        Route::get('/payments/{registration}', [PaymentController::class, 'show']);
        Route::post('/payments/{registration}/pay', [PaymentController::class, 'pay']);
        Route::post('/payments/{payment}/upload-proof', [PaymentController::class, 'uploadProof']);
    });
    
    // Judge Routes
    Route::middleware(['role:dewan_hakim'])->prefix('judge')->group(function () {
        Route::get('/dashboard', [JudgeController::class, 'dashboard']);
        Route::get('/profile', [JudgeController::class, 'profile']);
        Route::put('/profile', [JudgeController::class, 'updateProfile']);
        
        // Education Management
        Route::get('/profile/education', [JudgeController::class, 'education']);
        Route::post('/profile/education', [JudgeController::class, 'addEducation']);
        Route::put('/profile/education/{id}', [JudgeController::class, 'updateEducation']);
        Route::delete('/profile/education/{id}', [JudgeController::class, 'deleteEducation']);
        
        // Experience Management
        Route::get('/profile/experience', [JudgeController::class, 'experience']);
        Route::post('/profile/experience', [JudgeController::class, 'addExperience']);
        Route::put('/profile/experience/{id}', [JudgeController::class, 'updateExperience']);
        Route::delete('/profile/experience/{id}', [JudgeController::class, 'deleteExperience']);
        
        // Achievement Management
        Route::get('/profile/achievements', [JudgeController::class, 'achievements']);
        Route::post('/profile/achievements', [JudgeController::class, 'addAchievement']);
        Route::put('/profile/achievements/{id}', [JudgeController::class, 'updateAchievement']);
        Route::delete('/profile/achievements/{id}', [JudgeController::class, 'deleteAchievement']);
        
        // Scoring
        Route::get('/assignments', [ScoringController::class, 'assignments']);
        Route::get('/participants/{registration}', [ScoringController::class, 'participant']);
        Route::post('/participants/{registration}/score', [ScoringController::class, 'score']);
        Route::put('/participants/{registration}/score', [ScoringController::class, 'updateScore']);
        Route::get('/scoring-history', [ScoringController::class, 'history']);
    });
    
    // Admin Routes
    Route::middleware(['role:superadmin,admin'])->prefix('admin')->group(function () {
        // Dashboard
        Route::get('/dashboard', [AdminController::class, 'dashboard']);
        
        // User Management
        Route::apiResource('users', UserController::class);
        Route::post('/users/{user}/toggle-status', [UserController::class, 'toggleStatus']);
        Route::post('/users/{user}/reset-password', [UserController::class, 'resetPassword']);
        
        // Regional Management
        Route::apiResource('regions', RegionController::class);
        Route::get('/regions/{region}/children', [RegionController::class, 'children']);
        
        // Competition Management
        Route::apiResource('categories', CategoryController::class);
        Route::apiResource('groups', GroupController::class);
        Route::apiResource('venues', VenueController::class);
        
        // Judge Management
        Route::apiResource('judges', JudgeManagementController::class);
        Route::post('/judges/{judge}/toggle-status', [JudgeManagementController::class, 'toggleStatus']);
        
        // Event Management
        Route::apiResource('events', EventController::class);
        Route::post('/events/{event}/activate', [EventController::class, 'activate']);
        
        // Participant Management
        Route::apiResource('participants', ParticipantManagementController::class);
        Route::post('/participants/{participant}/verify', [ParticipantManagementController::class, 'verify']);
        Route::post('/participants/{participant}/approve', [ParticipantManagementController::class, 'approve']);
        Route::post('/participants/{participant}/reject', [ParticipantManagementController::class, 'reject']);
        
        // Registration Management
        Route::apiResource('registrations', RegistrationManagementController::class);
        Route::post('/registrations/{registration}/verify', [RegistrationManagementController::class, 'verify']);
        Route::post('/registrations/{registration}/approve', [RegistrationManagementController::class, 'approve']);
        Route::post('/registrations/{registration}/reject', [RegistrationManagementController::class, 'reject']);
        
        // Payment Management
        Route::get('/payments', [PaymentManagementController::class, 'index']);
        Route::get('/payments/{payment}', [PaymentManagementController::class, 'show']);
        Route::post('/payments/{payment}/verify', [PaymentManagementController::class, 'verify']);
        Route::post('/payments/{payment}/reject', [PaymentManagementController::class, 'reject']);
        
        // Reports
        Route::get('/reports/participants', [ReportController::class, 'participants']);
        Route::get('/reports/registrations', [ReportController::class, 'registrations']);
        Route::get('/reports/payments', [ReportController::class, 'payments']);
        Route::get('/reports/regions', [ReportController::class, 'regions']);
        Route::get('/reports/export/participants', [ReportController::class, 'exportParticipants']);
        Route::get('/reports/export/registrations', [ReportController::class, 'exportRegistrations']);
    });
    
    // Regional Admin Routes
    Route::middleware(['role:admin_daerah'])->prefix('regional-admin')->group(function () {
        Route::get('/dashboard', [RegionalAdminController::class, 'dashboard']);
        
        // Participant Management (Regional Admin can directly register participants)
        Route::get('/participants', [RegionalParticipantController::class, 'index']);
        Route::post('/participants', [RegionalParticipantController::class, 'store']);
        Route::get('/participants/{participant}', [RegionalParticipantController::class, 'show']);
        Route::put('/participants/{participant}', [RegionalParticipantController::class, 'update']);
        Route::post('/participants/register-direct', [RegionalParticipantController::class, 'registerDirect']);
        Route::post('/participants/{participant}/submit', [RegionalParticipantController::class, 'submit']);
        
        // Registration Management
        Route::get('/registrations', [RegionalRegistrationController::class, 'index']);
        Route::post('/registrations', [RegionalRegistrationController::class, 'store']);
        Route::get('/registrations/{registration}', [RegionalRegistrationController::class, 'show']);
        Route::put('/registrations/{registration}', [RegionalRegistrationController::class, 'update']);
        Route::post('/registrations/{registration}/submit', [RegionalRegistrationController::class, 'submit']);
        
        // Document Management
        Route::post('/participants/{participant}/documents', [RegionalDocumentController::class, 'store']);
        Route::get('/documents/{document}/download', [RegionalDocumentController::class, 'download']);
        Route::put('/documents/{document}', [RegionalDocumentController::class, 'update']);
        Route::delete('/documents/{document}', [RegionalDocumentController::class, 'destroy']);
        
        // Regional Reports
        Route::get('/reports/participants', [RegionalReportController::class, 'participants']);
        Route::get('/reports/registrations', [RegionalReportController::class, 'registrations']);
        Route::get('/reports/export/participants', [RegionalReportController::class, 'exportParticipants']);
    });
});

// Webhook Routes (for payment gateways, etc.)
Route::prefix('webhooks')->group(function () {
    Route::post('/payment/midtrans', [PaymentController::class, 'midtransWebhook']);
    Route::post('/payment/xendit', [PaymentController::class, 'xenditWebhook']);
});
