<?php

namespace Database\Seeders;

use App\Models\CabangLomba;
use App\Models\Golongan;
use App\Models\Peserta;
use App\Models\User;
use App\Models\Wilayah;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MtqDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting MTQ Data Seeding...');

        // Pastikan ada wilayah terlebih dahulu
        if (Wilayah::count() === 0) {
            $this->command->warn('⚠️  No wilayah found. Please run InitialDataSeeder first.');
            return;
        }

        // 1. Create additional admin daerah users
        $this->command->info('👥 Creating admin daerah users...');
        $wilayahList = Wilayah::where('jenis_wilayah', 'kabupaten_kota')->take(10)->get();

        foreach ($wilayahList as $wilayah) {
            User::factory()->adminDaerah()->create([
                'id_wilayah' => $wilayah->id_wilayah,
                'username' => 'admin_' . strtolower(str_replace(' ', '_', $wilayah->nama_wilayah)),
                'email' => 'admin.' . strtolower(str_replace(' ', '.', $wilayah->nama_wilayah)) . '@mtqlampung.id',
                'nama_lengkap' => 'Admin ' . $wilayah->nama_wilayah,
            ]);
        }

        // 2. Create cabang lomba if not exists
        $this->command->info('🏆 Creating cabang lomba...');
        $cabangLombaData = [
            ['kode_cabang' => 'TIL', 'nama_cabang' => 'Tilawatil Quran', 'deskripsi' => 'Seni membaca Al-Quran dengan tartil'],
            ['kode_cabang' => 'TAH', 'nama_cabang' => 'Tahfidzul Quran', 'deskripsi' => 'Hafalan Al-Quran'],
            ['kode_cabang' => 'FAH', 'nama_cabang' => 'Fahmil Quran', 'deskripsi' => 'Pemahaman isi Al-Quran'],
            ['kode_cabang' => 'SYA', 'nama_cabang' => 'Syarhil Quran', 'deskripsi' => 'Tafsir Al-Quran'],
            ['kode_cabang' => 'KAL', 'nama_cabang' => 'Kaligrafi', 'deskripsi' => 'Seni tulis Arab'],
            ['kode_cabang' => 'NAS', 'nama_cabang' => 'Nasyid', 'deskripsi' => 'Seni musik Islami'],
            ['kode_cabang' => 'CER', 'nama_cabang' => 'Ceramah', 'deskripsi' => 'Dakwah dan khutbah'],
            ['kode_cabang' => 'QIS', 'nama_cabang' => 'Qiraatul Kutub', 'deskripsi' => 'Pembacaan kitab kuning'],
        ];

        foreach ($cabangLombaData as $data) {
            CabangLomba::firstOrCreate(
                ['kode_cabang' => $data['kode_cabang']],
                $data + ['status' => 'aktif']
            );
        }

        // 3. Create golongan for each cabang
        $this->command->info('🎯 Creating golongan...');
        $cabangList = CabangLomba::where('status', 'aktif')->get();

        foreach ($cabangList as $cabang) {
            // Anak-anak (10-12 tahun)
            Golongan::firstOrCreate(
                ['kode_golongan' => $cabang->kode_cabang . '-PA'],
                [
                    'nama_golongan' => $cabang->nama_cabang . ' Putra Anak-anak',
                    'id_cabang' => $cabang->id_cabang,
                    'jenis_kelamin' => 'L',
                    'batas_umur_min' => 10,
                    'batas_umur_max' => 12,
                    'biaya_pendaftaran' => 0,
                    'kuota_max' => 50,
                    'status' => 'aktif'
                ]
            );

            Golongan::firstOrCreate(
                ['kode_golongan' => $cabang->kode_cabang . '-PI'],
                [
                    'nama_golongan' => $cabang->nama_cabang . ' Putri Anak-anak',
                    'id_cabang' => $cabang->id_cabang,
                    'jenis_kelamin' => 'P',
                    'batas_umur_min' => 10,
                    'batas_umur_max' => 12,
                    'biaya_pendaftaran' => 0,
                    'kuota_max' => 50,
                    'status' => 'aktif'
                ]
            );

            // Remaja (13-16 tahun)
            Golongan::firstOrCreate(
                ['kode_golongan' => $cabang->kode_cabang . '-PR'],
                [
                    'nama_golongan' => $cabang->nama_cabang . ' Putra Remaja',
                    'id_cabang' => $cabang->id_cabang,
                    'jenis_kelamin' => 'L',
                    'batas_umur_min' => 13,
                    'batas_umur_max' => 16,
                    'biaya_pendaftaran' => 0,
                    'kuota_max' => 50,
                    'status' => 'aktif'
                ]
            );

            Golongan::firstOrCreate(
                ['kode_golongan' => $cabang->kode_cabang . '-PIR'],
                [
                    'nama_golongan' => $cabang->nama_cabang . ' Putri Remaja',
                    'id_cabang' => $cabang->id_cabang,
                    'jenis_kelamin' => 'P',
                    'batas_umur_min' => 13,
                    'batas_umur_max' => 16,
                    'biaya_pendaftaran' => 0,
                    'kuota_max' => 50,
                    'status' => 'aktif'
                ]
            );

            // Dewasa (17-25 tahun)
            Golongan::firstOrCreate(
                ['kode_golongan' => $cabang->kode_cabang . '-PD'],
                [
                    'nama_golongan' => $cabang->nama_cabang . ' Putra Dewasa',
                    'id_cabang' => $cabang->id_cabang,
                    'jenis_kelamin' => 'L',
                    'batas_umur_min' => 17,
                    'batas_umur_max' => 25,
                    'biaya_pendaftaran' => 0,
                    'kuota_max' => 50,
                    'status' => 'aktif'
                ]
            );

            Golongan::firstOrCreate(
                ['kode_golongan' => $cabang->kode_cabang . '-PID'],
                [
                    'nama_golongan' => $cabang->nama_cabang . ' Putri Dewasa',
                    'id_cabang' => $cabang->id_cabang,
                    'jenis_kelamin' => 'P',
                    'batas_umur_min' => 17,
                    'batas_umur_max' => 25,
                    'biaya_pendaftaran' => 0,
                    'kuota_max' => 50,
                    'status' => 'aktif'
                ]
            );
        }

        // 4. Create peserta users and profiles
        $this->command->info('👤 Creating peserta users and profiles...');

        // Create 50 peserta with mandiri registration
        for ($i = 1; $i <= 50; $i++) {
            $user = User::factory()->peserta()->create();
            Peserta::factory()->mandiri()->approved()->create([
                'id_user' => $user->id_user,
                'id_wilayah' => $user->id_wilayah,
                'nama_lengkap' => $user->nama_lengkap,
                'email' => $user->email,
                'no_telepon' => $user->no_telepon,
            ]);
        }

        // Create 30 peserta registered by admin daerah
        $adminDaerahUsers = User::where('role', 'admin_daerah')->get();
        for ($i = 1; $i <= 30; $i++) {
            $adminDaerah = $adminDaerahUsers->random();
            $user = User::factory()->peserta()->create([
                'id_wilayah' => $adminDaerah->id_wilayah,
                'created_by' => $adminDaerah->id_user,
            ]);

            Peserta::factory()->adminDaerahRegistered()->approved()->create([
                'id_user' => $user->id_user,
                'id_wilayah' => $user->id_wilayah,
                'nama_lengkap' => $user->nama_lengkap,
                'email' => $user->email,
                'no_telepon' => $user->no_telepon,
                'registered_by' => $adminDaerah->id_user,
            ]);
        }

        // 5. Create some dewan hakim users
        $this->command->info('⚖️ Creating dewan hakim users...');
        User::factory(10)->dewaHakim()->create();

        $this->command->info('✅ MTQ Data Seeding completed successfully!');
        $this->command->info('📊 Summary:');
        $this->command->info('   - Users: ' . User::count());
        $this->command->info('   - Peserta: ' . Peserta::count());
        $this->command->info('   - Cabang Lomba: ' . CabangLomba::count());
        $this->command->info('   - Golongan: ' . Golongan::count());
    }
}
