<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'

interface CabangLomba {
    nama_cabang: string
}

interface Golongan {
    nama_golongan: string
    biaya_pendaftaran: number
    cabang_lomba: CabangLomba
}

interface Pembayaran {
    status_pembayaran: string
    jumlah_bayar: number
    tanggal_bayar: string | null
}

interface DokumenPeserta {
    jenis_dokumen: string
    status_verifikasi: string
}

interface Pendaftaran {
    id_pendaftaran: number
    nomor_pendaftaran: string
    nomor_peserta: string
    status_pendaftaran: string
    tanggal_daftar: string
    golongan: Golongan
    pembayaran: Pembayaran | null
    dokumen_peserta: DokumenPeserta[]
}

const props = defineProps<{
    pendaftaran: Pendaftaran[]
}>()

function getStatusColor(status: string): string {
    const colors = {
        draft: 'bg-gray-100 text-gray-800',
        submitted: 'bg-blue-100 text-blue-800',
        regional_verified: 'bg-indigo-100 text-indigo-800',
        payment_pending: 'bg-yellow-100 text-yellow-800',
        paid: 'bg-green-100 text-green-800',
        verified: 'bg-purple-100 text-purple-800',
        approved: 'bg-green-100 text-green-800',
        rejected: 'bg-red-100 text-red-800'
    }
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string): string {
    const texts = {
        draft: 'Draft',
        submitted: 'Disubmit',
        regional_verified: 'Verifikasi Regional',
        payment_pending: 'Menunggu Pembayaran',
        paid: 'Sudah Dibayar',
        verified: 'Verifikasi Provinsi',
        approved: 'Disetujui',
        rejected: 'Ditolak'
    }
    return texts[status as keyof typeof texts] || status
}

function getPaymentStatusColor(status: string): string {
    const colors = {
        pending: 'bg-yellow-100 text-yellow-800',
        paid: 'bg-green-100 text-green-800',
        failed: 'bg-red-100 text-red-800',
        expired: 'bg-gray-100 text-gray-800'
    }
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function getPaymentStatusText(status: string): string {
    const texts = {
        pending: 'Menunggu',
        paid: 'Lunas',
        failed: 'Gagal',
        expired: 'Kedaluwarsa'
    }
    return texts[status as keyof typeof texts] || status
}

function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0
    }).format(amount)
}

function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    })
}

function getDocumentProgress(documents: DokumenPeserta[]): { completed: number; total: number } {
    const requiredDocs = ['foto', 'ktp', 'kartu_keluarga']
    const completed = requiredDocs.filter(type =>
        documents.some(doc => doc.jenis_dokumen === type && doc.status_verifikasi === 'approved')
    ).length

    return { completed, total: requiredDocs.length }
}
</script>

<template>
    <AppLayout>

            <div class="flex items-center justify-between">
                <Heading title="Pendaftaran Lomba Saya" />
                <Button as-child>
                    <TextLink :href="route('peserta.pendaftaran.create')">
                        <Icon name="plus" class="w-4 h-4 mr-2" />
                        Daftar Lomba Baru
                    </TextLink>
                </Button>
            </div>


        <Head title="Pendaftaran Lomba Saya" />

        <div class="space-y-6">
            <!-- Empty State -->
            <div v-if="pendaftaran.length === 0" class="text-center py-12">
                <Icon name="clipboard-list" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Pendaftaran</h3>
                <p class="text-gray-600 mb-4">Anda belum mendaftar untuk lomba apapun. Mulai daftar sekarang!</p>
                <Button as-child>
                    <TextLink :href="route('peserta.pendaftaran.create')" class="text-white">
                        <Icon name="plus" class="w-8 h-6 mr-2" />
                        Daftar Lomba Baru
                    </TextLink>
                </Button>
            </div>

            <!-- Registration List -->
            <div v-else class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card v-for="reg in pendaftaran" :key="reg.id_pendaftaran" class="hover:shadow-lg transition-shadow">
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <CardTitle class="text-lg">{{ reg.golongan.nama_golongan }}</CardTitle>
                            <Badge :class="getStatusColor(reg.status_pendaftaran)">
                                {{ getStatusText(reg.status_pendaftaran) }}
                            </Badge>
                        </div>
                        <CardDescription>{{ reg.golongan.cabang_lomba.nama_cabang }}</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div class="space-y-4">
                            <!-- Registration Info -->
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="font-medium text-gray-500">No. Pendaftaran</p>
                                    <p class="font-mono">{{ reg.nomor_pendaftaran }}</p>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-500">No. Peserta</p>
                                    <p class="font-mono">{{ reg.nomor_peserta }}</p>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-500">Tanggal Daftar</p>
                                    <p>{{ formatDate(reg.tanggal_daftar) }}</p>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-500">Biaya</p>
                                    <p class="font-medium text-green-600">{{ formatCurrency(reg.golongan.biaya_pendaftaran) }}</p>
                                </div>
                            </div>

                            <!-- Payment Status -->
                            <div v-if="reg.pembayaran" class="p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium">Status Pembayaran</span>
                                    <Badge :class="getPaymentStatusColor(reg.pembayaran.status_pembayaran)">
                                        {{ getPaymentStatusText(reg.pembayaran.status_pembayaran) }}
                                    </Badge>
                                </div>
                                <div v-if="reg.pembayaran.tanggal_bayar" class="text-xs text-gray-600 mt-1">
                                    Dibayar: {{ formatDate(reg.pembayaran.tanggal_bayar) }}
                                </div>
                            </div>

                            <!-- Document Progress -->
                            <div class="p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium">Dokumen</span>
                                    <span class="text-xs text-gray-600">
                                        {{ getDocumentProgress(reg.dokumen_peserta).completed }}/{{ getDocumentProgress(reg.dokumen_peserta).total }}
                                    </span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div
                                        class="bg-blue-600 h-2 rounded-full transition-all"
                                        :style="{ width: `${(getDocumentProgress(reg.dokumen_peserta).completed / getDocumentProgress(reg.dokumen_peserta).total) * 100}%` }"
                                    ></div>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex space-x-2 pt-2">
                                <Button
                                    as-child
                                    size="sm"
                                    class="flex-1"
                                >
                                    <TextLink :href="route('peserta.pendaftaran.show', reg.id_pendaftaran)">
                                        <Icon name="eye" class="w-4 h-4 mr-2" />
                                        Detail
                                    </TextLink>
                                </Button>

                                <Button
                                    v-if="reg.status_pendaftaran === 'draft'"
                                    as-child
                                    size="sm"
                                    variant="outline"
                                    class="flex-1"
                                >
                                    <TextLink :href="route('peserta.pendaftaran.edit', reg.id_pendaftaran)">
                                        <Icon name="edit" class="w-4 h-4 mr-2" />
                                        Edit
                                    </TextLink>
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Quick Actions -->
            <Card>
                <CardHeader>
                    <CardTitle>Aksi Cepat</CardTitle>
                    <CardDescription>Tindakan yang mungkin Anda perlukan</CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Button as-child variant="outline" class="h-auto p-4">
                            <TextLink :href="route('competition.index')">
                                <div class="text-center">
                                    <Icon name="search" class="h-6 w-6 mx-auto mb-2" />
                                    <div class="font-medium">Jelajahi Lomba</div>
                                    <div class="text-xs text-gray-500">Lihat semua cabang lomba</div>
                                </div>
                            </TextLink>
                        </Button>

                        <Button as-child variant="outline" class="h-auto p-4">
                            <TextLink :href="route('peserta.dashboard')">
                                <div class="text-center">
                                    <Icon name="user" class="h-6 w-6 mx-auto mb-2" />
                                    <div class="font-medium">Profil Saya</div>
                                    <div class="text-xs text-gray-500">Kelola data pribadi</div>
                                </div>
                            </TextLink>
                        </Button>

                        <Button as-child class="h-auto p-4">
                            <TextLink :href="route('peserta.pendaftaran.create')">
                                <div class="text-center">
                                    <Icon name="plus" class="h-6 w-6 mx-auto mb-2" />
                                    <div class="font-medium">Daftar Baru</div>
                                    <div class="text-xs">Daftar lomba lainnya</div>
                                </div>
                            </TextLink>
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
