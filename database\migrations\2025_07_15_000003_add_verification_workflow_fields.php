<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add regional admin verification fields to peserta table
        Schema::table('peserta', function (Blueprint $table) {
            if (!Schema::hasColumn('peserta', 'regional_verified_by')) {
                $table->unsignedBigInteger('regional_verified_by')->nullable()->after('registered_by');
                $table->foreign('regional_verified_by')->references('id_user')->on('users')->onDelete('set null');
            }
            if (!Schema::hasColumn('peserta', 'regional_verified_at')) {
                $table->timestamp('regional_verified_at')->nullable()->after('regional_verified_by');
            }
            if (!Schema::hasColumn('peserta', 'regional_verification_notes')) {
                $table->text('regional_verification_notes')->nullable()->after('regional_verified_at');
            }
            if (!Schema::hasColumn('peserta', 'regional_verification_status')) {
                $table->enum('regional_verification_status', ['pending', 'verified', 'rejected'])->default('pending')->after('regional_verification_notes');
            }
            if (!Schema::hasColumn('peserta', 'documents_complete')) {
                $table->boolean('documents_complete')->default(false)->after('regional_verification_status');
            }
            if (!Schema::hasColumn('peserta', 'documents_completed_at')) {
                $table->timestamp('documents_completed_at')->nullable()->after('documents_complete');
            }
        });

        // Add regional admin verification fields to pendaftaran table
        Schema::table('pendaftaran', function (Blueprint $table) {
            if (!Schema::hasColumn('pendaftaran', 'regional_verified_by')) {
                $table->unsignedBigInteger('regional_verified_by')->nullable()->after('approved_at');
                $table->foreign('regional_verified_by')->references('id_user')->on('users')->onDelete('set null');
            }
            if (!Schema::hasColumn('pendaftaran', 'regional_verified_at')) {
                $table->timestamp('regional_verified_at')->nullable()->after('regional_verified_by');
            }
            if (!Schema::hasColumn('pendaftaran', 'regional_verification_notes')) {
                $table->text('regional_verification_notes')->nullable()->after('regional_verified_at');
            }
            if (!Schema::hasColumn('pendaftaran', 'regional_verification_status')) {
                $table->enum('regional_verification_status', ['pending', 'verified', 'rejected'])->default('pending')->after('regional_verification_notes');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('peserta', function (Blueprint $table) {
            if (Schema::hasColumn('peserta', 'regional_verified_by')) {
                $table->dropForeign(['regional_verified_by']);
                $table->dropColumn('regional_verified_by');
            }
            if (Schema::hasColumn('peserta', 'regional_verified_at')) {
                $table->dropColumn('regional_verified_at');
            }
            if (Schema::hasColumn('peserta', 'regional_verification_notes')) {
                $table->dropColumn('regional_verification_notes');
            }
            if (Schema::hasColumn('peserta', 'regional_verification_status')) {
                $table->dropColumn('regional_verification_status');
            }
            if (Schema::hasColumn('peserta', 'documents_complete')) {
                $table->dropColumn('documents_complete');
            }
            if (Schema::hasColumn('peserta', 'documents_completed_at')) {
                $table->dropColumn('documents_completed_at');
            }
        });

        Schema::table('pendaftaran', function (Blueprint $table) {
            if (Schema::hasColumn('pendaftaran', 'regional_verified_by')) {
                $table->dropForeign(['regional_verified_by']);
                $table->dropColumn('regional_verified_by');
            }
            if (Schema::hasColumn('pendaftaran', 'regional_verified_at')) {
                $table->dropColumn('regional_verified_at');
            }
            if (Schema::hasColumn('pendaftaran', 'regional_verification_notes')) {
                $table->dropColumn('regional_verification_notes');
            }
            if (Schema::hasColumn('pendaftaran', 'regional_verification_status')) {
                $table->dropColumn('regional_verification_status');
            }
        });
    }
};
