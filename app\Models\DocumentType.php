<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class DocumentType extends Model
{
    protected $table = 'document_types';
    protected $primaryKey = 'id_document_type';

    protected $fillable = [
        'name',
        'code',
        'description',
        'allowed_file_types',
        'max_file_size',
        'is_required_default',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'allowed_file_types' => 'array',
        'max_file_size' => 'integer',
        'is_required_default' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    // Relationships
    public function golonganRequirements(): HasMany
    {
        return $this->hasMany(GolonganDocumentRequirement::class, 'document_type_id', 'id_document_type');
    }

    public function golongan(): BelongsToMany
    {
        return $this->belongsToMany(Golongan::class, 'golongan_document_requirements', 'document_type_id', 'id_golongan')
                    ->withPivot('is_required')
                    ->withTimestamps();
    }

    public function dokumenPeserta(): HasMany
    {
        return $this->hasMany(DokumenPeserta::class, 'document_type_id', 'id_document_type');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Helper methods
    public function isFileTypeAllowed(string $extension): bool
    {
        return in_array(strtolower($extension), $this->allowed_file_types);
    }

    public function getMaxFileSizeInBytes(): int
    {
        return $this->max_file_size * 1024; // Convert KB to bytes
    }
}
