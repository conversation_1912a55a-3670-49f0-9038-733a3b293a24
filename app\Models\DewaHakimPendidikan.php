<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DewaHakimPendidikan extends Model
{
    protected $table = 'dewan_hakim_pendidikan';
    protected $primaryKey = 'id_pendidikan';

    public $timestamps = false;

    protected $fillable = [
        'id_dewan_hakim',
        'jenjang',
        'instansi',
        'jurusan',
        'tahun_mulai',
        'tahun_lulus'
    ];

    protected $casts = [
        'tahun_mulai' => 'integer',
        'tahun_lulus' => 'integer'
    ];

    // Relationships
    public function dewaHakim(): BelongsTo
    {
        return $this->belongsTo(DewaHakim::class, 'id_dewan_hakim', 'id_dewan_hakim');
    }

    // Helper methods
    public function getDurationAttribute(): int
    {
        return $this->tahun_lulus - $this->tahun_mulai;
    }

    public function getFormattedPeriodAttribute(): string
    {
        return "{$this->tahun_mulai} - {$this->tahun_lulus}";
    }
}
