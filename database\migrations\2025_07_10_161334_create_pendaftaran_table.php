<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pendaftaran', function (Blueprint $table) {
            $table->id('id_pendaftaran');
            $table->unsignedBigInteger('id_peserta');
            $table->unsignedBigInteger('id_golongan');
            $table->unsignedBigInteger('id_mimbar')->nullable();
            $table->string('nomor_pendaftaran', 20)->unique();
            $table->string('nomor_peserta', 12)->unique();
            $table->year('tahun_pendaftaran');
            $table->enum('status_pendaftaran', [
                'draft', 'submitted', 'payment_pending', 'paid', 'verified', 'approved', 'rejected'
            ])->default('draft');
            $table->timestamp('tanggal_daftar')->useCurrent();

            // Approval workflow
            $table->unsignedBigInteger('verified_by')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->text('catatan_verifikasi')->nullable();
            $table->text('catatan_approval')->nullable();

            // Informasi tambahan
            $table->text('keterangan')->nullable();
            $table->timestamps();

            // Foreign keys
            $table->foreign('id_peserta')->references('id_peserta')->on('peserta')->onDelete('cascade');
            $table->foreign('id_golongan')->references('id_golongan')->on('golongan')->onDelete('restrict');
            $table->foreign('id_mimbar')->references('id_mimbar')->on('mimbar')->onDelete('set null');
            $table->foreign('verified_by')->references('id_user')->on('users')->onDelete('set null');
            $table->foreign('approved_by')->references('id_user')->on('users')->onDelete('set null');

            // Unique constraint
            $table->unique(['id_peserta', 'id_golongan', 'tahun_pendaftaran'], 'unique_peserta_golongan_tahun');

            // Indexes
            $table->index(['nomor_pendaftaran']);
            $table->index(['nomor_peserta']);
            $table->index(['status_pendaftaran']);
            $table->index(['tahun_pendaftaran']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pendaftaran');
    }
};
