<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PaymentDisabledMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if this is a payment-related request
        if ($this->isPaymentRequest($request)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment functionality is temporarily disabled. Registration is currently free.',
                    'error' => 'PAYMENT_DISABLED'
                ], 503);
            }

            return redirect()->back()->with('error', 'Payment functionality is temporarily disabled. Registration is currently free.');
        }

        return $next($request);
    }

    /**
     * Check if the request is payment-related
     */
    private function isPaymentRequest(Request $request): bool
    {
        $uri = $request->getRequestUri();
        
        // Check for payment-related URIs
        $paymentPatterns = [
            '/pembayaran',
            '/payments',
            '/payment',
            '/pay',
            '/upload-proof',
            '/midtrans',
            '/xendit'
        ];

        foreach ($paymentPatterns as $pattern) {
            if (str_contains($uri, $pattern)) {
                return true;
            }
        }

        return false;
    }
}
