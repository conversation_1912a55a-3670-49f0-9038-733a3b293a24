<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ParticipantVerification extends Model
{
    protected $table = 'participant_verifications';
    protected $primaryKey = 'id_verification';

    protected $fillable = [
        'id_peserta',
        'verification_type_id',
        'status',
        'verified_by',
        'verified_at',
        'notes',
        'verification_data'
    ];

    protected $casts = [
        'status' => 'string',
        'verified_at' => 'datetime',
        'verification_data' => 'array'
    ];

    // Relationships
    public function peserta(): BelongsTo
    {
        return $this->belongsTo(Peserta::class, 'id_peserta', 'id_peserta');
    }

    public function verificationType(): BelongsTo
    {
        return $this->belongsTo(VerificationType::class, 'verification_type_id', 'id_verification_type');
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by', 'id_user');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeVerified($query)
    {
        return $query->where('status', 'verified');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopeForParticipant($query, $participantId)
    {
        return $query->where('id_peserta', $participantId);
    }

    public function scopeForVerificationType($query, $verificationTypeId)
    {
        return $query->where('verification_type_id', $verificationTypeId);
    }

    // Helper methods
    public function isVerified(): bool
    {
        return $this->status === 'verified';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    public function getVerificationData(string $key, $default = null)
    {
        return $this->verification_data[$key] ?? $default;
    }

    public function setVerificationData(string $key, $value): void
    {
        $data = $this->verification_data ?? [];
        $data[$key] = $value;
        $this->verification_data = $data;
    }
}
