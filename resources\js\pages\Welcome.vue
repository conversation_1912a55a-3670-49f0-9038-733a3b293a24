<script setup lang="ts">
import { Head, <PERSON> } from '@inertiajs/vue3';
import { ref, onMounted } from 'vue';

interface CabangLomba {
    id_cabang: number
    nama_cabang: string
    deskripsi: string
    icon: string
}

interface Golongan {
    id_golongan: number
    nama_golongan: string
    deskripsi: string
    cabang_lomba: CabangLomba
}

const props = defineProps<{
    cabangLomba?: CabangLomba[]
    golongan?: Golongan[]
}>()

const currentYear = new Date().getFullYear()
const eventYear = 2025
const eventNumber = 52

// Animation states
const isVisible = ref(false)

onMounted(() => {
    setTimeout(() => {
        isVisible.value = true
    }, 100)
})
</script>

<template>
    <!-- Main Container -->
    <div class="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-emerald-100"
    :class="{ 'opacity-100 translate-y-0': isVisible, 'opacity-0 translate-y-4': !isVisible }"
        style="transition: all 0.8s ease-out;">
        <Head title="MTQ Lampung ke-52">
            <link rel="preconnect" href="https://fonts.googleapis.com" />
            <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
            <link
                href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Inter:wght@300;400;500;600;700&display=swap"
                rel="stylesheet" />
        </Head>

        <!-- Navigation Header -->
        <nav class="relative z-50 bg-white/90 backdrop-blur-md shadow-sm border-b border-emerald-100">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo -->
                    <div class="flex items-center space-x-3">
                        <div
                            class="w-10 h-10 bg-gradient-to-br from-emerald-600 to-emerald-700 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2L2 7v10c0 5.55 3.84 9.739 9 11 5.16-1.261 9-5.45 9-11V7l-10-5z" />
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900 font-amiri">MTQ Lampung</h1>
                            <p class="text-xs text-emerald-600 font-medium">Ke-{{ eventNumber }} • {{ eventYear }}</p>
                        </div>
                    </div>

                    <!-- Navigation Links -->
                    <div class="hidden md:flex items-center space-x-8">
                        <a href="#beranda"
                            class="text-gray-700 hover:text-emerald-600 font-medium transition-colors">Beranda</a>
                        <a href="#tentang"
                            class="text-gray-700 hover:text-emerald-600 font-medium transition-colors">Tentang</a>
                        <a href="#cabang-lomba"
                            class="text-gray-700 hover:text-emerald-600 font-medium transition-colors">Cabang Lomba</a>
                        <a href="#pendaftaran"
                            class="text-gray-700 hover:text-emerald-600 font-medium transition-colors">Pendaftaran</a>
                    </div>

                    <!-- Auth Links -->
                    <div class="flex items-center space-x-4">
                        <Link v-if="$page.props.auth.user" :href="route('dashboard')"
                            class="bg-emerald-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-emerald-700 transition-colors">
                        Dashboard
                        </Link>
                        <template v-else>
                            <Link :href="route('login')"
                                class="text-gray-700 hover:text-emerald-600 font-medium transition-colors">
                            Masuk
                            </Link>
                            <Link :href="route('register')"
                                class="bg-emerald-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-emerald-700 transition-colors">
                            Daftar
                            </Link>
                        </template>
                    </div>
                </div>
            </div>
        </nav>
        <!-- Hero Section -->
        <section id="beranda" class="relative overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-5">
                <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <pattern id="islamic-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                            <polygon points="10,1 4,7 4,13 10,19 16,13 16,7" fill="currentColor" />
                        </pattern>
                    </defs>
                    <rect width="100" height="100" fill="url(#islamic-pattern)" class="text-emerald-600" />
                </svg>
            </div>

            <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <!-- Hero Content -->
                    <div class="text-center lg:text-left">
                        <div class="mb-6">
                            <span
                                class="inline-block bg-emerald-100 text-emerald-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
                                Musabaqah Tilawatil Quran
                            </span>
                            <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 mb-4 font-amiri leading-tight">
                                MTQ Lampung<br>
                                <span class="text-emerald-600">Ke-{{ eventNumber }}</span>
                            </h1>
                            <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                                Kompetisi tilawah Al-Quran terbesar di Provinsi Lampung.
                                Bergabunglah dengan ribuan peserta dari seluruh kabupaten/kota.
                            </p>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                            <Link v-if="!$page.props.auth.user" :href="route('register')"
                                class="bg-emerald-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-emerald-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                            Daftar Sekarang
                            </Link>
                            <a href="#cabang-lomba"
                                class="border-2 border-emerald-600 text-emerald-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-emerald-50 transition-all duration-200">
                                Lihat Cabang Lomba
                            </a>
                        </div>

                        <!-- Stats -->
                        <div class="grid grid-cols-3 gap-8 mt-12 pt-8 border-t border-gray-200">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-emerald-600 font-amiri">{{ eventNumber }}</div>
                                <div class="text-sm text-gray-600 font-medium">Tahun Ke</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-emerald-600 font-amiri">15+</div>
                                <div class="text-sm text-gray-600 font-medium">Kabupaten/Kota</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-emerald-600 font-amiri">1000+</div>
                                <div class="text-sm text-gray-600 font-medium">Peserta</div>
                            </div>
                        </div>
                    </div>

                    <!-- Hero Image -->
                    <div class="relative">
                        <div class="relative z-10 bg-white rounded-2xl shadow-2xl overflow-hidden">
                            <div class="aspect-w-4 aspect-h-3">
                                <img src="/images/mtq-hero.jpg" alt="MTQ Lampung" class="w-full h-96 object-cover"
                                    onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMDAgMTAwTDE1MCAyMDBIMjUwTDIwMCAxMDBaIiBmaWxsPSIjMTA5OTY4Ii8+CjxjaXJjbGUgY3g9IjIwMCIgY3k9IjE1MCIgcj0iNDAiIGZpbGw9IiNGRkZGRkYiLz4KPHN2Zz4K'" />
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-t from-emerald-900/20 to-transparent"></div>
                            <div class="absolute bottom-4 left-4 right-4">
                                <div class="bg-white/90 backdrop-blur-sm rounded-lg p-4">
                                    <h3 class="font-semibold text-gray-900 mb-1">Tilawah Al-Quran</h3>
                                    <p class="text-sm text-gray-600">Kompetisi membaca Al-Quran dengan tartil dan tajwid
                                        yang
                                        benar</p>
                                </div>
                            </div>
                        </div>

                        <!-- Floating Elements -->
                        <div
                            class="absolute -top-4 -right-4 w-24 h-24 bg-emerald-200 rounded-full opacity-60 animate-pulse">
                        </div>
                        <div class="absolute -bottom-6 -left-6 w-32 h-32 bg-emerald-100 rounded-full opacity-40 animate-pulse"
                            style="animation-delay: 1s;"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="tentang" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4 font-amiri">
                        Tentang MTQ Lampung
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Musabaqah Tilawatil Quran (MTQ) Lampung merupakan ajang kompetisi bergengsi
                        yang telah berlangsung selama {{ eventNumber }} tahun, mempertemukan para qari dan qariah
                        terbaik.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center p-6 rounded-xl bg-emerald-50 border border-emerald-100">
                        <div
                            class="w-16 h-16 bg-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Prestasi Terbaik</h3>
                        <p class="text-gray-600">
                            Menghasilkan qari dan qariah berprestasi yang mewakili Lampung di tingkat nasional
                        </p>
                    </div>

                    <div class="text-center p-6 rounded-xl bg-emerald-50 border border-emerald-100">
                        <div
                            class="w-16 h-16 bg-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2 1l-3 4v7h2v7h3v-7h2v7h2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Pembinaan Berkelanjutan</h3>
                        <p class="text-gray-600">
                            Program pembinaan dan pelatihan untuk meningkatkan kualitas bacaan Al-Quran
                        </p>
                    </div>

                    <div class="text-center p-6 rounded-xl bg-emerald-50 border border-emerald-100">
                        <div
                            class="w-16 h-16 bg-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Standar Internasional</h3>
                        <p class="text-gray-600">
                            Menggunakan standar penilaian internasional dengan dewan hakim berpengalaman
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Cabang Lomba Section -->
        <section id="cabang-lomba" class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4 font-amiri">
                        Cabang Lomba
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Berbagai kategori lomba tilawah Al-Quran untuk semua kalangan dan tingkat kemampuan
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Tilawah -->
                    <div
                        class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                        <div
                            class="h-48 bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center">
                            <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Tilawah</h3>
                            <p class="text-gray-600 mb-4">
                                Lomba membaca Al-Quran dengan tartil, tajwid yang benar, dan lagu yang indah
                            </p>
                            <div class="space-y-2">
                                <div class="flex items-center text-sm text-gray-500">
                                    <span class="w-2 h-2 bg-emerald-500 rounded-full mr-2"></span>
                                    Anak-anak (6-12 tahun)
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <span class="w-2 h-2 bg-emerald-500 rounded-full mr-2"></span>
                                    Remaja (13-17 tahun)
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <span class="w-2 h-2 bg-emerald-500 rounded-full mr-2"></span>
                                    Dewasa (18+ tahun)
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tahfidz -->
                    <div
                        class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                        <div class="h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                            <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Tahfidz</h3>
                            <p class="text-gray-600 mb-4">
                                Lomba menghafal Al-Quran dengan berbagai kategori jumlah hafalan
                            </p>
                            <div class="space-y-2">
                                <div class="flex items-center text-sm text-gray-500">
                                    <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                                    1 Juz
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                                    5 Juz
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                                    10 Juz
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                                    30 Juz
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Fahmil Quran -->
                    <div
                        class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                        <div
                            class="h-48 bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
                            <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">Fahmil Quran</h3>
                            <p class="text-gray-600 mb-4">
                                Lomba pemahaman dan penguasaan isi kandungan Al-Quran
                            </p>
                            <div class="space-y-2">
                                <div class="flex items-center text-sm text-gray-500">
                                    <span class="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                                    Tafsir Al-Quran
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <span class="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                                    Asbabun Nuzul
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <span class="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                                    Makna dan Hikmah
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Pendaftaran Section -->
        <section id="pendaftaran" class="py-20 bg-emerald-600">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 class="text-3xl lg:text-4xl font-bold text-white mb-4 font-amiri">
                    Siap Bergabung?
                </h2>
                <p class="text-xl text-emerald-100 mb-8 max-w-2xl mx-auto">
                    Daftarkan diri Anda sekarang dan jadilah bagian dari MTQ Lampung ke-{{ eventNumber }}.
                    Tunjukkan kemampuan terbaik Anda dalam membaca Al-Quran.
                </p>

                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <Link v-if="!$page.props.auth.user" :href="route('register')"
                        class="bg-white text-emerald-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transform hover:scale-105 transition-all duration-200 shadow-lg">
                    Daftar Sebagai Peserta
                    </Link>
                    <Link v-else :href="route('dashboard')"
                        class="bg-white text-emerald-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transform hover:scale-105 transition-all duration-200 shadow-lg">
                    Masuk ke Dashboard
                    </Link>
                    <a href="#cabang-lomba"
                        class="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white hover:text-emerald-600 transition-all duration-200">
                        Pelajari Lebih Lanjut
                    </a>
                </div>

                <!-- Contact Info -->
                <div class="mt-12 pt-8 border-t border-emerald-500">
                    <p class="text-emerald-100 mb-4">Butuh bantuan? Hubungi kami:</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center text-white">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
                            </svg>
                            <EMAIL>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z" />
                            </svg>
                            (0721) 123-4567
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="bg-gray-900 text-white py-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <!-- Logo & Description -->
                    <div class="md:col-span-2">
                        <div class="flex items-center space-x-3 mb-4">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-emerald-600 to-emerald-700 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2L2 7v10c0 5.55 3.84 9.739 9 11 5.16-1.261 9-5.45 9-11V7l-10-5z" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold font-amiri">MTQ Lampung</h3>
                                <p class="text-sm text-gray-400">Ke-{{ eventNumber }} • {{ eventYear }}</p>
                            </div>
                        </div>
                        <p class="text-gray-400 mb-4 max-w-md">
                            Musabaqah Tilawatil Quran Provinsi Lampung, ajang bergengsi untuk para qari dan qariah
                            terbaik di
                            seluruh Lampung.
                        </p>
                        <div class="flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-emerald-400 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                                </svg>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-emerald-400 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z" />
                                </svg>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-emerald-400 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z" />
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Menu</h4>
                        <ul class="space-y-2">
                            <li><a href="#beranda"
                                    class="text-gray-400 hover:text-emerald-400 transition-colors">Beranda</a>
                            </li>
                            <li><a href="#tentang"
                                    class="text-gray-400 hover:text-emerald-400 transition-colors">Tentang</a>
                            </li>
                            <li><a href="#cabang-lomba"
                                    class="text-gray-400 hover:text-emerald-400 transition-colors">Cabang
                                    Lomba</a></li>
                            <li><a href="#pendaftaran"
                                    class="text-gray-400 hover:text-emerald-400 transition-colors">Pendaftaran</a></li>
                        </ul>
                    </div>

                    <!-- Contact -->
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Kontak</h4>
                        <ul class="space-y-2 text-gray-400">
                            <li class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
                                </svg>
                                Bandar Lampung, Lampung
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z" />
                                </svg>
                                (0721) 123-4567
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path
                                        d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
                                </svg>
                                <EMAIL>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                    <p class="text-gray-400">
                        © {{ currentYear }} MTQ Lampung. Semua hak cipta dilindungi.
                        <span class="text-emerald-400">Bismillahirrahmanirrahim</span>
                    </p>
                </div>
            </div>
        </footer>
    </div>
</template>

<style scoped>
.font-amiri {
    font-family: 'Amiri', serif;
}

/* Smooth scrolling for anchor links */
html {
    scroll-behavior: smooth;
}

/* Animation for floating elements */
@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-10px);
    }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Custom gradient backgrounds */
.bg-islamic-pattern {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
}
</style>
