<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Pelaksanaan;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class PelaksanaanController extends Controller
{
    /**
     * Display a listing of pelaksanaan.
     */
    public function index(Request $request)
    {
        $query = Pelaksanaan::query()
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('tema', 'like', "%{$search}%")
                      ->orWhere('tempat', 'like', "%{$search}%")
                      ->orWhere('tahun', 'like', "%{$search}%");
                });
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->tahun, function ($query, $tahun) {
                $query->where('tahun', $tahun);
            });

        $pelaksanaan = $query->latest('tahun')->paginate(15)->withQueryString();

        // Get available years for filter
        $availableYears = Pelaksanaan::distinct()
            ->orderBy('tahun', 'desc')
            ->pluck('tahun')
            ->toArray();

        return Inertia::render('Admin/Pelaksanaan/Index', [
            'pelaksanaan' => $pelaksanaan,
            'filters' => $request->only(['search', 'status', 'tahun']),
            'availableYears' => $availableYears
        ]);
    }

    /**
     * Show the form for creating a new pelaksanaan.
     */
    public function create()
    {
        return Inertia::render('Admin/Pelaksanaan/Create');
    }

    /**
     * Store a newly created pelaksanaan in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'tahun' => 'required|integer|min:2020|max:2050|unique:pelaksanaan',
            'tema' => 'required|string|max:255',
            'tempat' => 'required|string|max:255',
            'tanggal_mulai' => 'required|date|after_or_equal:today',
            'tanggal_selesai' => 'required|date|after_or_equal:tanggal_mulai',
            'tanggal_buka_pendaftaran' => 'required|date|before_or_equal:tanggal_mulai',
            'tanggal_tutup_pendaftaran' => 'required|date|after_or_equal:tanggal_buka_pendaftaran|before_or_equal:tanggal_mulai',
            'status' => ['required', Rule::in(['draft', 'aktif', 'selesai'])]
        ]);

        Pelaksanaan::create($validated);

        return redirect()->route('admin.pelaksanaan.index')
            ->with('success', 'Pelaksanaan MTQ berhasil dibuat.');
    }

    /**
     * Display the specified pelaksanaan.
     */
    public function show(Pelaksanaan $pelaksanaan)
    {
        // Load related data for statistics
        $pelaksanaan->load([
            'pendaftaran.peserta',
            'pendaftaran.golongan.cabangLomba'
        ]);

        return Inertia::render('Admin/Pelaksanaan/Show', [
            'pelaksanaan' => $pelaksanaan
        ]);
    }

    /**
     * Show the form for editing the specified pelaksanaan.
     */
    public function edit(Pelaksanaan $pelaksanaan)
    {
        return Inertia::render('Admin/Pelaksanaan/Edit', [
            'pelaksanaan' => $pelaksanaan
        ]);
    }

    /**
     * Update the specified pelaksanaan in storage.
     */
    public function update(Request $request, Pelaksanaan $pelaksanaan)
    {
        $validated = $request->validate([
            'tahun' => ['required', 'integer', 'min:2020', 'max:2050', Rule::unique('pelaksanaan')->ignore($pelaksanaan->id_pelaksanaan, 'id_pelaksanaan')],
            'tema' => 'required|string|max:255',
            'tempat' => 'required|string|max:255',
            'tanggal_mulai' => 'required|date',
            'tanggal_selesai' => 'required|date|after_or_equal:tanggal_mulai',
            'tanggal_buka_pendaftaran' => 'required|date|before_or_equal:tanggal_mulai',
            'tanggal_tutup_pendaftaran' => 'required|date|after_or_equal:tanggal_buka_pendaftaran|before_or_equal:tanggal_mulai',
            'status' => ['required', Rule::in(['draft', 'aktif', 'selesai'])]
        ]);

        $pelaksanaan->update($validated);

        return redirect()->route('admin.pelaksanaan.index')
            ->with('success', 'Pelaksanaan MTQ berhasil diperbarui.');
    }

    /**
     * Remove the specified pelaksanaan from storage.
     */
    public function destroy(Pelaksanaan $pelaksanaan)
    {
        // Check if pelaksanaan has pendaftaran
        if ($pelaksanaan->pendaftaran()->count() > 0) {
            return back()->with('error', 'Pelaksanaan tidak dapat dihapus karena memiliki pendaftaran.');
        }

        $pelaksanaan->delete();

        return redirect()->route('admin.pelaksanaan.index')
            ->with('success', 'Pelaksanaan MTQ berhasil dihapus.');
    }

    /**
     * Activate pelaksanaan (set as active and deactivate others).
     */
    public function activate(Pelaksanaan $pelaksanaan)
    {
        // Deactivate all other pelaksanaan
        Pelaksanaan::where('id_pelaksanaan', '!=', $pelaksanaan->id_pelaksanaan)
            ->update(['status' => 'draft']);

        // Activate this pelaksanaan
        $pelaksanaan->update(['status' => 'aktif']);

        return back()->with('success', "Pelaksanaan MTQ {$pelaksanaan->tahun} berhasil diaktifkan.");
    }

    /**
     * Get statistics for pelaksanaan.
     */
    public function statistics(Pelaksanaan $pelaksanaan)
    {
        $stats = [
            'total_pendaftaran' => $pelaksanaan->pendaftaran()->count(),
            'pendaftaran_approved' => $pelaksanaan->pendaftaran()->where('status_pendaftaran', 'approved')->count(),
            'pendaftaran_pending' => $pelaksanaan->pendaftaran()->where('status_pendaftaran', 'pending')->count(),
            'total_peserta_laki' => $pelaksanaan->pendaftaran()
                ->whereHas('golongan', function ($q) {
                    $q->where('jenis_kelamin', 'L');
                })->count(),
            'total_peserta_perempuan' => $pelaksanaan->pendaftaran()
                ->whereHas('golongan', function ($q) {
                    $q->where('jenis_kelamin', 'P');
                })->count(),
            'cabang_lomba' => $pelaksanaan->pendaftaran()
                ->with('golongan.cabangLomba')
                ->get()
                ->groupBy('golongan.cabang_lomba.nama_cabang')
                ->map(function ($group) {
                    return $group->count();
                }),
            'wilayah' => $pelaksanaan->pendaftaran()
                ->with('peserta.wilayah')
                ->get()
                ->groupBy('peserta.wilayah.nama_wilayah')
                ->map(function ($group) {
                    return $group->count();
                })
        ];

        return response()->json($stats);
    }

    /**
     * Export participants data.
     */
    public function exportParticipants(Pelaksanaan $pelaksanaan)
    {
        // This would typically generate an Excel/CSV export
        // For now, return JSON data
        $participants = $pelaksanaan->pendaftaran()
            ->with(['peserta', 'golongan.cabangLomba', 'mimbar'])
            ->where('status_pendaftaran', 'approved')
            ->get();

        return response()->json($participants);
    }
}
