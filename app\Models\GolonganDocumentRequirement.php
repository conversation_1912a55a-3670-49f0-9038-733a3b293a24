<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GolonganDocumentRequirement extends Model
{
    protected $table = 'golongan_document_requirements';
    protected $primaryKey = 'id_requirement';

    protected $fillable = [
        'id_golongan',
        'document_type_id',
        'is_required'
    ];

    protected $casts = [
        'is_required' => 'boolean'
    ];

    // Relationships
    public function golongan(): BelongsTo
    {
        return $this->belongsTo(Golongan::class, 'id_golongan', 'id_golongan');
    }

    public function documentType(): BelongsTo
    {
        return $this->belongsTo(DocumentType::class, 'document_type_id', 'id_document_type');
    }

    // Scopes
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    public function scopeOptional($query)
    {
        return $query->where('is_required', false);
    }

    public function scopeForGolongan($query, $golonganId)
    {
        return $query->where('id_golongan', $golonganId);
    }
}
