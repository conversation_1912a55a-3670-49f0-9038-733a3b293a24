<template>
  <div class="space-y-3">
    <!-- Overall Progress -->
    <div v-if="showOverall">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium">{{ overallLabel }}</span>
        <span class="text-sm text-gray-500">{{ overallPercentage }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          class="h-2 rounded-full transition-all duration-500"
          :class="overallProgressClass"
          :style="{ width: `${overallPercentage}%` }"
        ></div>
      </div>
    </div>

    <!-- Individual Progress Items -->
    <div v-if="showDetails" class="space-y-2">
      <div
        v-for="(item, index) in progressItems"
        :key="index"
        class="flex items-center justify-between"
      >
        <div class="flex items-center space-x-2 flex-1">
          <Icon
            :name="item.icon"
            class="w-4 h-4 flex-shrink-0"
            :class="getItemIconClass(item.status)"
          />
          <span class="text-sm truncate">{{ item.label }}</span>
        </div>

        <div class="flex items-center space-x-2">
          <!-- Progress Bar for items with progress -->
          <div v-if="item.progress !== undefined" class="w-16">
            <div class="w-full bg-gray-200 rounded-full h-1">
              <div
                class="h-1 rounded-full transition-all duration-300"
                :class="getItemProgressClass(item.status)"
                :style="{ width: `${item.progress}%` }"
              ></div>
            </div>
          </div>

          <!-- Status Badge -->
          <Badge
            :variant="getItemBadgeVariant(item.status)"
            class="text-xs"
          >
            {{ getItemStatusLabel(item.status) }}
          </Badge>
        </div>
      </div>
    </div>

    <!-- Summary Stats -->
    <div v-if="showSummary" class="grid grid-cols-3 gap-2 pt-2 border-t">
      <div class="text-center">
        <p class="text-lg font-semibold text-green-600">{{ summary.completed }}</p>
        <p class="text-xs text-green-600">Selesai</p>
      </div>
      <div class="text-center">
        <p class="text-lg font-semibold text-yellow-600">{{ summary.pending }}</p>
        <p class="text-xs text-yellow-600">Pending</p>
      </div>
      <div class="text-center">
        <p class="text-lg font-semibold text-red-600">{{ summary.failed }}</p>
        <p class="text-xs text-red-600">Gagal</p>
      </div>
    </div>

    <!-- Quick Actions -->
    <div v-if="quickActions.length > 0" class="flex flex-wrap gap-2 pt-2 border-t">
      <Button
        v-for="action in quickActions"
        :key="action.id"
        @click="$emit('action', action.id)"
        size="sm"
        variant="outline"
        class="text-xs"
      >
        <Icon :name="action.icon" class="w-3 h-3 mr-1" />
        {{ action.label }}
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import Icon from '@/components/Icon.vue'

// Props
interface ProgressItem {
  label: string
  status: 'pending' | 'in_progress' | 'completed' | 'verified' | 'failed' | 'rejected'
  progress?: number
  icon: string
}

interface QuickAction {
  id: string
  label: string
  icon: string
}

interface Props {
  progressItems: ProgressItem[]
  overallLabel?: string
  showOverall?: boolean
  showDetails?: boolean
  showSummary?: boolean
  quickActions?: QuickAction[]
  variant?: 'default' | 'compact' | 'detailed'
}

const props = withDefaults(defineProps<Props>(), {
  overallLabel: 'Progress Keseluruhan',
  showOverall: true,
  showDetails: true,
  showSummary: false,
  quickActions: () => [],
  variant: 'default'
})

// Emits
const emit = defineEmits<{
  'action': [actionId: string]
}>()

// Computed
const overallPercentage = computed(() => {
  if (props.progressItems.length === 0) return 0

  const completedItems = props.progressItems.filter(item =>
    item.status === 'completed'
  ).length

  return Math.round((completedItems / props.progressItems.length) * 100)
})

const overallProgressClass = computed(() => {
  const hasFailures = props.progressItems.some(item =>
    item.status === 'failed' || item.status === 'rejected'
  )

  if (hasFailures) return 'bg-red-600'
  if (overallPercentage.value === 100) return 'bg-green-600'
  if (overallPercentage.value > 0) return 'bg-blue-600'
  return 'bg-gray-400'
})

const summary = computed(() => {
  return {
    completed: props.progressItems.filter(item =>
      item.status === 'completed' || item.status === 'verified'
    ).length,
    pending: props.progressItems.filter(item =>
      item.status === 'pending' || item.status === 'in_progress'
    ).length,
    failed: props.progressItems.filter(item =>
      item.status === 'failed' || item.status === 'rejected'
    ).length
  }
})

// Methods
const getItemIconClass = (status: string) => {
  const classes = {
    'pending': 'text-gray-400',
    'in_progress': 'text-blue-600',
    'completed': 'text-green-600',
    'verified': 'text-green-600',
    'failed': 'text-red-600',
    'rejected': 'text-red-600'
  }
  return classes[status as keyof typeof classes] || 'text-gray-400'
}

const getItemProgressClass = (status: string) => {
  const classes = {
    'pending': 'bg-gray-400',
    'in_progress': 'bg-blue-600',
    'completed': 'bg-green-600',
    'verified': 'bg-green-600',
    'failed': 'bg-red-600',
    'rejected': 'bg-red-600'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-400'
}

const getItemBadgeVariant = (status: string) => {
  const variants = {
    'pending': 'secondary',
    'in_progress': 'default',
    'completed': 'default',
    'verified': 'default',
    'failed': 'destructive',
    'rejected': 'destructive'
  }
  return variants[status as keyof typeof variants] || 'secondary'
}

const getItemStatusLabel = (status: string) => {
  const labels = {
    'pending': 'Pending',
    'in_progress': 'Proses',
    'completed': 'Selesai',
    'verified': 'Terverifikasi',
    'failed': 'Gagal',
    'rejected': 'Ditolak'
  }
  return labels[status as keyof typeof labels] || status
}
</script>
