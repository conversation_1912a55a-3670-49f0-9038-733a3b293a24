<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Pendaftaran extends Model
{
    protected $table = 'pendaftaran';
    protected $primaryKey = 'id_pendaftaran';

    protected $fillable = [
        'id_peserta',
        'id_golongan',
        'id_mimbar',
        'nomor_pendaftaran',
        'nomor_peserta',
        'nomor_urut',
        'tahun_pendaftaran',
        'status_pendaftaran',
        'tanggal_daftar',
        'verified_by',
        'verified_at',
        'approved_by',
        'approved_at',
        'regional_verified_by',
        'regional_verified_at',
        'regional_verification_notes',
        'regional_verification_status',
        'catatan_verifikasi',
        'catatan_approval',
        'keterangan'
    ];

    protected $casts = [
        'tahun_pendaftaran' => 'integer',
        'status_pendaftaran' => 'string',
        'tanggal_daftar' => 'datetime',
        'verified_at' => 'datetime',
        'approved_at' => 'datetime',
        'regional_verified_at' => 'datetime',
        'regional_verification_status' => 'string'
    ];

    // Relationships
    public function peserta(): BelongsTo
    {
        return $this->belongsTo(Peserta::class, 'id_peserta', 'id_peserta');
    }

    public function golongan(): BelongsTo
    {
        return $this->belongsTo(Golongan::class, 'id_golongan', 'id_golongan');
    }

    public function mimbar(): BelongsTo
    {
        return $this->belongsTo(Mimbar::class, 'id_mimbar', 'id_mimbar');
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by', 'id_user');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by', 'id_user');
    }

    public function regionalVerifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'regional_verified_by', 'id_user');
    }

    public function dokumenPeserta(): HasMany
    {
        return $this->hasMany(DokumenPeserta::class, 'id_pendaftaran', 'id_pendaftaran');
    }

    // Alias for documents
    public function documents(): HasMany
    {
        return $this->dokumenPeserta();
    }

    // Participant verifications through peserta
    public function participantVerifications(): HasManyThrough
    {
        return $this->hasManyThrough(
            ParticipantVerification::class,
            Peserta::class,
            'id_peserta', // Foreign key on peserta table
            'id_peserta', // Foreign key on participant_verifications table
            'id_peserta', // Local key on pendaftaran table
            'id_peserta'  // Local key on peserta table
        );
    }

    public function pembayaran(): HasOne
    {
        return $this->hasOne(Pembayaran::class, 'id_pendaftaran', 'id_pendaftaran');
    }

    public function nilaiPeserta(): HasMany
    {
        return $this->hasMany(NilaiPeserta::class, 'id_pendaftaran', 'id_pendaftaran');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status_pendaftaran', $status);
    }

    public function scopeByTahun($query, $tahun)
    {
        return $query->where('tahun_pendaftaran', $tahun);
    }

    public function scopeByGolongan($query, $idGolongan)
    {
        return $query->where('id_golongan', $idGolongan);
    }

    public function scopeRegionalVerified($query)
    {
        return $query->where('regional_verification_status', 'verified');
    }

    public function scopeRegionalPending($query)
    {
        return $query->where('regional_verification_status', 'pending');
    }

    public function scopeVisibleToProvincialAdmin($query)
    {
        return $query->whereHas('peserta', function($q) {
            $q->where('regional_verification_status', 'verified')
              ->where('documents_complete', true);
        });
    }

    // Helper methods for verification workflow
    public function needsRegionalVerification(): bool
    {
        return $this->peserta->registration_type === 'mandiri' &&
               $this->regional_verification_status === 'pending';
    }

    public function isRegionalVerified(): bool
    {
        return $this->regional_verification_status === 'verified';
    }

    public function canBeSeenByProvincialAdmin(): bool
    {
        return $this->peserta->canBeSeenByProvincialAdmin() &&
               $this->isRegionalVerified();
    }

    public function isFromAdminRegistration(): bool
    {
        return $this->peserta->registration_type === 'admin_daerah';
    }
}
