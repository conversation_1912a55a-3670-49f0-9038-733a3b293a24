<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Peserta;
use App\Models\User;
use App\Models\Wilayah;
use App\Rules\UniqueNikAcrossTables;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class PesertaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Peserta::with(['user', 'wilayah', 'registeredBy']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status_peserta', $request->status);
        }

        // Filter by wilayah
        if ($request->has('wilayah') && $request->wilayah !== 'all') {
            $query->where('id_wilayah', $request->wilayah);
        }

        $peserta = $query->orderBy('created_at', 'desc')
                        ->paginate(10)
                        ->withQueryString();

        $stats = [
            'total_peserta' => Peserta::count(),
            'total_peserta_approved' => Peserta::where('status_peserta', 'approved')->count(),
            'total_peserta_submitted' => Peserta::where('status_peserta', 'submitted')->count(),
            'total_peserta_rejected' => Peserta::where('status_peserta', 'rejected')->count(),
        ];

        $wilayah = Wilayah::aktif()->get();

        return Inertia::render('Admin/Peserta/Index', [
            'peserta' => $peserta,
            'stats' => $stats,
            'wilayah' => $wilayah,
            'filters' => $request->only(['search', 'status', 'wilayah'])
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $wilayah = Wilayah::aktif()->get();

        return Inertia::render('Admin/Peserta/Create', [
            'wilayah' => $wilayah
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'username' => 'required|string|max:50|unique:users,username',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8',
            'nik' => ['required', 'string', 'size:16', new UniqueNikAcrossTables()],
            'nama_lengkap' => 'required|string|max:100',
            'tempat_lahir' => 'required|string|max:50',
            'tanggal_lahir' => 'required|date',
            'jenis_kelamin' => 'required|in:L,P',
            'alamat' => 'required|string',
            'id_wilayah' => 'required|exists:wilayah,id_wilayah',
            'no_telepon' => 'nullable|string|max:20',
            'nama_ayah' => 'nullable|string|max:100',
            'nama_ibu' => 'nullable|string|max:100',
            'pekerjaan' => 'nullable|string|max:100',
            'instansi_asal' => 'nullable|string|max:100',
        ]);

        DB::transaction(function () use ($validated) {
            // Create user account
            $user = User::create([
                'username' => $validated['username'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'role' => 'peserta',
                'id_wilayah' => $validated['id_wilayah'],
                'nama_lengkap' => $validated['nama_lengkap'],
                'no_telepon' => $validated['no_telepon'],
                'status' => 'aktif',
                'created_by' => Auth::id()
            ]);

            // Create peserta profile with new verification workflow
            Peserta::create([
                'id_user' => $user->id_user,
                'nik' => $validated['nik'],
                'nama_lengkap' => $validated['nama_lengkap'],
                'tempat_lahir' => $validated['tempat_lahir'],
                'tanggal_lahir' => $validated['tanggal_lahir'],
                'jenis_kelamin' => $validated['jenis_kelamin'],
                'alamat' => $validated['alamat'],
                'id_wilayah' => $validated['id_wilayah'],
                'no_telepon' => $validated['no_telepon'],
                'email' => $validated['email'],
                'nama_ayah' => $validated['nama_ayah'],
                'nama_ibu' => $validated['nama_ibu'],
                'pekerjaan' => $validated['pekerjaan'],
                'instansi_asal' => $validated['instansi_asal'],
                'registered_by' => Auth::id(),
                'registration_type' => 'admin_daerah',
                'status_peserta' => 'verified',
                // Admin-registered participants bypass regional verification
                'regional_verification_status' => 'verified',
                'regional_verified_by' => Auth::id(),
                'regional_verified_at' => now(),
                'regional_verification_notes' => 'Didaftarkan langsung oleh admin provinsi',
                'documents_complete' => false // Will be updated when documents are uploaded
            ]);
        });

        return redirect()->route('admin.peserta.index')
                        ->with('success', 'Peserta berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $peserta = Peserta::with(['user', 'wilayah', 'registeredBy', 'pendaftaran.golongan.cabangLomba'])->find($id);

        return Inertia::render('Admin/Peserta/Show', [
            'peserta' => $peserta
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $peserta = Peserta::with(['user', 'wilayah'])->find($id);
        $wilayah = Wilayah::aktif()->get();

        return Inertia::render('Admin/Peserta/Edit', [
            'peserta' => $peserta,
            'wilayah' => $wilayah
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $peserta = Peserta::with('user')->find($id);

        if ($request->tipe === 'update_status') {
            $validated = $request->validate([
                'status_peserta' => 'required|in:draft,submitted,verified,approved,rejected',
            ]);

            $peserta->update([
                'status_peserta' => $validated['status_peserta']
            ]);

            return redirect()->route('admin.peserta.index')
                            ->with('success', 'Status peserta berhasil diperbarui.');
        }

        $validated = $request->validate([
            'username' => ['required', 'string', 'max:50', Rule::unique('users', 'username')->ignore($peserta->user->id_user, 'id_user')],
            'email' => ['required', 'email', Rule::unique('users', 'email')->ignore($peserta->user->id_user, 'id_user')],
            'nik' => ['required', 'string', 'size:16', new UniqueNikAcrossTables('peserta', $peserta->id_peserta, 'id_peserta')],
            'nama_lengkap' => 'required|string|max:100',
            'tempat_lahir' => 'required|string|max:50',
            'tanggal_lahir' => 'required|date',
            'jenis_kelamin' => 'required|in:L,P',
            'alamat' => 'required|string',
            'id_wilayah' => 'required|exists:wilayah,id_wilayah',
            'no_telepon' => 'nullable|string|max:20',
            'nama_ayah' => 'nullable|string|max:100',
            'nama_ibu' => 'nullable|string|max:100',
            'pekerjaan' => 'nullable|string|max:100',
            'instansi_asal' => 'nullable|string|max:100',
            'status_peserta' => 'required|in:draft,submitted,verified,approved,rejected',
        ]);

        DB::transaction(function () use ($validated, $peserta) {
            // Update user account
            $peserta->user->update([
                'username' => $validated['username'],
                'email' => $validated['email'],
                'id_wilayah' => $validated['id_wilayah'],
                'nama_lengkap' => $validated['nama_lengkap'],
                'no_telepon' => $validated['no_telepon'],
            ]);

            // Update peserta profile
            $peserta->update([
                'nik' => $validated['nik'],
                'nama_lengkap' => $validated['nama_lengkap'],
                'tempat_lahir' => $validated['tempat_lahir'],
                'tanggal_lahir' => $validated['tanggal_lahir'],
                'jenis_kelamin' => $validated['jenis_kelamin'],
                'alamat' => $validated['alamat'],
                'id_wilayah' => $validated['id_wilayah'],
                'no_telepon' => $validated['no_telepon'],
                'email' => $validated['email'],
                'nama_ayah' => $validated['nama_ayah'],
                'nama_ibu' => $validated['nama_ibu'],
                'pekerjaan' => $validated['pekerjaan'],
                'instansi_asal' => $validated['instansi_asal'],
                'status_peserta' => $validated['status_peserta']
            ]);
        });

        return redirect()->route('admin.peserta.index')
                        ->with('success', 'Peserta berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $peserta = Peserta::with('user')->find($id);

        DB::transaction(function () use ($peserta) {
            // Delete user account (will cascade delete peserta due to foreign key)
            $peserta->user->delete();
        });

        return redirect()->route('admin.peserta.index')
                        ->with('success', 'Peserta berhasil dihapus.');
    }
}
