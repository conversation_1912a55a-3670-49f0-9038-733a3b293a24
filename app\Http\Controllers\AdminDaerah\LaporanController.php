<?php

namespace App\Http\Controllers\AdminDaerah;

use App\Http\Controllers\Controller;
use App\Models\Peserta;
use App\Models\Pendaftaran;
use App\Models\Pembayaran;
use App\Models\Wilayah;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class LaporanController extends Controller
{
    /**
     * Display laporan dashboard
     */
    public function index(): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        // Get statistics for admin's wilayah
        $stats = [
            'total_peserta' => Peserta::where('id_wilayah', $adminWilayah)->count(),
            'peserta_approved' => Peserta::where('id_wilayah', $adminWilayah)->where('status_peserta', 'approved')->count(),
            'total_pendaftaran' => Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->count(),
            'pendaftaran_approved' => Pendaftaran::whereHas('peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->where('status_pendaftaran', 'approved')->count(),
            'total_pembayaran' => Pembayaran::whereHas('pendaftaran.peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->sum('jumlah_bayar'),
            'pembayaran_lunas' => Pembayaran::whereHas('pendaftaran.peserta', function($q) use ($adminWilayah) {
                $q->where('id_wilayah', $adminWilayah);
            })->where('status_pembayaran', 'approved')->sum('jumlah_bayar'),
        ];

        $wilayah = Wilayah::find($adminWilayah);

        return Inertia::render('AdminDaerah/Laporan/Index', [
            'stats' => $stats,
            'wilayah' => $wilayah
        ]);
    }

    /**
     * Display laporan peserta
     */
    public function peserta(Request $request): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $query = Peserta::with(['user', 'wilayah', 'pendaftaran.golongan.cabangLomba'])
            ->where('id_wilayah', $adminWilayah);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status_peserta', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('email', 'like', "%{$search}%");
                  });
            });
        }

        $peserta = $query->orderBy('created_at', 'desc')->paginate(20);

        return Inertia::render('AdminDaerah/Laporan/Peserta', [
            'peserta' => $peserta,
            'filters' => $request->only(['status', 'search'])
        ]);
    }

    /**
     * Display laporan pendaftaran
     */
    public function pendaftaran(Request $request): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $query = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'pembayaran'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        });

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status_pendaftaran', $request->status);
        }

        if ($request->filled('golongan')) {
            $query->where('id_golongan', $request->golongan);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('peserta', function($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%");
            });
        }

        $pendaftaran = $query->orderBy('created_at', 'desc')->paginate(20);

        return Inertia::render('AdminDaerah/Laporan/Pendaftaran', [
            'pendaftaran' => $pendaftaran,
            'filters' => $request->only(['status', 'golongan', 'search'])
        ]);
    }

    /**
     * Export laporan peserta
     */
    public function exportPeserta(Request $request)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $query = Peserta::with(['user', 'wilayah', 'pendaftaran.golongan.cabangLomba'])
            ->where('id_wilayah', $adminWilayah);

        // Apply same filters as peserta method
        if ($request->filled('status')) {
            $query->where('status_peserta', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('email', 'like', "%{$search}%");
                  });
            });
        }

        $peserta = $query->orderBy('created_at', 'desc')->get();

        // Generate CSV
        $filename = 'laporan-peserta-' . date('Y-m-d') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($peserta) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'NIK', 'Nama Lengkap', 'Email', 'Tempat Lahir', 'Tanggal Lahir',
                'Jenis Kelamin', 'No Telepon', 'Status', 'Tanggal Daftar'
            ]);

            foreach ($peserta as $p) {
                fputcsv($file, [
                    $p->nik,
                    $p->nama_lengkap,
                    $p->user->email ?? '',
                    $p->tempat_lahir,
                    $p->tanggal_lahir,
                    $p->jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan',
                    $p->no_telepon ?? '',
                    ucfirst($p->status_peserta),
                    $p->created_at->format('d/m/Y H:i')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export laporan pendaftaran
     */
    public function exportPendaftaran(Request $request)
    {
        $adminWilayah = Auth::user()->id_wilayah;

        $query = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'pembayaran'
        ])
        ->whereHas('peserta', function($q) use ($adminWilayah) {
            $q->where('id_wilayah', $adminWilayah);
        });

        // Apply same filters as pendaftaran method
        if ($request->filled('status')) {
            $query->where('status_pendaftaran', $request->status);
        }

        if ($request->filled('golongan')) {
            $query->where('id_golongan', $request->golongan);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('peserta', function($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%");
            });
        }

        $pendaftaran = $query->orderBy('created_at', 'desc')->get();

        // Generate CSV
        $filename = 'laporan-pendaftaran-' . date('Y-m-d') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($pendaftaran) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Nomor Urut', 'NIK', 'Nama Peserta', 'Cabang Lomba', 'Golongan',
                'Biaya', 'Status Pendaftaran', 'Status Pembayaran', 'Tanggal Daftar'
            ]);

            foreach ($pendaftaran as $p) {
                fputcsv($file, [
                    $p->nomor_urut,
                    $p->peserta->nik,
                    $p->peserta->nama_lengkap,
                    $p->golongan->cabangLomba->nama_cabang ?? '',
                    $p->golongan->nama_golongan,
                    number_format($p->biaya_pendaftaran, 0, ',', '.'),
                    ucfirst($p->status_pendaftaran),
                    $p->pembayaran ? ucfirst($p->pembayaran->status_pembayaran) : 'Belum Bayar',
                    $p->created_at->format('d/m/Y H:i')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
