<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;

class UniqueNikAcrossTables implements ValidationRule
{
    protected $ignoreTable;
    protected $ignoreId;
    protected $ignoreColumn;

    public function __construct($ignoreTable = null, $ignoreId = null, $ignoreColumn = 'id')
    {
        $this->ignoreTable = $ignoreTable;
        $this->ignoreId = $ignoreId;
        $this->ignoreColumn = $ignoreColumn;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check if NIK exists in peserta table
        $pesertaQuery = DB::table('peserta')->where('nik', $value);
        if ($this->ignoreTable === 'peserta' && $this->ignoreId) {
            $pesertaQuery->where($this->ignoreColumn, '!=', $this->ignoreId);
        }
        $existsInPeserta = $pesertaQuery->exists();

        // Check if NIK exists in dewan_hakim table
        $dewaHakimQuery = DB::table('dewan_hakim')->where('nik', $value);
        if ($this->ignoreTable === 'dewan_hakim' && $this->ignoreId) {
            $dewaHakimQuery->where($this->ignoreColumn, '!=', $this->ignoreId);
        }
        $existsInDewaHakim = $dewaHakimQuery->exists();

        if ($existsInPeserta || $existsInDewaHakim) {
            $fail('NIK sudah terdaftar dalam sistem.');
        }
    }
}
