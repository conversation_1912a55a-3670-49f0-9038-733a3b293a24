<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PembayaranController extends Controller
{
    public function index()
    {
        return Inertia::render('Admin/Pembayaran/Index');
    }

    public function show($id)
    {
        return Inertia::render('Admin/Pembayaran/Show');
    }

    public function update(Request $request, $id)
    {
        return back()->with('success', 'Pembayaran berhasil diupdate');
    }

    public function verify(Request $request, $id)
    {
        return back()->with('success', 'Pembayaran berhasil diverifikasi');
    }

    public function reject(Request $request, $id)
    {
        return back()->with('success', 'Pembayaran berhasil ditolak');
    }
}
