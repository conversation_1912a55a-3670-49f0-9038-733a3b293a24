<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PembayaranController extends Controller
{
    public function index()
    {
        return Inertia::render('Admin/Pembayaran/Index', [
            'paymentDisabled' => true,
            'message' => 'Fitur pembayaran sementara dinonaktifkan. Pendaftaran saat ini gratis untuk semua peserta.'
        ]);
    }

    public function show($id)
    {
        return Inertia::render('Admin/Pembayaran/Show', [
            'paymentDisabled' => true,
            'message' => 'Fitur pembayaran sementara dinonaktifkan. Pendaftaran saat ini gratis untuk semua peserta.'
        ]);
    }

    public function update(Request $request, $id)
    {
        return back()->with('error', 'Fitur pembayaran sementara dinonaktifkan. Pendaftaran saat ini gratis.');
    }

    public function verify(Request $request, $id)
    {
        return back()->with('error', 'Fitur pembayaran sementara dinonaktifkan. Pendaftaran saat ini gratis.');
    }

    public function reject(Request $request, $id)
    {
        return back()->with('error', 'Fitur pembayaran sementara dinonaktifkan. Pendaftaran saat ini gratis.');
    }
}
