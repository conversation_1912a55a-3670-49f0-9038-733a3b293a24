<?php

namespace App\Http\Controllers\AdminDaerah;

use App\Http\Controllers\Controller;
use App\Models\Peserta;
use App\Models\Pendaftaran;
use App\Models\DokumenPeserta;
use App\Models\DocumentType;
use App\Models\GolonganDocumentRequirement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class VerificationController extends Controller
{
    /**
     * Display participants requiring regional verification
     */
    public function index(Request $request): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;
        
        // Get participants that need regional verification
        $query = Peserta::with([
            'user',
            'wilayah',
            'pendaftaran.golongan.cabangLomba',
            'pendaftaran.dokumenPeserta.documentType',
            'regionalVerifiedBy'
        ])
        ->where('id_wilayah', $adminWilayah)
        ->where('registration_type', 'mandiri') // Only self-registered participants
        ->where('regional_verification_status', 'pending');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status_peserta', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('email', 'like', "%{$search}%");
                  });
            });
        }

        $participants = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get statistics
        $stats = [
            'total_pending' => Peserta::where('id_wilayah', $adminWilayah)
                ->where('registration_type', 'mandiri')
                ->where('regional_verification_status', 'pending')
                ->count(),
            'total_verified' => Peserta::where('id_wilayah', $adminWilayah)
                ->where('registration_type', 'mandiri')
                ->where('regional_verification_status', 'verified')
                ->count(),
            'total_rejected' => Peserta::where('id_wilayah', $adminWilayah)
                ->where('registration_type', 'mandiri')
                ->where('regional_verification_status', 'rejected')
                ->count(),
        ];

        return Inertia::render('AdminDaerah/Verification/Index', [
            'participants' => $participants,
            'stats' => $stats,
            'filters' => $request->only(['status', 'search'])
        ]);
    }

    /**
     * Show detailed verification view for a participant
     */
    public function show(string $id): Response
    {
        $adminWilayah = Auth::user()->id_wilayah;
        
        $participant = Peserta::with([
            'user',
            'wilayah',
            'pendaftaran.golongan.cabangLomba',
            'pendaftaran.dokumenPeserta.documentType',
            'regionalVerifiedBy'
        ])
        ->where('id_wilayah', $adminWilayah)
        ->where('registration_type', 'mandiri')
        ->findOrFail($id);

        // Get required documents for each registration
        $registrationsWithRequirements = [];
        foreach ($participant->pendaftaran as $registration) {
            $requiredDocuments = GolonganDocumentRequirement::with('documentType')
                ->where('id_golongan', $registration->id_golongan)
                ->where('is_required', true)
                ->get();

            $uploadedDocuments = $registration->dokumenPeserta->keyBy('document_type_id');
            
            $documentStatus = [];
            foreach ($requiredDocuments as $requirement) {
                $documentStatus[] = [
                    'document_type' => $requirement->documentType,
                    'is_required' => true,
                    'uploaded' => isset($uploadedDocuments[$requirement->document_type_id]),
                    'document' => $uploadedDocuments[$requirement->document_type_id] ?? null
                ];
            }

            $registrationsWithRequirements[] = [
                'registration' => $registration,
                'required_documents' => $documentStatus,
                'documents_complete' => collect($documentStatus)->every(fn($doc) => $doc['uploaded'])
            ];
        }

        return Inertia::render('AdminDaerah/Verification/Show', [
            'participant' => $participant,
            'registrations' => $registrationsWithRequirements
        ]);
    }

    /**
     * Verify a participant
     */
    public function verify(Request $request, string $id)
    {
        $adminWilayah = Auth::user()->id_wilayah;
        
        $validated = $request->validate([
            'action' => 'required|in:verify,reject',
            'notes' => 'nullable|string|max:1000'
        ]);

        $participant = Peserta::where('id_wilayah', $adminWilayah)
            ->where('registration_type', 'mandiri')
            ->where('regional_verification_status', 'pending')
            ->findOrFail($id);

        DB::transaction(function() use ($participant, $validated) {
            $status = $validated['action'] === 'verify' ? 'verified' : 'rejected';
            
            // Update participant regional verification
            $participant->update([
                'regional_verification_status' => $status,
                'regional_verified_by' => Auth::id(),
                'regional_verified_at' => now(),
                'regional_verification_notes' => $validated['notes'],
                'status_peserta' => $status === 'verified' ? 'regional_verified' : 'rejected'
            ]);

            // Update all registrations for this participant
            foreach ($participant->pendaftaran as $registration) {
                $registration->update([
                    'regional_verification_status' => $status,
                    'regional_verified_by' => Auth::id(),
                    'regional_verified_at' => now(),
                    'regional_verification_notes' => $validated['notes'],
                    'status_pendaftaran' => $status === 'verified' ? 'regional_verified' : 'rejected'
                ]);
            }

            // Check and update document completion status if verified
            if ($status === 'verified') {
                $this->updateDocumentCompletionStatus($participant);
            }
        });

        $message = $validated['action'] === 'verify' 
            ? 'Peserta berhasil diverifikasi' 
            : 'Peserta berhasil ditolak';

        return redirect()->route('admin-daerah.verification.index')
            ->with('success', $message);
    }

    /**
     * Bulk verify participants
     */
    public function bulkVerify(Request $request)
    {
        $adminWilayah = Auth::user()->id_wilayah;
        
        $validated = $request->validate([
            'participant_ids' => 'required|array',
            'participant_ids.*' => 'exists:peserta,id_peserta',
            'action' => 'required|in:verify,reject',
            'notes' => 'nullable|string|max:1000'
        ]);

        $participants = Peserta::where('id_wilayah', $adminWilayah)
            ->where('registration_type', 'mandiri')
            ->where('regional_verification_status', 'pending')
            ->whereIn('id_peserta', $validated['participant_ids'])
            ->get();

        if ($participants->isEmpty()) {
            return back()->with('error', 'Tidak ada peserta yang dapat diverifikasi');
        }

        DB::transaction(function() use ($participants, $validated) {
            $status = $validated['action'] === 'verify' ? 'verified' : 'rejected';
            
            foreach ($participants as $participant) {
                // Update participant
                $participant->update([
                    'regional_verification_status' => $status,
                    'regional_verified_by' => Auth::id(),
                    'regional_verified_at' => now(),
                    'regional_verification_notes' => $validated['notes'],
                    'status_peserta' => $status === 'verified' ? 'regional_verified' : 'rejected'
                ]);

                // Update registrations
                foreach ($participant->pendaftaran as $registration) {
                    $registration->update([
                        'regional_verification_status' => $status,
                        'regional_verified_by' => Auth::id(),
                        'regional_verified_at' => now(),
                        'regional_verification_notes' => $validated['notes'],
                        'status_pendaftaran' => $status === 'verified' ? 'regional_verified' : 'rejected'
                    ]);
                }

                // Check document completion if verified
                if ($status === 'verified') {
                    $this->updateDocumentCompletionStatus($participant);
                }
            }
        });

        $count = $participants->count();
        $message = $validated['action'] === 'verify' 
            ? "Berhasil memverifikasi {$count} peserta" 
            : "Berhasil menolak {$count} peserta";

        return back()->with('success', $message);
    }

    /**
     * Update document completion status for a participant
     */
    private function updateDocumentCompletionStatus(Peserta $participant): void
    {
        $allDocumentsComplete = true;
        
        foreach ($participant->pendaftaran as $registration) {
            $requiredDocuments = GolonganDocumentRequirement::where('id_golongan', $registration->id_golongan)
                ->where('is_required', true)
                ->count();
                
            $uploadedDocuments = $registration->dokumenPeserta()->count();
            
            if ($uploadedDocuments < $requiredDocuments) {
                $allDocumentsComplete = false;
                break;
            }
        }

        $participant->update([
            'documents_complete' => $allDocumentsComplete,
            'documents_completed_at' => $allDocumentsComplete ? now() : null
        ]);
    }
}
