<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Wilayah extends Model
{
    protected $table = 'wilayah';
    protected $primaryKey = 'id_wilayah';

    protected $fillable = [
        'kode_wilayah',
        'nama_wilayah',
        'level_wilayah',
        'parent_id',
        'status'
    ];

    protected $casts = [
        'level_wilayah' => 'string',
        'status' => 'string'
    ];

    // Relationships
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Wilayah::class, 'parent_id', 'id_wilayah');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Wilayah::class, 'parent_id', 'id_wilayah');
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'id_wilayah', 'id_wilayah');
    }

    public function peserta(): HasMany
    {
        return $this->hasMany(Peserta::class, 'id_wilayah', 'id_wilayah');
    }

    public function dewaHakim(): HasMany
    {
        return $this->hasMany(DewaHakim::class, 'id_wilayah', 'id_wilayah');
    }

    public function suratMandat(): HasMany
    {
        return $this->hasMany(SuratMandat::class, 'id_wilayah', 'id_wilayah');
    }

    // Scopes
    public function scopeAktif($query)
    {
        return $query->where('status', 'aktif');
    }

    public function scopeByLevel($query, $level)
    {
        return $query->where('level_wilayah', $level);
    }
}
