<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\NilaiPeserta;
use App\Models\Golongan;
use App\Models\CabangLomba;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class ReportController extends Controller
{
    /**
     * Get public results (if enabled)
     */
    public function publicResults(Request $request): JsonResponse
    {
        // Check if public results are enabled (you might want to add this to settings)
        $publicResultsEnabled = config('app.public_results_enabled', false);
        
        if (!$publicResultsEnabled) {
            return response()->json([
                'success' => false,
                'message' => 'Public results are not available at this time'
            ], 403);
        }

        $query = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'nilaiPeserta.dewaHakim'
        ])->whereIn('status_pendaftaran', ['verified']);

        // Filter by competition category
        if ($request->has('id_golongan') && $request->id_golongan) {
            $query->where('id_golongan', $request->id_golongan);
        }

        // Filter by competition branch
        if ($request->has('id_cabang_lomba') && $request->id_cabang_lomba) {
            $query->whereHas('golongan', function ($q) use ($request) {
                $q->where('id_cabang_lomba', $request->id_cabang_lomba);
            });
        }

        // Get results with average scores
        $results = $query->get()->map(function ($pendaftaran) {
            $scores = $pendaftaran->nilaiPeserta;
            
            if ($scores->count() > 0) {
                $avgScore = $scores->avg('total_nilai');
                $pendaftaran->average_score = round($avgScore, 2);
                $pendaftaran->total_judges = $scores->count();
            } else {
                $pendaftaran->average_score = 0;
                $pendaftaran->total_judges = 0;
            }
            
            return $pendaftaran;
        })->sortByDesc('average_score')->values();

        return response()->json([
            'success' => true,
            'data' => [
                'results' => $results,
                'filters' => $request->only(['id_golongan', 'id_cabang_lomba'])
            ]
        ]);
    }

    /**
     * Get competition statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        $stats = [
            'total_participants' => Pendaftaran::whereIn('status_pendaftaran', ['approved', 'verified'])->count(),
            'total_categories' => Golongan::where('status', 'aktif')->count(),
            'total_competitions' => CabangLomba::where('status', 'aktif')->count(),
            'total_scores' => NilaiPeserta::count(),
        ];

        // Participants by category
        $participantsByCategory = Golongan::with('cabangLomba')
            ->withCount(['pendaftaran' => function ($query) {
                $query->whereIn('status_pendaftaran', ['approved', 'verified']);
            }])
            ->where('status', 'aktif')
            ->get()
            ->map(function ($golongan) {
                return [
                    'category' => $golongan->nama_golongan,
                    'competition' => $golongan->cabangLomba->nama_cabang,
                    'participants' => $golongan->pendaftaran_count
                ];
            });

        // Participants by region
        $participantsByRegion = DB::table('pendaftaran')
            ->join('peserta', 'pendaftaran.id_peserta', '=', 'peserta.id_peserta')
            ->join('wilayah', 'peserta.id_wilayah', '=', 'wilayah.id_wilayah')
            ->whereIn('pendaftaran.status_pendaftaran', ['approved', 'verified'])
            ->select('wilayah.nama_wilayah', DB::raw('count(*) as total'))
            ->groupBy('wilayah.id_wilayah', 'wilayah.nama_wilayah')
            ->orderBy('total', 'desc')
            ->get();

        // Scoring progress
        $totalAssignments = Pendaftaran::whereIn('status_pendaftaran', ['approved', 'verified'])->count();
        $completedScores = NilaiPeserta::distinct('id_pendaftaran')->count();
        $scoringProgress = $totalAssignments > 0 ? round(($completedScores / $totalAssignments) * 100, 2) : 0;

        return response()->json([
            'success' => true,
            'data' => [
                'overview' => $stats,
                'participants_by_category' => $participantsByCategory,
                'participants_by_region' => $participantsByRegion,
                'scoring_progress' => [
                    'total_assignments' => $totalAssignments,
                    'completed_scores' => $completedScores,
                    'progress_percentage' => $scoringProgress
                ]
            ]
        ]);
    }

    /**
     * Get detailed results for a specific category
     */
    public function categoryResults(string $categoryId): JsonResponse
    {
        $golongan = Golongan::with('cabangLomba')->findOrFail($categoryId);

        $results = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'nilaiPeserta.dewaHakim'
        ])->where('id_golongan', $categoryId)
          ->whereIn('status_pendaftaran', ['verified'])
          ->get()
          ->map(function ($pendaftaran) {
              $scores = $pendaftaran->nilaiPeserta;
              
              if ($scores->count() > 0) {
                  $pendaftaran->average_score = round($scores->avg('total_nilai'), 2);
                  $pendaftaran->total_judges = $scores->count();
                  $pendaftaran->detailed_scores = [
                      'tajwid' => round($scores->avg('nilai_tajwid'), 2),
                      'fashohah' => round($scores->avg('nilai_fashohah'), 2),
                      'suara' => round($scores->avg('nilai_suara'), 2),
                      'adab' => round($scores->avg('nilai_adab'), 2),
                  ];
              } else {
                  $pendaftaran->average_score = 0;
                  $pendaftaran->total_judges = 0;
                  $pendaftaran->detailed_scores = [
                      'tajwid' => 0,
                      'fashohah' => 0,
                      'suara' => 0,
                      'adab' => 0,
                  ];
              }
              
              return $pendaftaran;
          })
          ->sortByDesc('average_score')
          ->values();

        return response()->json([
            'success' => true,
            'data' => [
                'category' => $golongan,
                'results' => $results,
                'statistics' => [
                    'total_participants' => $results->count(),
                    'average_score' => $results->avg('average_score'),
                    'highest_score' => $results->max('average_score'),
                    'lowest_score' => $results->min('average_score'),
                ]
            ]
        ]);
    }

    /**
     * Get judge performance report
     */
    public function judgePerformance(): JsonResponse
    {
        $judgeStats = DB::table('nilai_peserta')
            ->join('dewan_hakim', 'nilai_peserta.id_dewan_hakim', '=', 'dewan_hakim.id_dewan_hakim')
            ->select(
                'dewan_hakim.nama_lengkap',
                'dewan_hakim.spesialisasi',
                DB::raw('count(*) as total_scores'),
                DB::raw('avg(nilai_peserta.total_nilai) as average_score_given'),
                DB::raw('min(nilai_peserta.tanggal_penilaian) as first_score_date'),
                DB::raw('max(nilai_peserta.tanggal_penilaian) as last_score_date')
            )
            ->groupBy('dewan_hakim.id_dewan_hakim', 'dewan_hakim.nama_lengkap', 'dewan_hakim.spesialisasi')
            ->orderBy('total_scores', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $judgeStats
        ]);
    }

    /**
     * Export results to CSV format
     */
    public function exportResults(Request $request): JsonResponse
    {
        $query = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'nilaiPeserta'
        ])->whereIn('status_pendaftaran', ['verified']);

        // Apply filters
        if ($request->has('id_golongan') && $request->id_golongan) {
            $query->where('id_golongan', $request->id_golongan);
        }

        $results = $query->get()->map(function ($pendaftaran) {
            $scores = $pendaftaran->nilaiPeserta;
            
            return [
                'nomor_peserta' => $pendaftaran->nomor_peserta,
                'nama_peserta' => $pendaftaran->peserta->nama_lengkap,
                'wilayah' => $pendaftaran->peserta->wilayah->nama_wilayah ?? '',
                'kategori' => $pendaftaran->golongan->nama_golongan,
                'cabang_lomba' => $pendaftaran->golongan->cabangLomba->nama_cabang,
                'rata_rata_nilai' => $scores->count() > 0 ? round($scores->avg('total_nilai'), 2) : 0,
                'nilai_tajwid' => $scores->count() > 0 ? round($scores->avg('nilai_tajwid'), 2) : 0,
                'nilai_fashohah' => $scores->count() > 0 ? round($scores->avg('nilai_fashohah'), 2) : 0,
                'nilai_suara' => $scores->count() > 0 ? round($scores->avg('nilai_suara'), 2) : 0,
                'nilai_adab' => $scores->count() > 0 ? round($scores->avg('nilai_adab'), 2) : 0,
                'jumlah_hakim' => $scores->count(),
            ];
        })->sortByDesc('rata_rata_nilai')->values();

        return response()->json([
            'success' => true,
            'data' => $results,
            'headers' => [
                'Nomor Peserta',
                'Nama Peserta',
                'Wilayah',
                'Kategori',
                'Cabang Lomba',
                'Rata-rata Nilai',
                'Nilai Tajwid',
                'Nilai Fashohah',
                'Nilai Suara',
                'Nilai Adab',
                'Jumlah Hakim'
            ]
        ]);
    }
}
