<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('dokumen_peserta', function (Blueprint $table) {
            // Add document_type_id field (nullable for backward compatibility)
            $table->unsignedBigInteger('document_type_id')->nullable()->after('jenis_dokumen');

            // Add foreign key constraint
            $table->foreign('document_type_id')->references('id_document_type')->on('document_types')->onDelete('set null');

            // Add index for better performance
            $table->index(['document_type_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('dokumen_peserta', function (Blueprint $table) {
            // Drop foreign key and column
            $table->dropForeign(['document_type_id']);
            $table->dropColumn('document_type_id');
        });
    }
};
