<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Mimbar;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class MimbarController extends Controller
{
    /**
     * Display a listing of mimbar.
     */
    public function index(Request $request)
    {
        $query = Mimbar::withCount('pendaftaran')
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('nama_mimbar', 'like', "%{$search}%")
                      ->orWhere('kode_mimbar', 'like', "%{$search}%");
                });
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            });

        $mimbar = $query->latest()->paginate(15)->withQueryString();

        return Inertia::render('Admin/Mimbar/Index', [
            'mimbar' => $mimbar,
            'filters' => $request->only(['search', 'status'])
        ]);
    }

    /**
     * Show the form for creating a new mimbar.
     */
    public function create()
    {
        return Inertia::render('Admin/Mimbar/Create');
    }

    /**
     * Store a newly created mimbar in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'kode_mimbar' => 'required|string|max:10|unique:mimbar',
            'nama_mimbar' => 'required|string|max:100',
            'keterangan' => 'nullable|string',
            'kapasitas' => 'required|integer|min:1',
            'status' => ['required', Rule::in(['aktif', 'non_aktif'])]
        ]);

        Mimbar::create($validated);

        return redirect()->route('admin.mimbar.index')
            ->with('success', 'Mimbar berhasil dibuat.');
    }

    /**
     * Display the specified mimbar.
     */
    public function show(Mimbar $mimbar)
    {
        $mimbar->load(['pendaftaran.peserta', 'pendaftaran.golongan.cabangLomba']);

        return Inertia::render('Admin/Mimbar/Show', [
            'mimbar' => $mimbar
        ]);
    }

    /**
     * Show the form for editing the specified mimbar.
     */
    public function edit(Mimbar $mimbar)
    {
        return Inertia::render('Admin/Mimbar/Edit', [
            'mimbar' => $mimbar
        ]);
    }

    /**
     * Update the specified mimbar in storage.
     */
    public function update(Request $request, Mimbar $mimbar)
    {
        $validated = $request->validate([
            'kode_mimbar' => ['required', 'string', 'max:10', Rule::unique('mimbar')->ignore($mimbar->id_mimbar, 'id_mimbar')],
            'nama_mimbar' => 'required|string|max:100',
            'keterangan' => 'nullable|string',
            'kapasitas' => 'required|integer|min:1',
            'status' => ['required', Rule::in(['aktif', 'non_aktif'])]
        ]);

        $mimbar->update($validated);

        return redirect()->route('admin.mimbar.index')
            ->with('success', 'Mimbar berhasil diperbarui.');
    }

    /**
     * Remove the specified mimbar from storage.
     */
    public function destroy(Mimbar $mimbar)
    {
        // Check if mimbar has pendaftaran
        if ($mimbar->pendaftaran()->count() > 0) {
            return back()->with('error', 'Mimbar tidak dapat dihapus karena memiliki pendaftaran.');
        }

        $mimbar->delete();

        return redirect()->route('admin.mimbar.index')
            ->with('success', 'Mimbar berhasil dihapus.');
    }

    /**
     * Toggle mimbar status.
     */
    public function toggleStatus(Mimbar $mimbar)
    {
        $newStatus = $mimbar->status === 'aktif' ? 'non_aktif' : 'aktif';
        $mimbar->update(['status' => $newStatus]);

        return back()->with('success', "Status mimbar berhasil diubah menjadi {$newStatus}.");
    }
}
