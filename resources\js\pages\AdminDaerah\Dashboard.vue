<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'

interface User {
  username: string
  nama_lengkap: string
}

interface CabangLomba {
  nama_cabang: string
}

interface Golongan {
  nama_golongan: string
  cabang_lomba: CabangLomba
}

interface Pendaftaran {
  nomor_pendaftaran: string
  status_pendaftaran: string
  golongan: Golongan
}

interface Pembayaran {
  status_pembayaran: string
}

interface Peserta {
  id_peserta: number
  nama_lengkap: string
  status_peserta: string
  registration_type: string
  created_at: string
  user: User
  pendaftaran: Pendaftaran[]
}

interface PendaftaranDetail {
  id_pendaftaran: number
  nomor_pendaftaran: string
  status_pendaftaran: string
  created_at: string
  peserta: {
    nama_lengkap: string
    user: User
  }
  golongan: Golongan
  pembayaran: Pembayaran | null
}

interface Stats {
  total_peserta: number
  peserta_approved: number
  peserta_pending: number
  total_pendaftaran: number
  pendaftaran_approved: number
  dokumen_pending: number
  pembayaran_pending: number
}

const props = defineProps<{
  stats: Stats
  recentPeserta: Peserta[]
  recentPendaftaran: PendaftaranDetail[]
}>()

function getStatusColor(status: string): string {
  const colors = {
    draft: 'bg-gray-100 text-gray-800',
    submitted: 'bg-blue-100 text-blue-800',
    verified: 'bg-indigo-100 text-indigo-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    payment_pending: 'bg-yellow-100 text-yellow-800',
    paid: 'bg-green-100 text-green-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string): string {
  const texts = {
    draft: 'Draft',
    submitted: 'Disubmit',
    verified: 'Terverifikasi',
    approved: 'Disetujui',
    rejected: 'Ditolak',
    payment_pending: 'Menunggu Pembayaran',
    paid: 'Sudah Dibayar'
  }
  return texts[status as keyof typeof texts] || status
}

function getRegistrationTypeText(type: string): string {
  const types = {
    mandiri: 'Mandiri',
    admin_daerah: 'Admin Daerah'
  }
  return types[type as keyof typeof types] || type
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>

<template>
  <AppLayout>

      <div class="flex items-center justify-between mb-6">
        <div class="islamic-gradient p-6 rounded-lg islamic-shadow">
          <Heading title="Dashboard Admin Daerah" class="text-white"/>
          <p class="text-green-100 mt-2">Kelola peserta MTQ di wilayah Anda</p>
        </div>
        <div class="flex space-x-2">
          <Button as-child class="islamic-shadow">
            <TextLink :href="route('admin-daerah.peserta.create')">
              <Icon name="userPlus" class="w-4 h-4 mr-2" />
              Daftarkan Peserta Baru
            </TextLink>
          </Button>
          <Button as-child class="islamic-shadow bg-blue-600 hover:bg-blue-700">
            <TextLink :href="route('admin-daerah.verification.index')">
              <Icon name="shield-check" class="w-4 h-4 mr-2" />
              Verifikasi Regional
            </TextLink>
          </Button>
        </div>
      </div>


    <Head title="Dashboard Admin Daerah" />

    <div class="space-y-6">
      <!-- Statistics Cards with Islamic Theme -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Verification Statistics Card -->
        <Card class="islamic-shadow hover:islamic-shadow-lg transition-all duration-300 geometric-pattern md:col-span-4 mb-4">
          <CardContent class="p-6">
            <h3 class="text-lg font-semibold text-islamic-700 mb-4">Verifikasi Regional</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="flex items-center space-x-2">
                <div class="p-3 bg-yellow-500 rounded-full">
                  <Icon name="clock" class="h-6 w-6 text-white" />
                </div>
                <div>
                  <p class="text-2xl font-bold text-yellow-700">{{ stats.verification_pending }}</p>
                  <p class="text-sm text-yellow-600">Menunggu Verifikasi</p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <div class="p-3 bg-green-500 rounded-full">
                  <Icon name="check" class="h-6 w-6 text-white" />
                </div>
                <div>
                  <p class="text-2xl font-bold text-green-700">{{ stats.verification_verified }}</p>
                  <p class="text-sm text-green-600">Terverifikasi</p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <div class="p-3 bg-red-500 rounded-full">
                  <Icon name="x" class="h-6 w-6 text-white" />
                </div>
                <div>
                  <p class="text-2xl font-bold text-red-700">{{ stats.verification_rejected }}</p>
                  <p class="text-sm text-red-600">Ditolak</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Main Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card class="islamic-shadow hover:islamic-shadow-lg transition-all duration-300 geometric-pattern">
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-islamic-500 rounded-full">
                <Icon name="users" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-islamic-700">{{ stats.total_peserta }}</p>
                <p class="text-sm text-islamic-600">Total Peserta</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="islamic-shadow hover:islamic-shadow-lg transition-all duration-300 geometric-pattern">
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-emerald-500 rounded-full">
                <Icon name="check-circle" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-emerald-700">{{ stats.peserta_approved }}</p>
                <p class="text-sm text-emerald-600">Peserta Disetujui</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="islamic-shadow hover:islamic-shadow-lg transition-all duration-300 geometric-pattern">
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-jade-500 rounded-full">
                <Icon name="clipboard-list" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-jade-700">{{ stats.total_pendaftaran }}</p>
                <p class="text-sm text-jade-600">Total Pendaftaran</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="islamic-shadow hover:islamic-shadow-lg transition-all duration-300 geometric-pattern">
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-islamic-400 rounded-full">
                <Icon name="clock" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-islamic-700">{{ stats.peserta_pending }}</p>
                <p class="text-sm text-gray-600">Menunggu Verifikasi</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Additional Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <Icon name="trophy" class="h-8 w-8 text-orange-600" />
              <div>
                <p class="text-2xl font-bold">{{ stats.pendaftaran_approved }}</p>
                <p class="text-sm text-gray-600">Pendaftaran Disetujui</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <Icon name="file-text" class="h-8 w-8 text-indigo-600" />
              <div>
                <p class="text-2xl font-bold">{{ stats.dokumen_pending }}</p>
                <p class="text-sm text-gray-600">Dokumen Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <Icon name="credit-card" class="h-8 w-8 text-red-600" />
              <div>
                <p class="text-2xl font-bold">{{ stats.pembayaran_pending }}</p>
                <p class="text-sm text-gray-600">Pembayaran Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card class="hover:shadow-lg transition-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <Icon name="user-plus" class="h-8 w-8 text-blue-600" />
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900">Daftarkan Peserta</h3>
                <p class="text-sm text-gray-500">Daftarkan peserta baru dari wilayah Anda</p>
              </div>
            </div>
            <div class="mt-4">
              <Button as-child class="w-full">
                <TextLink :href="route('admin-daerah.peserta.create')">
                    Daftarkan Sekarang
                </TextLink>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card class="hover:shadow-lg transition-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <Icon name="users" class="h-8 w-8 text-green-600" />
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900">Kelola Peserta</h3>
                <p class="text-sm text-gray-500">Lihat dan kelola data peserta</p>
              </div>
            </div>
            <div class="mt-4">
              <Button as-child variant="outline" class="w-full">
                  <TextLink :href="route('admin-daerah.peserta.index')">
                      Kelola Peserta
                  </TextLink>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card class="hover:shadow-lg transition-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <Icon name="search" class="h-8 w-8 text-purple-600" />
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900">Lihat Lomba</h3>
                <p class="text-sm text-gray-500">Jelajahi cabang lomba yang tersedia</p>
              </div>
            </div>
            <div class="mt-4">
              <Button as-child variant="outline" class="w-full">
                <TextLink :href="route('competition.index')">Lihat Lomba</TextLink>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Recent Activity -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Peserta -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle>Peserta Terbaru</CardTitle>
              <Button as-child variant="outline" size="sm">
                <TextLink :href="route('admin-daerah.peserta.index')">Lihat Semua</TextLink>
              </Button>
            </div>
            <CardDescription>Peserta yang baru didaftarkan</CardDescription>
          </CardHeader>
          <CardContent>
            <div v-if="recentPeserta.length === 0" class="text-center py-8">
              <Icon name="users" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-600">Belum ada peserta yang didaftarkan.</p>
            </div>

            <div v-else class="space-y-4">
              <div
                v-for="peserta in recentPeserta"
                :key="peserta.id_peserta"
                class="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div class="flex-1">
                  <h4 class="font-medium">{{ peserta.nama_lengkap }}</h4>
                  <p class="text-sm text-gray-600">{{ peserta.user.username }}</p>
                  <p class="text-xs text-gray-500">{{ formatDate(peserta.created_at) }}</p>
                </div>
                <div class="text-right">
                  <Badge :class="getStatusColor(peserta.status_peserta)">
                    {{ getStatusText(peserta.status_peserta) }}
                  </Badge>
                  <div class="mt-1">
                    <Badge variant="outline" class="text-xs">
                      {{ getRegistrationTypeText(peserta.registration_type) }}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Recent Registrations -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle>Pendaftaran Terbaru</CardTitle>
              <Button as-child variant="outline" size="sm">
                <TextLink :href="route('admin.pendaftaran.index')">Lihat Semua</TextLink>
              </Button>
            </div>
            <CardDescription>Pendaftaran lomba terbaru</CardDescription>
          </CardHeader>
          <CardContent>
            <div v-if="recentPendaftaran.length === 0" class="text-center py-8">
              <Icon name="clipboard-list" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-600">Belum ada pendaftaran lomba.</p>
            </div>

            <div v-else class="space-y-4">
              <div
                v-for="reg in recentPendaftaran"
                :key="reg.id_pendaftaran"
                class="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div class="flex-1">
                  <h4 class="font-medium">{{ reg.peserta.nama_lengkap }}</h4>
                  <p class="text-sm text-gray-600">{{ reg.golongan.nama_golongan }}</p>
                  <p class="text-xs text-gray-500">{{ reg.nomor_pendaftaran }}</p>
                </div>
                <div class="text-right">
                  <Badge :class="getStatusColor(reg.status_pendaftaran)">
                    {{ getStatusText(reg.status_pendaftaran) }}
                  </Badge>
                  <div v-if="reg.pembayaran" class="mt-1">
                    <Badge
                      :class="getStatusColor(reg.pembayaran.status_pembayaran)"
                      variant="outline"
                      class="text-xs"
                    >
                      {{ getStatusText(reg.pembayaran.status_pembayaran) }}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </AppLayout>
</template>
