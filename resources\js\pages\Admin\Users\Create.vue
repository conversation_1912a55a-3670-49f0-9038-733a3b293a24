<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Tambah User" />
    <Heading title="Tambah User" />

    <div class="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Informasi User Baru</CardTitle>
          <CardDescription>
            Lengkapi form di bawah untuk menambahkan user baru ke sistem
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Basic Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Dasar</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="username">Username *</Label>
                  <Input
                    id="username"
                    v-model="form.username"
                    type="text"
                    required
                    :class="{ 'border-red-500': form.errors.username }"
                  />
                  <p v-if="form.errors.username" class="text-sm text-red-600 mt-1">
                    {{ form.errors.username }}
                  </p>
                </div>

                <div>
                  <Label for="email">Email *</Label>
                  <Input
                    id="email"
                    v-model="form.email"
                    type="email"
                    required
                    :class="{ 'border-red-500': form.errors.email }"
                  />
                  <p v-if="form.errors.email" class="text-sm text-red-600 mt-1">
                    {{ form.errors.email }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="nama_lengkap">Nama Lengkap *</Label>
                <Input
                  id="nama_lengkap"
                  v-model="form.nama_lengkap"
                  type="text"
                  required
                  :class="{ 'border-red-500': form.errors.nama_lengkap }"
                />
                <p v-if="form.errors.nama_lengkap" class="text-sm text-red-600 mt-1">
                  {{ form.errors.nama_lengkap }}
                </p>
              </div>

              <div>
                <Label for="no_telepon">No. Telepon</Label>
                <Input
                  id="no_telepon"
                  v-model="form.no_telepon"
                  type="tel"
                  :class="{ 'border-red-500': form.errors.no_telepon }"
                />
                <p v-if="form.errors.no_telepon" class="text-sm text-red-600 mt-1">
                  {{ form.errors.no_telepon }}
                </p>
              </div>
            </div>

            <!-- Password -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Password</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="password">Password *</Label>
                  <Input
                    id="password"
                    v-model="form.password"
                    type="password"
                    required
                    :class="{ 'border-red-500': form.errors.password }"
                  />
                  <p v-if="form.errors.password" class="text-sm text-red-600 mt-1">
                    {{ form.errors.password }}
                  </p>
                </div>

                <div>
                  <Label for="password_confirmation">Konfirmasi Password *</Label>
                  <Input
                    id="password_confirmation"
                    v-model="form.password_confirmation"
                    type="password"
                    required
                    :class="{ 'border-red-500': form.errors.password_confirmation }"
                  />
                  <p v-if="form.errors.password_confirmation" class="text-sm text-red-600 mt-1">
                    {{ form.errors.password_confirmation }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Role and Access -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Role dan Akses</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="role">Role *</Label>
                  <Select v-model="form.role" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.role }">
                      <SelectValue placeholder="Pilih Role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="(label, value) in roles" :key="value" :value="value">
                        {{ label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.role" class="text-sm text-red-600 mt-1">
                    {{ form.errors.role }}
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="non_aktif">Non Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                </div>
              </div>

              <div v-if="form.role === 'admin_daerah' || form.role === 'dewan_hakim'">
                <Label for="id_wilayah">Wilayah *</Label>
                <Select v-model="form.id_wilayah" required>
                  <SelectTrigger :class="{ 'border-red-500': form.errors.id_wilayah }">
                    <SelectValue placeholder="Pilih Wilayah" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem v-for="w in wilayah" :key="w.id_wilayah" :value="w.id_wilayah.toString()">
                      {{ w.nama_wilayah }}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.id_wilayah" class="text-sm text-red-600 mt-1">
                  {{ form.errors.id_wilayah }}
                </p>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.users.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan User
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen User', href: '/admin/users' },
  { title: 'Tambah User', href: '/admin/users/create' }
]

const props = defineProps<{
  wilayah: Array<{
    id_wilayah: number
    nama_wilayah: string
  }>
  roles: Record<string, string>
}>()

const form = useForm({
  username: '',
  email: '',
  password: '',
  password_confirmation: '',
  role: '',
  nama_lengkap: '',
  no_telepon: '',
  id_wilayah: '',
  status: 'aktif'
})

// Clear wilayah when role changes to admin
watch(() => form.role, (newRole) => {
  if (newRole === 'admin') {
    form.id_wilayah = ''
  }
})

const submit = () => {
  form.post(route('admin.users.store'), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}
</script>
