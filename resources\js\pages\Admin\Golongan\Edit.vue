<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Edit Golongan" />
    <Heading :title="`Edit Golongan: ${golongan.nama_golongan}`" />

    <div class="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Edit Informasi Golongan</CardTitle>
          <CardDescription>
            Perbarui informasi golongan di bawah ini
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Basic Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Dasar</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="kode_golongan">Kode Golongan *</Label>
                  <Input
                    id="kode_golongan"
                    v-model="form.kode_golongan"
                    type="text"
                    required
                    placeholder="Contoh: TIL-1Pa, TAH-5Pi"
                    :class="{ 'border-red-500': form.errors.kode_golongan }"
                  />
                  <p v-if="form.errors.kode_golongan" class="text-sm text-red-600 mt-1">
                    {{ form.errors.kode_golongan }}
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="non_aktif">Non Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="nama_golongan">Nama Golongan *</Label>
                <Input
                  id="nama_golongan"
                  v-model="form.nama_golongan"
                  type="text"
                  required
                  placeholder="Contoh: Tilawah 1 Putra, Tahfidz 5 Juz Putri"
                  :class="{ 'border-red-500': form.errors.nama_golongan }"
                />
                <p v-if="form.errors.nama_golongan" class="text-sm text-red-600 mt-1">
                  {{ form.errors.nama_golongan }}
                </p>
              </div>

              <div>
                <Label for="id_cabang">Cabang Lomba *</Label>
                <Select v-model="form.id_cabang" required>
                  <SelectTrigger :class="{ 'border-red-500': form.errors.id_cabang }">
                    <SelectValue placeholder="Pilih Cabang Lomba" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem v-for="cabang in cabangLomba" :key="cabang.id_cabang" :value="cabang.id_cabang.toString()">
                      {{ cabang.nama_cabang }} ({{ cabang.kode_cabang }})
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.id_cabang" class="text-sm text-red-600 mt-1">
                  {{ form.errors.id_cabang }}
                </p>
              </div>
            </div>

            <!-- Criteria -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Kriteria Peserta</h3>
              
              <div>
                <Label for="jenis_kelamin">Jenis Kelamin *</Label>
                <Select v-model="form.jenis_kelamin" required>
                  <SelectTrigger :class="{ 'border-red-500': form.errors.jenis_kelamin }">
                    <SelectValue placeholder="Pilih Jenis Kelamin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="L">Laki-laki</SelectItem>
                    <SelectItem value="P">Perempuan</SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.jenis_kelamin" class="text-sm text-red-600 mt-1">
                  {{ form.errors.jenis_kelamin }}
                </p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="batas_umur_min">Batas Umur Minimum *</Label>
                  <Input
                    id="batas_umur_min"
                    v-model="form.batas_umur_min"
                    type="number"
                    required
                    min="1"
                    max="100"
                    placeholder="Contoh: 7"
                    :class="{ 'border-red-500': form.errors.batas_umur_min }"
                  />
                  <p v-if="form.errors.batas_umur_min" class="text-sm text-red-600 mt-1">
                    {{ form.errors.batas_umur_min }}
                  </p>
                </div>

                <div>
                  <Label for="batas_umur_max">Batas Umur Maksimum *</Label>
                  <Input
                    id="batas_umur_max"
                    v-model="form.batas_umur_max"
                    type="number"
                    required
                    min="1"
                    max="100"
                    placeholder="Contoh: 12"
                    :class="{ 'border-red-500': form.errors.batas_umur_max }"
                  />
                  <p v-if="form.errors.batas_umur_max" class="text-sm text-red-600 mt-1">
                    {{ form.errors.batas_umur_max }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Competition Settings -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Pengaturan Lomba</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="kuota_max">Kuota Maksimum *</Label>
                  <Input
                    id="kuota_max"
                    v-model="form.kuota_max"
                    type="number"
                    required
                    min="1"
                    placeholder="Contoh: 50"
                    :class="{ 'border-red-500': form.errors.kuota_max }"
                  />
                  <p v-if="form.errors.kuota_max" class="text-sm text-red-600 mt-1">
                    {{ form.errors.kuota_max }}
                  </p>
                </div>

                <div>
                  <Label for="biaya_pendaftaran">Biaya Pendaftaran *</Label>
                  <Input
                    id="biaya_pendaftaran"
                    v-model="form.biaya_pendaftaran"
                    type="number"
                    required
                    min="0"
                    step="1000"
                    placeholder="Contoh: 150000"
                    :class="{ 'border-red-500': form.errors.biaya_pendaftaran }"
                  />
                  <p v-if="form.errors.biaya_pendaftaran" class="text-sm text-red-600 mt-1">
                    {{ form.errors.biaya_pendaftaran }}
                  </p>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="nomor_urut_awal">Nomor Urut Awal</Label>
                  <Input
                    id="nomor_urut_awal"
                    v-model="form.nomor_urut_awal"
                    type="number"
                    min="1"
                    placeholder="Default: 1"
                    :class="{ 'border-red-500': form.errors.nomor_urut_awal }"
                  />
                  <p v-if="form.errors.nomor_urut_awal" class="text-sm text-red-600 mt-1">
                    {{ form.errors.nomor_urut_awal }}
                  </p>
                </div>

                <div>
                  <Label for="nomor_urut_akhir">Nomor Urut Akhir</Label>
                  <Input
                    id="nomor_urut_akhir"
                    v-model="form.nomor_urut_akhir"
                    type="number"
                    min="1"
                    placeholder="Default: 999"
                    :class="{ 'border-red-500': form.errors.nomor_urut_akhir }"
                  />
                  <p v-if="form.errors.nomor_urut_akhir" class="text-sm text-red-600 mt-1">
                    {{ form.errors.nomor_urut_akhir }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Current Info -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">Informasi Saat Ini</h4>
              <div class="text-sm text-gray-600 space-y-1">
                <p><strong>Dibuat:</strong> {{ formatDate(golongan.created_at) }}</p>
                <p><strong>Diperbarui:</strong> {{ formatDate(golongan.updated_at) }}</p>
                <p><strong>Jumlah Pendaftaran:</strong> {{ golongan.pendaftaran?.length || 0 }} pendaftaran</p>
              </div>
            </div>

            <!-- Warning for changes -->
            <div v-if="hasPendaftaran" class="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
              <div class="flex">
                <Icon name="alert-triangle" class="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
                <div>
                  <h4 class="font-medium text-yellow-800">Perhatian</h4>
                  <p class="text-sm text-yellow-700 mt-1">
                    Golongan ini memiliki {{ golongan.pendaftaran?.length || 0 }} pendaftaran. 
                    Perubahan kriteria dapat mempengaruhi peserta yang sudah terdaftar.
                  </p>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.golongan.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan Perubahan
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface CabangLomba {
  id_cabang: number
  kode_cabang: string
  nama_cabang: string
}

interface Golongan {
  id_golongan: number
  kode_golongan: string
  nama_golongan: string
  id_cabang: number
  jenis_kelamin: string
  batas_umur_min: number
  batas_umur_max: number
  kuota_max: number
  biaya_pendaftaran: number
  nomor_urut_awal: number
  nomor_urut_akhir: number
  status: string
  created_at: string
  updated_at: string
  pendaftaran?: any[]
}

const props = defineProps<{
  golongan: Golongan
  cabangLomba: CabangLomba[]
}>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Golongan', href: '/admin/golongan' },
  { title: 'Edit Golongan', href: `/admin/golongan/${props.golongan.id_golongan}/edit` }
]

const form = useForm({
  kode_golongan: props.golongan.kode_golongan,
  nama_golongan: props.golongan.nama_golongan,
  id_cabang: props.golongan.id_cabang.toString(),
  jenis_kelamin: props.golongan.jenis_kelamin,
  batas_umur_min: props.golongan.batas_umur_min.toString(),
  batas_umur_max: props.golongan.batas_umur_max.toString(),
  kuota_max: props.golongan.kuota_max.toString(),
  biaya_pendaftaran: props.golongan.biaya_pendaftaran.toString(),
  nomor_urut_awal: props.golongan.nomor_urut_awal.toString(),
  nomor_urut_akhir: props.golongan.nomor_urut_akhir.toString(),
  status: props.golongan.status
})

const hasPendaftaran = computed(() => {
  return props.golongan.pendaftaran && props.golongan.pendaftaran.length > 0
})

const submit = () => {
  form.put(route('admin.golongan.update', props.golongan.id_golongan), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
