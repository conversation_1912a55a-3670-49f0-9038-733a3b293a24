[2025-07-14 05:14:07] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran (Connection: sqlite, SQL: insert into "pendaftaran" ("id_peserta", "id_golongan", "status_pendaftaran", "tanggal_daftar", "updated_at", "created_at") values (65, 44, submitted, 2025-07-14 05:14:06, 2025-07-14 05:14:06, 2025-07-14 05:14:06)) {"userId":3,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran (Connection: sqlite, SQL: insert into \"pendaftaran\" (\"id_peserta\", \"id_golongan\", \"status_pendaftaran\", \"tanggal_daftar\", \"updated_at\", \"created_at\") values (65, 44, submitted, 2025-07-14 05:14:06, 2025-07-14 05:14:06, 2025-07-14 05:14:06)) at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pendaftaran')
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pendaftaran')
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pendaftaran))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pendaftaran), Object(Closure))
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(125): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(116): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#79 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"pe...', Array)
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pendaftaran')
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pendaftaran')
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pendaftaran))
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pendaftaran), Object(Closure))
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(125): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(116): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#77 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#79 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#80 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#81 {main}
"} 
[2025-07-14 05:14:24] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran (Connection: sqlite, SQL: insert into "pendaftaran" ("id_peserta", "id_golongan", "status_pendaftaran", "tanggal_daftar", "updated_at", "created_at") values (65, 44, submitted, 2025-07-14 05:14:24, 2025-07-14 05:14:24, 2025-07-14 05:14:24)) {"userId":3,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran (Connection: sqlite, SQL: insert into \"pendaftaran\" (\"id_peserta\", \"id_golongan\", \"status_pendaftaran\", \"tanggal_daftar\", \"updated_at\", \"created_at\") values (65, 44, submitted, 2025-07-14 05:14:24, 2025-07-14 05:14:24, 2025-07-14 05:14:24)) at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pendaftaran')
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pendaftaran')
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pendaftaran))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pendaftaran), Object(Closure))
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(125): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(116): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#79 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"pe...', Array)
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pendaftaran')
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pendaftaran')
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pendaftaran))
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pendaftaran), Object(Closure))
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(125): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(116): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#77 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#79 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#80 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#81 {main}
"} 
[2025-07-14 05:53:49] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran (Connection: sqlite, SQL: insert into "pendaftaran" ("id_peserta", "id_golongan", "status_pendaftaran", "tanggal_daftar", "updated_at", "created_at") values (65, 44, submitted, 2025-07-14 05:53:49, 2025-07-14 05:53:49, 2025-07-14 05:53:49)) {"userId":3,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran (Connection: sqlite, SQL: insert into \"pendaftaran\" (\"id_peserta\", \"id_golongan\", \"status_pendaftaran\", \"tanggal_daftar\", \"updated_at\", \"created_at\") values (65, 44, submitted, 2025-07-14 05:53:49, 2025-07-14 05:53:49, 2025-07-14 05:53:49)) at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pendaftaran')
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pendaftaran')
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pendaftaran))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pendaftaran), Object(Closure))
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(125): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(116): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#79 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pendaftaran.nomor_pendaftaran at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"pe...', Array)
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pendaftaran')
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pendaftaran')
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pendaftaran))
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pendaftaran), Object(Closure))
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(125): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(116): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#77 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#79 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#80 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#81 {main}
"} 
[2025-07-14 05:59:27] local.ERROR: Call to undefined method App\Http\Controllers\Peserta\PendaftaranController::edit() {"userId":6,"exception":"[object] (Error(code: 0): Call to undefined method App\\Http\\Controllers\\Peserta\\PendaftaranController::edit() at D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46)
[stacktrace]
#0 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Peserta\\PendaftaranController), 'edit')
#1 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#2 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#3 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#4 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'peserta')
#6 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#57 D:\\emtqlampung-v2\\emtqlampung-pc-kantor\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#58 {main}
"} 
[2025-07-14 07:07:33] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pembayaran.nomor_transaksi (Connection: sqlite, SQL: insert into "pembayaran" ("id_pendaftaran", "jumlah_bayar", "metode_pembayaran", "status_pembayaran", "tanggal_bayar", "updated_at", "created_at") values (2, 0.00, transfer, pending, 2025-07-14 07:07:33, 2025-07-14 07:07:33, 2025-07-14 07:07:33)) {"userId":3,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pembayaran.nomor_transaksi (Connection: sqlite, SQL: insert into \"pembayaran\" (\"id_pendaftaran\", \"jumlah_bayar\", \"metode_pembayaran\", \"status_pembayaran\", \"tanggal_bayar\", \"updated_at\", \"created_at\") values (2, 0.00, transfer, pending, 2025-07-14 07:07:33, 2025-07-14 07:07:33, 2025-07-14 07:07:33)) at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pembayaran')
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pembayaran')
#6 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pembayaran))
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pembayaran), Object(Closure))
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(144): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#19 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(117): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 D:\\emtqlampung-v2\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#79 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: pembayaran.nomor_transaksi at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"pe...', Array)
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#6 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id_pembayaran')
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id_pembayaran')
#8 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Pembayaran))
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Pembayaran), Object(Closure))
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(144): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->App\\Http\\Controllers\\AdminDaerah\\{closure}(Object(Illuminate\\Database\\SQLiteConnection))
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->transaction(Object(Closure))
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('transaction', Array)
#21 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Controllers\\AdminDaerah\\PendaftaranController.php(117): Illuminate\\Support\\Facades\\Facade::__callStatic('transaction', Array)
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AdminDaerah\\PendaftaranController->store(Object(Illuminate\\Http\\Request))
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminDaerah\\PendaftaranController), 'store')
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin_daerah')
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#45 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#54 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#77 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#78 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#79 D:\\emtqlampung-v2\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#80 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#81 {main}
"} 
[2025-07-15 01:34:23] local.ERROR: Call to undefined method App\Http\Controllers\Admin\PendaftaranController::store() {"userId":1,"exception":"[object] (Error(code: 0): Call to undefined method App\\Http\\Controllers\\Admin\\PendaftaranController::store() at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\PendaftaranController), 'store')
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#4 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Middleware\\RoleMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'superadmin', 'admin')
#6 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\emtqlampung-v2\\mtq-lampung\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 D:\\emtqlampung-v2\\mtq-lampung\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#57 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\emtqlampung-...')
#58 {main}
"} 
[2025-07-15 02:31:33] local.ERROR: Trait "HasVerificationPermissions" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"HasVerificationPermissions\" not found at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\psy\\psysh\\src\\ExecutionClosure.php(40) : eval()'d code:1)
[stacktrace]
#0 {main}
"} 
[2025-07-15 06:18:56] local.ERROR: SQLSTATE[HY000]: General error: 1 error in index peserta_status_peserta_index after drop column: no such column: status_peserta (Connection: sqlite, SQL: alter table "peserta" drop column "status_peserta") {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 error in index peserta_status_peserta_index after drop column: no such column: status_peserta (Connection: sqlite, SQL: alter table \"peserta\" drop column \"status_peserta\") at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table \"pe...', Array, Object(Closure))
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table \"pe...', Array, Object(Closure))
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table \"pe...')
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('peserta', Object(Closure))
#6 D:\\emtqlampung-v2\\mtq-lampung\\database\\migrations\\2025_07_15_000001_enhance_verification_workflow_schema.php(52): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_15_0000...', Object(Closure))
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_15_0000...', Object(Closure))
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\emtqlampung-...', 4, false)
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\emtqlampung-v2\\mtq-lampung\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 error in index peserta_status_peserta_index after drop column: no such column: status_peserta at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table \"pe...', Array)
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table \"pe...', Array, Object(Closure))
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table \"pe...', Array, Object(Closure))
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table \"pe...')
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('peserta', Object(Closure))
#8 D:\\emtqlampung-v2\\mtq-lampung\\database\\migrations\\2025_07_15_000001_enhance_verification_workflow_schema.php(52): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_15_0000...', Object(Closure))
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_15_0000...', Object(Closure))
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\emtqlampung-...', 4, false)
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\emtqlampung-v2\\mtq-lampung\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-15 06:19:35] local.ERROR: SQLSTATE[HY000]: General error: 1 duplicate column name: regional_verified_by (Connection: sqlite, SQL: alter table "peserta" add column "regional_verified_by" integer) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 duplicate column name: regional_verified_by (Connection: sqlite, SQL: alter table \"peserta\" add column \"regional_verified_by\" integer) at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table \"pe...', Array, Object(Closure))
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table \"pe...', Array, Object(Closure))
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table \"pe...')
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('peserta', Object(Closure))
#6 D:\\emtqlampung-v2\\mtq-lampung\\database\\migrations\\2025_07_15_000001_enhance_verification_workflow_schema.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_15_0000...', Object(Closure))
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_15_0000...', Object(Closure))
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\emtqlampung-...', 4, false)
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\emtqlampung-v2\\mtq-lampung\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 duplicate column name: regional_verified_by at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('alter table \"pe...')
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table \"pe...', Array)
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table \"pe...', Array, Object(Closure))
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table \"pe...', Array, Object(Closure))
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table \"pe...')
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('peserta', Object(Closure))
#8 D:\\emtqlampung-v2\\mtq-lampung\\database\\migrations\\2025_07_15_000001_enhance_verification_workflow_schema.php(15): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_15_0000...', Object(Closure))
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_15_0000...', Object(Closure))
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\emtqlampung-...', 4, false)
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\emtqlampung-v2\\mtq-lampung\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-15 06:19:58] local.ERROR: The "--table" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--table\" option does not exist. at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Input\\ArgvInput.php(153): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('table', 'peserta')
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--table=peserta')
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--table=peserta', true)
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Command\\Command.php(276): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\emtqlampung-v2\\mtq-lampung\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-07-15 06:20:44] local.ERROR: Method Illuminate\Database\SQLiteConnection::getDoctrineSchemaManager does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method Illuminate\\Database\\SQLiteConnection::getDoctrineSchemaManager does not exist. at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php:115)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\database\\migrations\\2025_07_15_000002_enhance_verification_workflow_schema.php(40): Illuminate\\Database\\Connection->__call('getDoctrineSche...', Array)
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(107): Illuminate\\Database\\Migrations\\Migration@anonymous->{closure}(Object(Illuminate\\Database\\Schema\\Blueprint))
#2 [internal function]: Illuminate\\Database\\Schema\\Blueprint->__construct(Object(Illuminate\\Database\\SQLiteConnection), 'peserta', Object(Closure))
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Data...')
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Data...', Array, true)
#6 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Data...', Array)
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Data...', Array)
#8 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(636): Illuminate\\Foundation\\Application->make('Illuminate\\\\Data...', Array)
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->createBlueprint('peserta', Object(Closure))
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('peserta', Object(Closure))
#11 D:\\emtqlampung-v2\\mtq-lampung\\database\\migrations\\2025_07_15_000002_enhance_verification_workflow_schema.php(38): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_15_0000...', Object(Closure))
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_15_0000...', Object(Closure))
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\emtqlampung-...', 4, false)
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 D:\\emtqlampung-v2\\mtq-lampung\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#39 {main}
"} 
[2025-07-15 06:21:48] local.ERROR: SQLSTATE[HY000]: General error: 1 index peserta_regional_verification_status_index already exists (Connection: sqlite, SQL: create index "peserta_regional_verification_status_index" on "peserta" ("regional_verification_status")) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 index peserta_regional_verification_status_index already exists (Connection: sqlite, SQL: create index \"peserta_regional_verification_status_index\" on \"peserta\" (\"regional_verification_status\")) at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create index \"p...', Array, Object(Closure))
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create index \"p...', Array, Object(Closure))
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create index \"p...')
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('peserta', Object(Closure))
#6 D:\\emtqlampung-v2\\mtq-lampung\\database\\migrations\\2025_07_15_000002_enhance_verification_workflow_schema.php(38): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_15_0000...', Object(Closure))
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_15_0000...', Object(Closure))
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\emtqlampung-...', 4, false)
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\emtqlampung-v2\\mtq-lampung\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 index peserta_regional_verification_status_index already exists at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create index \"p...')
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create index \"p...', Array)
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create index \"p...', Array, Object(Closure))
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create index \"p...', Array, Object(Closure))
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create index \"p...')
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('peserta', Object(Closure))
#8 D:\\emtqlampung-v2\\mtq-lampung\\database\\migrations\\2025_07_15_000002_enhance_verification_workflow_schema.php(38): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_15_0000...', Object(Closure))
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_15_0000...', Object(Closure))
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\emtqlampung-...', 4, false)
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\emtqlampung-v2\\mtq-lampung\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-15 06:21:57] local.ERROR: SQLSTATE[HY000]: General error: 1 error in index dokumen_peserta_document_type_id_index after drop column: no such column: document_type_id (Connection: sqlite, SQL: alter table "dokumen_peserta" drop column "document_type_id") {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 error in index dokumen_peserta_document_type_id_index after drop column: no such column: document_type_id (Connection: sqlite, SQL: alter table \"dokumen_peserta\" drop column \"document_type_id\") at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table \"do...', Array, Object(Closure))
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table \"do...', Array, Object(Closure))
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table \"do...')
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('dokumen_peserta', Object(Closure))
#6 D:\\emtqlampung-v2\\mtq-lampung\\database\\migrations\\2025_07_14_082853_add_document_type_id_to_dokumen_peserta_table.php(31): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#8 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_14_0828...', Object(Closure))
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_14_0828...', Object(Closure))
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(337): Illuminate\\Database\\Migrations\\Migrator->runDown('D:\\\\emtqlampung-...', Object(stdClass), false)
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(281): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations(Array, Array, Array)
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RollbackCommand.php(63): Illuminate\\Database\\Migrations\\Migrator->rollback(Array, Array)
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\RollbackCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RollbackCommand.php(62): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RollbackCommand->handle()
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RollbackCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\emtqlampung-v2\\mtq-lampung\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#33 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 error in index dokumen_peserta_document_type_id_index after drop column: no such column: document_type_id at D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table \"do...', Array)
#2 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table \"do...', Array, Object(Closure))
#3 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table \"do...', Array, Object(Closure))
#4 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table \"do...')
#5 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('dokumen_peserta', Object(Closure))
#8 D:\\emtqlampung-v2\\mtq-lampung\\database\\migrations\\2025_07_14_082853_add_document_type_id_to_dokumen_peserta_table.php(31): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#10 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#11 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#13 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_14_0828...', Object(Closure))
#15 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_14_0828...', Object(Closure))
#16 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(337): Illuminate\\Database\\Migrations\\Migrator->runDown('D:\\\\emtqlampung-...', Object(stdClass), false)
#17 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(281): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations(Array, Array, Array)
#18 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RollbackCommand.php(63): Illuminate\\Database\\Migrations\\Migrator->rollback(Array, Array)
#19 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\RollbackCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RollbackCommand.php(62): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RollbackCommand->handle()
#22 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RollbackCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\emtqlampung-v2\\mtq-lampung\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\emtqlampung-v2\\mtq-lampung\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#35 {main}
"} 
[2025-07-15 06:42:06] local.ERROR: Cannot redeclare App\Models\Peserta::scopeVisibleToProvincialAdmin() {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot redeclare App\\Models\\Peserta::scopeVisibleToProvincialAdmin() at D:\\emtqlampung-v2\\mtq-lampung\\app\\Models\\Peserta.php:154)
[stacktrace]
#0 {main}
"} 
[2025-07-15 06:42:09] local.ERROR: Cannot redeclare App\Models\Peserta::scopeVisibleToProvincialAdmin() {"userId":3,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot redeclare App\\Models\\Peserta::scopeVisibleToProvincialAdmin() at D:\\emtqlampung-v2\\mtq-lampung\\app\\Models\\Peserta.php:154)
[stacktrace]
#0 {main}
"} 
[2025-07-15 06:42:32] local.ERROR: Cannot redeclare App\Models\Peserta::scopeVisibleToProvincialAdmin() {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot redeclare App\\Models\\Peserta::scopeVisibleToProvincialAdmin() at D:\\emtqlampung-v2\\mtq-lampung\\app\\Models\\Peserta.php:164)
[stacktrace]
#0 {main}
"} 
[2025-07-15 06:42:32] local.ERROR: Cannot redeclare App\Models\Peserta::scopeVisibleToProvincialAdmin() {"userId":3,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot redeclare App\\Models\\Peserta::scopeVisibleToProvincialAdmin() at D:\\emtqlampung-v2\\mtq-lampung\\app\\Models\\Peserta.php:164)
[stacktrace]
#0 {main}
"} 
[2025-07-15 06:43:07] local.ERROR: Cannot redeclare App\Models\Pendaftaran::scopeVisibleToProvincialAdmin() {"userId":3,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot redeclare App\\Models\\Pendaftaran::scopeVisibleToProvincialAdmin() at D:\\emtqlampung-v2\\mtq-lampung\\app\\Models\\Pendaftaran.php:172)
[stacktrace]
#0 {main}
"} 
[2025-07-15 06:43:07] local.ERROR: Cannot redeclare App\Models\Pendaftaran::scopeVisibleToProvincialAdmin() {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot redeclare App\\Models\\Pendaftaran::scopeVisibleToProvincialAdmin() at D:\\emtqlampung-v2\\mtq-lampung\\app\\Models\\Pendaftaran.php:172)
[stacktrace]
#0 {main}
"} 
[2025-07-15 06:43:28] local.ERROR: Cannot redeclare App\Models\Pendaftaran::scopeVisibleToProvincialAdmin() {"userId":3,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot redeclare App\\Models\\Pendaftaran::scopeVisibleToProvincialAdmin() at D:\\emtqlampung-v2\\mtq-lampung\\app\\Models\\Pendaftaran.php:171)
[stacktrace]
#0 {main}
"} 
[2025-07-15 06:43:28] local.ERROR: Cannot redeclare App\Models\Pendaftaran::scopeVisibleToProvincialAdmin() {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot redeclare App\\Models\\Pendaftaran::scopeVisibleToProvincialAdmin() at D:\\emtqlampung-v2\\mtq-lampung\\app\\Models\\Pendaftaran.php:171)
[stacktrace]
#0 {main}
"} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":1,"participant_name":"Pink Hill","documents_complete":false} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":2,"participant_name":"Johann Schinner PhD","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":3,"participant_name":"Prof. Jensen Donnelly MD","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":4,"participant_name":"Mrs. Keely Rowe","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":5,"participant_name":"Ludwig Hackett","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":6,"participant_name":"Dr. Veda Towne III","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":7,"participant_name":"Eloy Rodriguez","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":8,"participant_name":"Prof. Christina Brown DDS","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":9,"participant_name":"Mr. Frederick Becker","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":10,"participant_name":"Prof. Riley Ullrich","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":11,"participant_name":"Jamir Langworth MD","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":12,"participant_name":"Orion Johnson Jr.","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":13,"participant_name":"Prof. Lue Barton","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":14,"participant_name":"Myron Conroy","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":15,"participant_name":"Mattie Armstrong","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":16,"participant_name":"Dwight Kemmer","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":17,"participant_name":"Mr. Jayden Huels","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":18,"participant_name":"Chauncey Mohr DDS","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":19,"participant_name":"Miss Euna Bauch V","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":20,"participant_name":"Marlee McDermott","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":21,"participant_name":"Bessie Jacobi","documents_complete":false} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":22,"participant_name":"Priscilla Wiza","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":23,"participant_name":"Ms. Jennifer Franecki III","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":24,"participant_name":"Chelsey Murphy Jr.","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":25,"participant_name":"Krista Zboncak DVM","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":26,"participant_name":"Keegan Gaylord PhD","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":27,"participant_name":"Darlene Buckridge","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":28,"participant_name":"Jade Frami","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":29,"participant_name":"Mr. Mateo Christiansen Sr.","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":30,"participant_name":"Anabel McLaughlin Jr.","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":31,"participant_name":"Sid Waelchi","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":32,"participant_name":"Dr. Adolphus Grimes","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":33,"participant_name":"Mrs. Ashlee Ferry PhD","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":34,"participant_name":"Miss April Borer","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":35,"participant_name":"Justus Lang","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":36,"participant_name":"Sonya Lind","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":37,"participant_name":"Cielo Abernathy Jr.","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":38,"participant_name":"Dena Roberts","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":39,"participant_name":"Mr. Lonny Ratke Sr.","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":40,"participant_name":"Dejon Hettinger","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":41,"participant_name":"Jackie Rippin","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":42,"participant_name":"Francisco Mertz","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":43,"participant_name":"Lorna Harris DVM","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":44,"participant_name":"Theresia Cole Sr.","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":45,"participant_name":"Dr. Jordi Cummings","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":46,"participant_name":"Eden Crooks","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":47,"participant_name":"Stephania Hirthe Sr.","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":48,"participant_name":"Marco Keebler Jr.","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":49,"participant_name":"Gudrun Keebler","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":50,"participant_name":"Vito Stoltenberg","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":51,"participant_name":"Prof. Selena Batz","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":52,"participant_name":"Grady Sawayn","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":53,"participant_name":"Miss Freida Stehr","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":54,"participant_name":"Miss Taryn Shanahan","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":55,"participant_name":"Elise Fritsch IV","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":56,"participant_name":"Donavon Hodkiewicz","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":57,"participant_name":"Morton Hansen","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":58,"participant_name":"Tyrese Franecki","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":59,"participant_name":"April Skiles","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":60,"participant_name":"Prof. Jaime Wolff PhD","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":61,"participant_name":"Glenda Feest","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":62,"participant_name":"Dr. Golda Renner MD","documents_complete":false} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":63,"participant_name":"Dr. Anahi Ullrich MD","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":64,"participant_name":"Miss Lily Hessel IV","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":65,"participant_name":"Alvera Kutch","documents_complete":false} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":66,"participant_name":"Talon Will","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":67,"participant_name":"Ena Haag PhD","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":68,"participant_name":"Aiyana Mueller","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":69,"participant_name":"Mr. Braulio Halvorson","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":70,"participant_name":"Sally Lubowitz Sr.","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":71,"participant_name":"Salvador Kreiger","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":72,"participant_name":"Adella Parisian","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":73,"participant_name":"Lyla Crist V","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":74,"participant_name":"Prof. Kieran Hermann IV","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":75,"participant_name":"Casey Bergnaum","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":76,"participant_name":"Prof. Alvah Schaden","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":77,"participant_name":"Ms. Bianka Brakus","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":78,"participant_name":"Letitia Gutkowski","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":79,"participant_name":"Orin Ratke","documents_complete":true} 
[2025-07-15 06:51:18] local.INFO: Document completion status updated {"participant_id":80,"participant_name":"Cordell Spinka V","documents_complete":true} 
