<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class VerificationType extends Model
{
    protected $table = 'verification_types';
    protected $primaryKey = 'id_verification_type';

    protected $fillable = [
        'name',
        'code',
        'description',
        'is_active',
        'is_required',
        'settings',
        'sort_order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_required' => 'boolean',
        'settings' => 'array',
        'sort_order' => 'integer'
    ];

    // Relationships
    public function participantVerifications(): HasMany
    {
        return $this->hasMany(ParticipantVerification::class, 'verification_type_id', 'id_verification_type');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Helper methods
    public function getSetting(string $key, $default = null)
    {
        return $this->settings[$key] ?? $default;
    }

    public function setSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        $settings[$key] = $value;
        $this->settings = $settings;
    }
}
