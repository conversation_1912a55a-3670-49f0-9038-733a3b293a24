<script setup lang="ts">
import { useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import InputError from '@/components/InputError.vue'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'

interface User {
  username: string
  email: string
}

interface Peserta {
  id_peserta: number
  nama_lengkap: string
  tempat_lahir: string
  tanggal_lahir: string
  alamat: string
  no_telepon: string | null
  status_peserta: string
  keterangan: string | null
  user: User
}

const props = defineProps<{
  peserta: Peserta
}>()

const form = useForm({
  nama_lengkap: props.peserta.nama_lengkap,
  email: props.peserta.user.email,
  tempat_lahir: props.peserta.tempat_lahir,
  tanggal_lahir: props.peserta.tanggal_lahir,
  alamat: props.peserta.alamat,
  no_telepon: props.peserta.no_telepon || '',
  status_peserta: props.peserta.status_peserta,
  keterangan: props.peserta.keterangan || ''
})

function submit() {
  form.put(route('admin-daerah.peserta.update', props.peserta.id_peserta))
}
</script>

<template>
  <AppLayout>
    <template #header>
      <div class="flex items-center space-x-4">
        <Button
          as-child
          variant="ghost"
          size="sm"
        >
          <TextLink :href="route('admin-daerah.peserta.show', peserta.id_peserta)">
            <Icon name="arrow-left" class="w-4 h-4 mr-2" />
            Kembali
          </TextLink>
        </Button>
        <Heading title="Edit Peserta" />
      </div>
    </template>

    <Head title="Edit Peserta" />

    <div class="space-y-6">
      <form @submit.prevent="submit" class="space-y-6">
        <!-- Personal Information -->
        <Card>
          <CardHeader>
            <CardTitle>Data Pribadi</CardTitle>
            <CardDescription>Perbarui informasi pribadi peserta</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid gap-2">
              <Label for="nama_lengkap">Nama Lengkap</Label>
              <Input
                id="nama_lengkap"
                v-model="form.nama_lengkap"
                placeholder="Nama lengkap peserta"
                required
              />
              <InputError :message="form.errors.nama_lengkap" />
            </div>

            <div class="grid gap-2">
              <Label for="email">Email</Label>
              <Input
                id="email"
                type="email"
                v-model="form.email"
                placeholder="<EMAIL>"
                required
              />
              <InputError :message="form.errors.email" />
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="grid gap-2">
                <Label for="tempat_lahir">Tempat Lahir</Label>
                <Input
                  id="tempat_lahir"
                  v-model="form.tempat_lahir"
                  placeholder="Tempat lahir"
                  required
                />
                <InputError :message="form.errors.tempat_lahir" />
              </div>

              <div class="grid gap-2">
                <Label for="tanggal_lahir">Tanggal Lahir</Label>
                <Input
                  id="tanggal_lahir"
                  type="date"
                  v-model="form.tanggal_lahir"
                  required
                />
                <InputError :message="form.errors.tanggal_lahir" />
              </div>
            </div>

            <div class="grid gap-2">
              <Label for="alamat">Alamat</Label>
              <Textarea
                id="alamat"
                v-model="form.alamat"
                placeholder="Alamat lengkap"
                rows="3"
                required
              />
              <InputError :message="form.errors.alamat" />
            </div>

            <div class="grid gap-2">
              <Label for="no_telepon">No. Telepon (Opsional)</Label>
              <Input
                id="no_telepon"
                type="tel"
                v-model="form.no_telepon"
                placeholder="Nomor telepon"
              />
              <InputError :message="form.errors.no_telepon" />
            </div>
          </CardContent>
        </Card>

        <!-- Status and Notes -->
        <Card>
          <CardHeader>
            <CardTitle>Status dan Catatan</CardTitle>
            <CardDescription>Kelola status peserta dan tambahkan catatan</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid gap-2">
              <Label for="status_peserta">Status Peserta</Label>
              <select
                id="status_peserta"
                v-model="form.status_peserta"
                required
                class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="draft">Draft</option>
                <option value="submitted">Disubmit</option>
                <option value="verified">Terverifikasi</option>
                <option value="approved">Disetujui</option>
                <option value="rejected">Ditolak</option>
              </select>
              <InputError :message="form.errors.status_peserta" />
            </div>

            <div class="grid gap-2">
              <Label for="keterangan">Keterangan (Opsional)</Label>
              <Textarea
                id="keterangan"
                v-model="form.keterangan"
                placeholder="Tambahkan catatan atau keterangan khusus..."
                rows="4"
              />
              <InputError :message="form.errors.keterangan" />
            </div>
          </CardContent>
        </Card>

        <!-- Account Information (Read Only) -->
        <Card>
          <CardHeader>
            <CardTitle>Informasi Akun</CardTitle>
            <CardDescription>Data akun peserta (hanya untuk referensi)</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Username</Label>
                <p class="text-sm font-mono bg-gray-50 p-2 rounded border">{{ peserta.user.username }}</p>
              </div>
              <div>
                <Label>Status Akun</Label>
                <p class="text-sm bg-gray-50 p-2 rounded border">Aktif</p>
              </div>
            </div>
            <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div class="flex items-start space-x-3">
                <Icon name="info" class="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 class="text-sm font-medium text-blue-900">Informasi</h4>
                  <p class="text-sm text-blue-700 mt-1">
                    Username tidak dapat diubah. Untuk mengubah password, peserta harus melakukan reset password melalui halaman login.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Submit Button -->
        <div class="flex justify-end space-x-4 pt-6 border-t">
          <Button
            as-child
            variant="outline"
          >
            <TextLink :href="route('admin-daerah.peserta.show', peserta.id_peserta)">
              Batal
            </TextLink>
          </Button>
          <Button
            type="submit"
            :disabled="form.processing"
          >
            <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
            {{ form.processing ? 'Menyimpan...' : 'Simpan Perubahan' }}
          </Button>
        </div>
      </form>
    </div>
  </AppLayout>
</template>
