<template>
  <div class="space-y-6">
    <!-- Verification Progress Overview -->
    <Card class="border-l-4 border-l-indigo-500">
      <CardContent class="p-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-semibold text-lg">Progress Verifikasi Data Peserta</h3>
          <Badge :variant="getOverallProgressVariant()" class="text-sm">
            {{ Math.round(overallProgress) }}% Selesai
          </Badge>
        </div>
        
        <!-- Progress Bar -->
        <div class="w-full bg-gray-200 rounded-full h-3 mb-4">
          <div 
            class="bg-indigo-600 h-3 rounded-full transition-all duration-500"
            :style="{ width: `${overallProgress}%` }"
          ></div>
        </div>
        
        <!-- Progress Categories -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div class="text-center">
            <div class="font-medium text-indigo-600">{{ verifiedFieldsCount }}</div>
            <div class="text-gray-600">Field Terverifikasi</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-green-600">{{ eligibilityChecksCount }}</div>
            <div class="text-gray-600">Cek Kelayakan</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-blue-600">{{ validationChecksCount }}</div>
            <div class="text-gray-600">Validasi Data</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-purple-600">{{ competitionChecksCount }}</div>
            <div class="text-gray-600">Cek Kompetisi</div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Data Accuracy Verification -->
    <Card>
      <CardContent class="p-4">
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-medium">Verifikasi Akurasi Data</h4>
          <Button 
            @click="runAllDataValidations"
            size="sm"
            class="bg-blue-600 hover:bg-blue-700 text-white"
            :disabled="isRunningValidations"
          >
            <Icon :name="isRunningValidations ? 'loader' : 'checkCircle'" class="w-4 h-4 mr-2" :class="{ 'animate-spin': isRunningValidations }" />
            Validasi Semua
          </Button>
        </div>

        <div class="space-y-4">
          <!-- Name Consistency Check -->
          <div class="p-4 border rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-2">
                <Icon name="user" class="w-5 h-5 text-blue-600" />
                <span class="font-medium">Konsistensi Nama</span>
              </div>
              <div class="flex items-center space-x-2">
                <Badge v-if="nameConsistencyCheck" :variant="nameConsistencyCheck.consistent ? 'default' : 'destructive'">
                  {{ nameConsistencyCheck.consistent ? 'Konsisten' : 'Tidak Konsisten' }}
                </Badge>
                <Button @click="checkNameConsistency" size="sm" variant="outline">
                  <Icon name="search" class="w-3 h-3 mr-1" />
                  Cek
                </Button>
              </div>
            </div>
            <div v-if="nameConsistencyCheck && !nameConsistencyCheck.consistent" class="text-sm text-red-600">
              <p><strong>Masalah ditemukan:</strong></p>
              <ul class="list-disc list-inside mt-1">
                <li v-for="issue in nameConsistencyCheck.issues" :key="issue">{{ issue }}</li>
              </ul>
            </div>
          </div>

          <!-- Birth Date Validation -->
          <div class="p-4 border rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-2">
                <Icon name="calendar" class="w-5 h-5 text-green-600" />
                <span class="font-medium">Validasi Tanggal Lahir</span>
              </div>
              <div class="flex items-center space-x-2">
                <Badge v-if="birthDateValidation" :variant="birthDateValidation.valid ? 'default' : 'destructive'">
                  {{ birthDateValidation.valid ? 'Valid' : 'Tidak Valid' }}
                </Badge>
                <Button @click="validateBirthDate" size="sm" variant="outline">
                  <Icon name="calendar" class="w-3 h-3 mr-1" />
                  Validasi
                </Button>
              </div>
            </div>
            <div v-if="birthDateValidation" class="text-sm">
              <p class="text-gray-600">{{ birthDateValidation.message }}</p>
              <div v-if="birthDateValidation.details" class="mt-2 grid grid-cols-2 gap-4">
                <div>
                  <span class="text-gray-500">Usia saat ini:</span>
                  <span class="font-medium ml-2">{{ birthDateValidation.details.currentAge }} tahun</span>
                </div>
                <div>
                  <span class="text-gray-500">Zodiak:</span>
                  <span class="font-medium ml-2">{{ birthDateValidation.details.zodiac }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Contact Information Validation -->
          <div class="p-4 border rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-2">
                <Icon name="phone" class="w-5 h-5 text-purple-600" />
                <span class="font-medium">Validasi Kontak</span>
              </div>
              <div class="flex items-center space-x-2">
                <Badge v-if="contactValidation" :variant="getContactValidationVariant()">
                  {{ getContactValidationLabel() }}
                </Badge>
                <Button @click="validateContactInfo" size="sm" variant="outline">
                  <Icon name="phone" class="w-3 h-3 mr-1" />
                  Validasi
                </Button>
              </div>
            </div>
            <div v-if="contactValidation" class="space-y-2 text-sm">
              <div class="flex items-center justify-between">
                <span class="text-gray-600">Telepon:</span>
                <Badge :variant="contactValidation.phone.valid ? 'default' : 'destructive'" class="text-xs">
                  {{ contactValidation.phone.valid ? 'Valid' : 'Tidak Valid' }}
                </Badge>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">Email:</span>
                <Badge :variant="contactValidation.email.valid ? 'default' : 'destructive'" class="text-xs">
                  {{ contactValidation.email.valid ? 'Valid' : 'Tidak Valid' }}
                </Badge>
              </div>
              <div v-if="contactValidation.phone.provider" class="text-xs text-gray-500">
                Provider: {{ contactValidation.phone.provider }}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Cross-Reference Verification -->
    <Card>
      <CardContent class="p-4">
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-medium">Verifikasi Silang</h4>
          <Button 
            @click="runCrossReferenceChecks"
            size="sm"
            class="bg-orange-600 hover:bg-orange-700 text-white"
            :disabled="isRunningCrossReference"
          >
            <Icon :name="isRunningCrossReference ? 'loader' : 'link'" class="w-4 h-4 mr-2" :class="{ 'animate-spin': isRunningCrossReference }" />
            Cek Silang
          </Button>
        </div>

        <div class="space-y-4">
          <!-- NIK Cross-Reference -->
          <div class="p-4 border rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-2">
                <Icon name="idCard" class="w-5 h-5 text-orange-600" />
                <span class="font-medium">Referensi Silang NIK</span>
              </div>
              <Badge v-if="nikCrossReference" :variant="nikCrossReference.matches ? 'default' : 'destructive'">
                {{ nikCrossReference.matches ? 'Cocok' : 'Tidak Cocok' }}
              </Badge>
            </div>
            <div v-if="nikCrossReference" class="text-sm space-y-2">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <span class="text-gray-600">Data NIK:</span>
                  <p class="font-medium">{{ nikCrossReference.nikData.name }}</p>
                  <p class="text-xs text-gray-500">{{ nikCrossReference.nikData.birthPlace }}, {{ nikCrossReference.nikData.birthDate }}</p>
                </div>
                <div>
                  <span class="text-gray-600">Data Pendaftaran:</span>
                  <p class="font-medium">{{ participant.nama_lengkap }}</p>
                  <p class="text-xs text-gray-500">{{ participant.tempat_lahir }}, {{ formatDate(participant.tanggal_lahir) }}</p>
                </div>
              </div>
              <div v-if="!nikCrossReference.matches" class="text-red-600 text-xs">
                <p><strong>Ketidakcocokan:</strong></p>
                <ul class="list-disc list-inside">
                  <li v-for="mismatch in nikCrossReference.mismatches" :key="mismatch">{{ mismatch }}</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Regional Verification -->
          <div class="p-4 border rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-2">
                <Icon name="mapPin" class="w-5 h-5 text-orange-600" />
                <span class="font-medium">Verifikasi Wilayah</span>
              </div>
              <Badge v-if="regionalVerification" :variant="regionalVerification.valid ? 'default' : 'destructive'">
                {{ regionalVerification.valid ? 'Valid' : 'Tidak Valid' }}
              </Badge>
            </div>
            <div v-if="regionalVerification" class="text-sm">
              <p class="text-gray-600">{{ regionalVerification.message }}</p>
              <div v-if="regionalVerification.hierarchy" class="mt-2">
                <span class="text-gray-500">Hierarki:</span>
                <span class="font-medium ml-2">{{ regionalVerification.hierarchy.join(' → ') }}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Final Verification Actions -->
    <Card class="border-l-4 border-l-green-500">
      <CardContent class="p-4">
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-medium">Aksi Verifikasi Final</h4>
          <div class="flex items-center space-x-2">
            <Badge :variant="getFinalVerificationVariant()">
              {{ getFinalVerificationLabel() }}
            </Badge>
          </div>
        </div>

        <div class="space-y-4">
          <!-- Verification Summary -->
          <div class="p-4 bg-gray-50 rounded-lg">
            <h5 class="font-medium mb-3">Ringkasan Verifikasi</h5>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div class="text-center">
                <div class="text-2xl font-bold" :class="verifiedFieldsCount === totalFields ? 'text-green-600' : 'text-orange-600'">
                  {{ verifiedFieldsCount }}/{{ totalFields }}
                </div>
                <div class="text-gray-600">Data Terverifikasi</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold" :class="passedValidationsCount === totalValidations ? 'text-green-600' : 'text-red-600'">
                  {{ passedValidationsCount }}/{{ totalValidations }}
                </div>
                <div class="text-gray-600">Validasi Lulus</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold" :class="eligibleChecksCount === totalEligibilityChecks ? 'text-green-600' : 'text-red-600'">
                  {{ eligibleChecksCount }}/{{ totalEligibilityChecks }}
                </div>
                <div class="text-gray-600">Kelayakan</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold" :class="crossReferencePassedCount === totalCrossReferenceChecks ? 'text-green-600' : 'text-red-600'">
                  {{ crossReferencePassedCount }}/{{ totalCrossReferenceChecks }}
                </div>
                <div class="text-gray-600">Referensi Silang</div>
              </div>
            </div>
          </div>

          <!-- Final Actions -->
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-600">
              <p v-if="canApproveParticipant">✅ Semua verifikasi lengkap - siap untuk disetujui</p>
              <p v-else>⚠️ Masih ada verifikasi yang perlu diselesaikan</p>
            </div>
            <div class="flex items-center space-x-2">
              <Button 
                @click="$emit('approve-participant')"
                :disabled="!canApproveParticipant"
                class="bg-green-600 hover:bg-green-700 text-white"
              >
                <Icon name="check" class="w-4 h-4 mr-2" />
                Setujui Data Peserta
              </Button>
              <Button 
                @click="$emit('reject-participant')"
                variant="destructive"
              >
                <Icon name="x" class="w-4 h-4 mr-2" />
                Tolak
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  participant: any
  registration: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'approve-participant': []
  'reject-participant': []
  'update-verification': [data: any]
}>()

// Reactive data
const isRunningValidations = ref(false)
const isRunningCrossReference = ref(false)

// Verification results
const nameConsistencyCheck = ref<{ consistent: boolean; issues?: string[] } | null>(null)
const birthDateValidation = ref<{ valid: boolean; message: string; details?: any } | null>(null)
const contactValidation = ref<{ phone: any; email: any } | null>(null)
const nikCrossReference = ref<{ matches: boolean; nikData?: any; mismatches?: string[] } | null>(null)
const regionalVerification = ref<{ valid: boolean; message: string; hierarchy?: string[] } | null>(null)

// Field verification tracking (this would be passed from parent or managed globally)
const fieldVerifications = ref<Record<string, boolean>>({
  nama_lengkap: false,
  nik: false,
  birth_info: false,
  jenis_kelamin: false,
  wilayah: false,
  no_telepon: false,
  email: false,
  instansi_asal: false
})

// Computed properties
const totalFields = computed(() => Object.keys(fieldVerifications.value).length)
const verifiedFieldsCount = computed(() => Object.values(fieldVerifications.value).filter(Boolean).length)
const overallProgress = computed(() => (verifiedFieldsCount.value / totalFields.value) * 100)

const eligibilityChecksCount = computed(() => 3) // Age, gender, regional
const validationChecksCount = computed(() => 3) // Name, birth date, contact
const competitionChecksCount = computed(() => 2) // History, duplicate

const totalValidations = computed(() => 3)
const passedValidationsCount = computed(() => {
  let count = 0
  if (nameConsistencyCheck.value?.consistent) count++
  if (birthDateValidation.value?.valid) count++
  if (contactValidation.value && getContactValidationVariant() === 'default') count++
  return count
})

const totalEligibilityChecks = computed(() => 3)
const eligibleChecksCount = computed(() => 3) // This would be calculated based on actual eligibility checks

const totalCrossReferenceChecks = computed(() => 2)
const crossReferencePassedCount = computed(() => {
  let count = 0
  if (nikCrossReference.value?.matches) count++
  if (regionalVerification.value?.valid) count++
  return count
})

const canApproveParticipant = computed(() => {
  return verifiedFieldsCount.value === totalFields.value &&
         passedValidationsCount.value === totalValidations.value &&
         crossReferencePassedCount.value === totalCrossReferenceChecks.value
})

// Methods
const getOverallProgressVariant = () => {
  if (overallProgress.value === 100) return 'default'
  if (overallProgress.value >= 75) return 'secondary'
  return 'outline'
}

const runAllDataValidations = async () => {
  isRunningValidations.value = true
  
  try {
    await Promise.all([
      checkNameConsistency(),
      validateBirthDate(),
      validateContactInfo()
    ])
  } finally {
    isRunningValidations.value = false
  }
}

const checkNameConsistency = async () => {
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  const name = props.participant.nama_lengkap
  const issues = []
  
  // Check for common issues
  if (name.includes('  ')) issues.push('Nama mengandung spasi ganda')
  if (name !== name.trim()) issues.push('Nama mengandung spasi di awal/akhir')
  if (!/^[a-zA-Z\s.'-]+$/.test(name)) issues.push('Nama mengandung karakter tidak valid')
  if (name.length < 3) issues.push('Nama terlalu pendek')
  
  nameConsistencyCheck.value = {
    consistent: issues.length === 0,
    issues
  }
}

const validateBirthDate = async () => {
  await new Promise(resolve => setTimeout(resolve, 800))
  
  const birthDate = new Date(props.participant.tanggal_lahir)
  const today = new Date()
  const age = today.getFullYear() - birthDate.getFullYear()
  
  const isValid = birthDate < today && age >= 5 && age <= 100
  
  birthDateValidation.value = {
    valid: isValid,
    message: isValid ? 'Tanggal lahir valid' : 'Tanggal lahir tidak valid',
    details: isValid ? {
      currentAge: age,
      zodiac: getZodiacSign(birthDate)
    } : null
  }
}

const validateContactInfo = async () => {
  await new Promise(resolve => setTimeout(resolve, 600))
  
  const phone = props.participant.no_telepon
  const email = props.participant.email
  
  const phoneValid = /^(\+62|62|0)[0-9]{9,12}$/.test(phone)
  const emailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  
  contactValidation.value = {
    phone: {
      valid: phoneValid,
      provider: phoneValid ? getPhoneProvider(phone) : null
    },
    email: {
      valid: emailValid
    }
  }
}

const runCrossReferenceChecks = async () => {
  isRunningCrossReference.value = true
  
  try {
    await Promise.all([
      checkNIKCrossReference(),
      verifyRegionalData()
    ])
  } finally {
    isRunningCrossReference.value = false
  }
}

const checkNIKCrossReference = async () => {
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  // Mock NIK database lookup
  const mockNIKData = {
    name: props.participant.nama_lengkap,
    birthPlace: props.participant.tempat_lahir,
    birthDate: props.participant.tanggal_lahir
  }
  
  const matches = true // In real implementation, this would be actual comparison
  
  nikCrossReference.value = {
    matches,
    nikData: mockNIKData,
    mismatches: matches ? [] : ['Nama tidak cocok', 'Tanggal lahir tidak cocok']
  }
}

const verifyRegionalData = async () => {
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  regionalVerification.value = {
    valid: true,
    message: 'Data wilayah valid dan terdaftar',
    hierarchy: ['Lampung', 'Bandar Lampung', 'Tanjung Karang']
  }
}

// Helper functions
const getContactValidationVariant = () => {
  if (!contactValidation.value) return 'outline'
  return contactValidation.value.phone.valid && contactValidation.value.email.valid ? 'default' : 'destructive'
}

const getContactValidationLabel = () => {
  if (!contactValidation.value) return 'Belum Dicek'
  const phoneValid = contactValidation.value.phone.valid
  const emailValid = contactValidation.value.email.valid
  
  if (phoneValid && emailValid) return 'Semua Valid'
  if (phoneValid || emailValid) return 'Sebagian Valid'
  return 'Tidak Valid'
}

const getFinalVerificationVariant = () => {
  if (canApproveParticipant.value) return 'default'
  return 'outline'
}

const getFinalVerificationLabel = () => {
  if (canApproveParticipant.value) return 'Siap Disetujui'
  return 'Perlu Verifikasi'
}

const getZodiacSign = (date: Date) => {
  const month = date.getMonth() + 1
  const day = date.getDate()
  
  const signs = [
    'Capricorn', 'Aquarius', 'Pisces', 'Aries', 'Taurus', 'Gemini',
    'Cancer', 'Leo', 'Virgo', 'Libra', 'Scorpio', 'Sagittarius'
  ]
  
  // Simplified zodiac calculation
  return signs[month - 1]
}

const getPhoneProvider = (phone: string) => {
  const providers: Record<string, string> = {
    '0811': 'Telkomsel',
    '0812': 'Telkomsel',
    '0813': 'Telkomsel',
    '0821': 'Telkomsel',
    '0822': 'Telkomsel',
    '0852': 'Telkomsel',
    '0853': 'Telkomsel',
    '0851': 'Telkomsel',
    '0814': 'Indosat',
    '0815': 'Indosat',
    '0816': 'Indosat',
    '0855': 'Indosat',
    '0856': 'Indosat',
    '0857': 'Indosat',
    '0858': 'Indosat',
    '0817': 'XL',
    '0818': 'XL',
    '0819': 'XL',
    '0859': 'XL',
    '0877': 'XL',
    '0878': 'XL'
  }
  
  const prefix = phone.substring(0, 4)
  return providers[prefix] || 'Unknown'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID')
}
</script>
