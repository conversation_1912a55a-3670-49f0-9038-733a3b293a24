<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Tambah Wilayah" />
    <Heading title="Tambah Wilayah" />

    <div class="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Informasi Wilayah Baru</CardTitle>
          <CardDescription>
            Lengkapi form di bawah untuk menambahkan wilayah baru
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Basic Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Dasar</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="kode_wilayah">Kode Wilayah *</Label>
                  <Input
                    id="kode_wilayah"
                    v-model="form.kode_wilayah"
                    type="text"
                    required
                    placeholder="Contoh: LP, BDL, MTR"
                    :class="{ 'border-red-500': form.errors.kode_wilayah }"
                  />
                  <p v-if="form.errors.kode_wilayah" class="text-sm text-red-600 mt-1">
                    {{ form.errors.kode_wilayah }}
                  </p>
                </div>

                <div>
                  <Label for="nama_wilayah">Nama Wilayah *</Label>
                  <Input
                    id="nama_wilayah"
                    v-model="form.nama_wilayah"
                    type="text"
                    required
                    placeholder="Contoh: Lampung, Bandar Lampung"
                    :class="{ 'border-red-500': form.errors.nama_wilayah }"
                  />
                  <p v-if="form.errors.nama_wilayah" class="text-sm text-red-600 mt-1">
                    {{ form.errors.nama_wilayah }}
                  </p>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="level_wilayah">Level Wilayah *</Label>
                  <Select v-model="form.level_wilayah" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.level_wilayah }">
                      <SelectValue placeholder="Pilih Level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="(label, value) in levels" :key="value" :value="value">
                        {{ label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.level_wilayah" class="text-sm text-red-600 mt-1">
                    {{ form.errors.level_wilayah }}
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="non_aktif">Non Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                </div>
              </div>

              <div v-if="form.level_wilayah !== 'provinsi'">
                <Label for="parent_id">Wilayah Induk</Label>
                <Select v-model="form.parent_id">
                  <SelectTrigger :class="{ 'border-red-500': form.errors.parent_id }">
                    <SelectValue placeholder="Pilih Wilayah Induk" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem v-for="parent in getAvailableParents()" :key="parent.id_wilayah" :value="parent.id_wilayah.toString()">
                      {{ parent.nama_wilayah }}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.parent_id" class="text-sm text-red-600 mt-1">
                  {{ form.errors.parent_id }}
                </p>
                <p class="text-sm text-gray-500 mt-1">
                  Kosongkan jika wilayah ini tidak memiliki induk
                </p>
              </div>
            </div>

            <!-- Hierarchy Info -->
            <div class="bg-blue-50 p-4 rounded-lg">
              <h4 class="font-medium text-blue-900 mb-2">Informasi Hierarki</h4>
              <div class="text-sm text-blue-800 space-y-1">
                <p><strong>Provinsi:</strong> Level tertinggi dalam hierarki wilayah</p>
                <p><strong>Kabupaten/Kota:</strong> Berada di bawah provinsi</p>
                <p><strong>Kecamatan:</strong> Berada di bawah kabupaten/kota (opsional)</p>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.wilayah.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan Wilayah
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Wilayah', href: '/admin/wilayah' },
  { title: 'Tambah Wilayah', href: '/admin/wilayah/create' }
]

interface ParentWilayah {
  id_wilayah: number
  nama_wilayah: string
  level_wilayah: string
}

const props = defineProps<{
  parentWilayah: ParentWilayah[]
  levels: Record<string, string>
}>()

const form = useForm({
  kode_wilayah: '',
  nama_wilayah: '',
  level_wilayah: '',
  parent_id: '',
  status: 'aktif'
})

// Clear parent when level changes to provinsi
watch(() => form.level_wilayah, (newLevel) => {
  if (newLevel === 'provinsi') {
    form.parent_id = ''
  }
})

const getAvailableParents = () => {
  if (form.level_wilayah === 'provinsi') {
    return []
  }
  
  if (form.level_wilayah === 'kabupaten' || form.level_wilayah === 'kota') {
    return props.parentWilayah.filter(w => w.level_wilayah === 'provinsi')
  }
  
  return props.parentWilayah.filter(w => 
    w.level_wilayah === 'kabupaten' || w.level_wilayah === 'kota'
  )
}

const submit = () => {
  form.post(route('admin.wilayah.store'), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}
</script>
