<?php

namespace App\Services;

use App\Models\Golongan;
use App\Models\Pendaftaran;
use App\Models\Pembayaran;
use Exception;

class RegistrationNumberService
{
    /**
     * Generate unique global registration number
     */
    public static function generateNomorPendaftaran(int $tahun): string
    {
        $prefix = "REG{$tahun}";
        $lastNumber = Pendaftaran::where('tahun_pendaftaran', $tahun)
            ->where('nomor_pendaftaran', 'like', "{$prefix}%")
            ->count();
        
        return $prefix . str_pad($lastNumber + 1, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Generate unique participant number (standardized format)
     */
    public static function generateNomorPeserta(Golongan $golongan, int $tahun): string
    {
        $prefix = $golongan->kode_golongan . $tahun;
        $lastNumber = Pendaftaran::where('id_golongan', $golongan->id_golongan)
            ->where('tahun_pendaftaran', $tahun)
            ->count();
        
        return $prefix . str_pad($lastNumber + 1, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Generate competition sequence number within golongan range
     */
    public static function generateNomorUrut(Golongan $golongan): int
    {
        $lastNomor = Pendaftaran::where('id_golongan', $golongan->id_golongan)
            ->max('nomor_urut') ?? $golongan->nomor_urut_awal - 1;
        
        $nextNomor = $lastNomor + 1;
        
        // Validate within range
        if ($nextNomor > $golongan->nomor_urut_akhir) {
            throw new Exception("Nomor urut exceeded maximum range ({$golongan->nomor_urut_akhir}) for category {$golongan->nama_golongan}");
        }
        
        return $nextNomor;
    }

    /**
     * Generate unique transaction number for payments
     */
    public static function generateNomorTransaksi(): string
    {
        $prefix = "TRX" . date('Ymd');
        $lastNumber = Pembayaran::where('nomor_transaksi', 'like', "{$prefix}%")
            ->count();
        
        return $prefix . str_pad($lastNumber + 1, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Validate if nomor_urut is within golongan range
     */
    public static function validateNomorUrut(int $nomorUrut, Golongan $golongan): bool
    {
        return $nomorUrut >= $golongan->nomor_urut_awal && $nomorUrut <= $golongan->nomor_urut_akhir;
    }

    /**
     * Check if nomor_urut is available for a golongan
     */
    public static function isNomorUrutAvailable(int $nomorUrut, int $idGolongan, ?int $excludePendaftaranId = null): bool
    {
        $query = Pendaftaran::where('id_golongan', $idGolongan)
            ->where('nomor_urut', $nomorUrut);
        
        if ($excludePendaftaranId) {
            $query->where('id_pendaftaran', '!=', $excludePendaftaranId);
        }
        
        return !$query->exists();
    }

    /**
     * Get next available nomor_urut for a golongan
     */
    public static function getNextAvailableNomorUrut(Golongan $golongan): ?int
    {
        for ($i = $golongan->nomor_urut_awal; $i <= $golongan->nomor_urut_akhir; $i++) {
            if (self::isNomorUrutAvailable($i, $golongan->id_golongan)) {
                return $i;
            }
        }
        
        return null; // No available numbers in range
    }

    /**
     * Generate all registration numbers for a new pendaftaran
     */
    public static function generateAllNumbers(Golongan $golongan, int $tahun, ?int $customNomorUrut = null): array
    {
        $nomorPendaftaran = self::generateNomorPendaftaran($tahun);
        $nomorPeserta = self::generateNomorPeserta($golongan, $tahun);
        
        if ($customNomorUrut !== null) {
            if (!self::validateNomorUrut($customNomorUrut, $golongan)) {
                throw new Exception("Nomor urut {$customNomorUrut} is outside valid range ({$golongan->nomor_urut_awal}-{$golongan->nomor_urut_akhir})");
            }
            
            if (!self::isNomorUrutAvailable($customNomorUrut, $golongan->id_golongan)) {
                throw new Exception("Nomor urut {$customNomorUrut} is already taken");
            }
            
            $nomorUrut = $customNomorUrut;
        } else {
            $nomorUrut = self::generateNomorUrut($golongan);
        }
        
        return [
            'nomor_pendaftaran' => $nomorPendaftaran,
            'nomor_peserta' => $nomorPeserta,
            'nomor_urut' => $nomorUrut
        ];
    }
}
