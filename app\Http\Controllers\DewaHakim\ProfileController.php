<?php

namespace App\Http\Controllers\DewaHakim;

use App\Http\Controllers\Controller;
use App\Models\DewaHakim;
use App\Models\DewaHakimPendidikan;
use App\Models\DewaHakimPengalaman;
use App\Models\DewaHakimPrestasi;
use App\Models\Wilayah;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class ProfileController extends Controller
{
    /**
     * Display the dewan hakim profile.
     */
    public function show()
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return redirect()->route('dashboard')
                ->with('error', 'Profil dewan hakim belum dibuat. Silakan hubungi administrator.');
        }

        $dewaHakim->load(['wilayah', 'pendidikan', 'pengalaman', 'prestasi']);

        return Inertia::render('DewaHakim/Profile/Show', [
            'dewaHakim' => $dewaHakim,
            'wilayah' => Wilayah::aktif()->get()
        ]);
    }

    /**
     * Update the dewan hakim profile.
     */
    public function update(Request $request)
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return back()->with('error', 'Profil dewan hakim tidak ditemukan.');
        }

        $validated = $request->validate([
            'nik' => ['required', 'string', 'size:16', Rule::unique('dewan_hakim')->ignore($dewaHakim->id_dewan_hakim, 'id_dewan_hakim')],
            'nama_lengkap' => 'required|string|max:100',
            'tempat_lahir' => 'required|string|max:50',
            'tanggal_lahir' => 'required|date',
            'pekerjaan' => 'nullable|string|max:100',
            'unit_kerja' => 'nullable|string|max:100',
            'alamat_rumah' => 'nullable|string',
            'alamat_kantor' => 'nullable|string',
            'no_telepon' => 'nullable|string|max:20',
            'spesialisasi' => 'nullable|string',
            'id_wilayah' => 'nullable|exists:wilayah,id_wilayah'
        ]);

        $dewaHakim->update($validated);

        return back()->with('success', 'Profil berhasil diperbarui.');
    }

    /**
     * Add education record.
     */
    public function addEducation(Request $request)
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return back()->with('error', 'Profil dewan hakim tidak ditemukan.');
        }

        $validated = $request->validate([
            'jenjang' => 'required|string|max:10',
            'instansi' => 'required|string|max:100',
            'jurusan' => 'required|string|max:100',
            'tahun_mulai' => 'required|integer|min:1950|max:' . date('Y'),
            'tahun_lulus' => 'required|integer|min:1950|max:' . date('Y') . '|gte:tahun_mulai'
        ]);

        $validated['id_dewan_hakim'] = $dewaHakim->id_dewan_hakim;

        DewaHakimPendidikan::create($validated);

        return back()->with('success', 'Data pendidikan berhasil ditambahkan.');
    }

    /**
     * Update education record.
     */
    public function updateEducation(Request $request, DewaHakimPendidikan $education)
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim || $education->id_dewan_hakim !== $dewaHakim->id_dewan_hakim) {
            return back()->with('error', 'Data tidak ditemukan.');
        }

        $validated = $request->validate([
            'jenjang' => 'required|string|max:10',
            'instansi' => 'required|string|max:100',
            'jurusan' => 'required|string|max:100',
            'tahun_mulai' => 'required|integer|min:1950|max:' . date('Y'),
            'tahun_lulus' => 'required|integer|min:1950|max:' . date('Y') . '|gte:tahun_mulai'
        ]);

        $education->update($validated);

        return back()->with('success', 'Data pendidikan berhasil diperbarui.');
    }

    /**
     * Delete education record.
     */
    public function deleteEducation(DewaHakimPendidikan $education)
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim || $education->id_dewan_hakim !== $dewaHakim->id_dewan_hakim) {
            return back()->with('error', 'Data tidak ditemukan.');
        }

        $education->delete();

        return back()->with('success', 'Data pendidikan berhasil dihapus.');
    }

    /**
     * Add experience record.
     */
    public function addExperience(Request $request)
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return back()->with('error', 'Profil dewan hakim tidak ditemukan.');
        }

        $validated = $request->validate([
            'nama_kegiatan' => 'required|string|max:200',
            'penyelenggara' => 'required|string|max:100',
            'tahun' => 'required|integer|min:1950|max:' . date('Y'),
            'tingkat' => ['required', Rule::in(['kabupaten', 'provinsi', 'nasional', 'internasional'])]
        ]);

        $validated['id_dewan_hakim'] = $dewaHakim->id_dewan_hakim;

        DewaHakimPengalaman::create($validated);

        return back()->with('success', 'Data pengalaman berhasil ditambahkan.');
    }

    /**
     * Update experience record.
     */
    public function updateExperience(Request $request, DewaHakimPengalaman $experience)
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim || $experience->id_dewan_hakim !== $dewaHakim->id_dewan_hakim) {
            return back()->with('error', 'Data tidak ditemukan.');
        }

        $validated = $request->validate([
            'nama_kegiatan' => 'required|string|max:200',
            'penyelenggara' => 'required|string|max:100',
            'tahun' => 'required|integer|min:1950|max:' . date('Y'),
            'tingkat' => ['required', Rule::in(['kabupaten', 'provinsi', 'nasional', 'internasional'])]
        ]);

        $experience->update($validated);

        return back()->with('success', 'Data pengalaman berhasil diperbarui.');
    }

    /**
     * Delete experience record.
     */
    public function deleteExperience(DewaHakimPengalaman $experience)
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim || $experience->id_dewan_hakim !== $dewaHakim->id_dewan_hakim) {
            return back()->with('error', 'Data tidak ditemukan.');
        }

        $experience->delete();

        return back()->with('success', 'Data pengalaman berhasil dihapus.');
    }

    /**
     * Add achievement record.
     */
    public function addAchievement(Request $request)
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return back()->with('error', 'Profil dewan hakim tidak ditemukan.');
        }

        $validated = $request->validate([
            'juara_ke' => 'required|integer|min:1|max:10',
            'cabang_golongan' => 'required|string|max:100',
            'nama_kegiatan' => 'required|string|max:200',
            'tahun' => 'required|integer|min:1950|max:' . date('Y'),
            'tingkat' => ['required', Rule::in(['kabupaten', 'provinsi', 'nasional', 'internasional'])]
        ]);

        $validated['id_dewan_hakim'] = $dewaHakim->id_dewan_hakim;

        DewaHakimPrestasi::create($validated);

        return back()->with('success', 'Data prestasi berhasil ditambahkan.');
    }

    /**
     * Update achievement record.
     */
    public function updateAchievement(Request $request, DewaHakimPrestasi $achievement)
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim || $achievement->id_dewan_hakim !== $dewaHakim->id_dewan_hakim) {
            return back()->with('error', 'Data tidak ditemukan.');
        }

        $validated = $request->validate([
            'juara_ke' => 'required|integer|min:1|max:10',
            'cabang_golongan' => 'required|string|max:100',
            'nama_kegiatan' => 'required|string|max:200',
            'tahun' => 'required|integer|min:1950|max:' . date('Y'),
            'tingkat' => ['required', Rule::in(['kabupaten', 'provinsi', 'nasional', 'internasional'])]
        ]);

        $achievement->update($validated);

        return back()->with('success', 'Data prestasi berhasil diperbarui.');
    }

    /**
     * Delete achievement record.
     */
    public function deleteAchievement(DewaHakimPrestasi $achievement)
    {
        $user = auth()->user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim || $achievement->id_dewan_hakim !== $dewaHakim->id_dewan_hakim) {
            return back()->with('error', 'Data tidak ditemukan.');
        }

        $achievement->delete();

        return back()->with('success', 'Data prestasi berhasil dihapus.');
    }
}
