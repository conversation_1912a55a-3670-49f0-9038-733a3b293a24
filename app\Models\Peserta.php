<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Notifications\Notifiable;

class Peserta extends Model
{
    use HasFactory, Notifiable;

    protected $table = 'peserta';
    protected $primaryKey = 'id_peserta';

    protected $fillable = [
        'id_user',
        'nik',
        'nama_lengkap',
        'tempat_lahir',
        'tanggal_lahir',
        'jenis_kelamin',
        'alamat',
        'id_wilayah',
        'no_telepon',
        'email',
        'nama_ayah',
        'nama_ibu',
        'pekerjaan',
        'instansi_asal',
        'registered_by',
        'registration_type',
        'status_peserta',
        'nama_rekening',
        'no_rekening'
    ];

    protected $casts = [
        'tanggal_lahir' => 'date',
        'jenis_kelamin' => 'string',
        'registration_type' => 'string',
        'status_peserta' => 'string'
    ];


    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id_user', 'id_user');
    }

    public function wilayah(): BelongsTo
    {
        return $this->belongsTo(Wilayah::class, 'id_wilayah', 'id_wilayah');
    }

    public function registeredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'registered_by', 'id_user');
    }

    public function pendaftaran(): HasMany
    {
        return $this->hasMany(Pendaftaran::class, 'id_peserta', 'id_peserta');
    }

    public function verifications(): HasMany
    {
        return $this->hasMany(ParticipantVerification::class, 'id_peserta', 'id_peserta');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status_peserta', $status);
    }

    public function scopeByJenisKelamin($query, $jenisKelamin)
    {
        return $query->where('jenis_kelamin', $jenisKelamin);
    }

    public function scopeByWilayah($query, $idWilayah)
    {
        return $query->where('id_wilayah', $idWilayah);
    }
    public function scopeAktif($query)
    {
        return $query->where('status_peserta', 'aktif');
    }

    // Helper methods for verification
    public function getVerificationStatus(string $verificationTypeCode): ?string
    {
        $verification = $this->verifications()
            ->whereHas('verificationType', function($query) use ($verificationTypeCode) {
                $query->where('code', $verificationTypeCode);
            })
            ->first();

        return $verification ? $verification->status : null;
    }

    public function isVerified(string $verificationTypeCode): bool
    {
        return $this->getVerificationStatus($verificationTypeCode) === 'verified';
    }

    public function hasAllRequiredVerifications(): bool
    {
        $requiredTypes = VerificationType::active()->required()->count();
        $verifiedCount = $this->verifications()
            ->whereHas('verificationType', function($query) {
                $query->where('is_active', true)->where('is_required', true);
            })
            ->where('status', 'verified')
            ->count();

        return $requiredTypes === $verifiedCount;
    }
}
