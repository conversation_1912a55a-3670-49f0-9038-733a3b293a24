- peserta yang mendaftar (bukan lomba) mandiri tidak bisa daftar lomba jika belum diverifikasi oleh admin daerah
- peserta yang didaftarkan (peserta dan atau lomba ) oleh admin daerah tidak perlu diverifikasi oleh admin daerah lagi
- peserta yang mendaftar lomba secara mandiri perlu diverifikasi oleh admin daerah
- field wajib seperti dokumen wajib harus di lengkapi dulu agar bisa dilihat oleh admin daerah/provinsi
- admin provinsi/admin/superadmin hanya bisa melihat daftar peserta/pendaftaran lomba yang sudah diverifikasi oleh admin daerah dan dokumen wajib sudah legkap
- admin provinsi/admin/superadmin memverifikasi/validasi berdasarkan tipe verifikasi yang ada dan dokumen wajib yang sudah di upload peserta. di fitur verifikasi cepat yang sudah ada, kalau di pilih setujui maka alasan defaultnya "Data valid dan lengkap" kalau ditolak maka alasan defaultnya "Data tidak valid atau lengkap" dan harus diisi alasan lainnya
- fitur pembayaran untuk sementara di disable/hilangkan

fokus ke modul admin daerah dulu kemudian ke admin kemudian baru modul peserta
