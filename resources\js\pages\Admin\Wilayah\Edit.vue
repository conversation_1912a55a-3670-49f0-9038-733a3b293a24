<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Edit Wilayah" />
    <Heading :title="`Edit Wilayah: ${wilayah.nama_wilayah}`" />

    <div class="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Edit Informasi Wilayah</CardTitle>
          <CardDescription>
            Perbarui informasi wilayah di bawah ini
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Basic Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Dasar</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="kode_wilayah">Kode Wilayah *</Label>
                  <Input
                    id="kode_wilayah"
                    v-model="form.kode_wilayah"
                    type="text"
                    required
                    placeholder="Contoh: LP, BDL, MTR"
                    :class="{ 'border-red-500': form.errors.kode_wilayah }"
                  />
                  <p v-if="form.errors.kode_wilayah" class="text-sm text-red-600 mt-1">
                    {{ form.errors.kode_wilayah }}
                  </p>
                </div>

                <div>
                  <Label for="nama_wilayah">Nama Wilayah *</Label>
                  <Input
                    id="nama_wilayah"
                    v-model="form.nama_wilayah"
                    type="text"
                    required
                    placeholder="Contoh: Lampung, Bandar Lampung"
                    :class="{ 'border-red-500': form.errors.nama_wilayah }"
                  />
                  <p v-if="form.errors.nama_wilayah" class="text-sm text-red-600 mt-1">
                    {{ form.errors.nama_wilayah }}
                  </p>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="level_wilayah">Level Wilayah *</Label>
                  <Select v-model="form.level_wilayah" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.level_wilayah }">
                      <SelectValue placeholder="Pilih Level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem v-for="(label, value) in levels" :key="value" :value="value">
                        {{ label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.level_wilayah" class="text-sm text-red-600 mt-1">
                    {{ form.errors.level_wilayah }}
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="non_aktif">Non Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                </div>
              </div>

              <div v-if="form.level_wilayah !== 'provinsi'">
                <Label for="parent_id">Wilayah Induk</Label>
                <Select v-model="form.parent_id">
                  <SelectTrigger :class="{ 'border-red-500': form.errors.parent_id }">
                    <SelectValue placeholder="Pilih Wilayah Induk" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem v-for="parent in getAvailableParents()" :key="parent.id_wilayah" :value="parent.id_wilayah.toString()">
                      {{ parent.nama_wilayah }}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.parent_id" class="text-sm text-red-600 mt-1">
                  {{ form.errors.parent_id }}
                </p>
                <p class="text-sm text-gray-500 mt-1">
                  Kosongkan jika wilayah ini tidak memiliki induk
                </p>
              </div>
            </div>

            <!-- Current Info -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">Informasi Saat Ini</h4>
              <div class="text-sm text-gray-600 space-y-1">
                <p><strong>Dibuat:</strong> {{ formatDate(wilayah.created_at) }}</p>
                <p><strong>Diperbarui:</strong> {{ formatDate(wilayah.updated_at) }}</p>
                <p v-if="wilayah.parent"><strong>Wilayah Induk:</strong> {{ wilayah.parent.nama_wilayah }}</p>
              </div>
            </div>

            <!-- Warning for changes -->
            <div v-if="hasChildren" class="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
              <div class="flex">
                <Icon name="alert-triangle" class="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
                <div>
                  <h4 class="font-medium text-yellow-800">Perhatian</h4>
                  <p class="text-sm text-yellow-700 mt-1">
                    Wilayah ini memiliki anak wilayah. Perubahan level atau parent dapat mempengaruhi hierarki wilayah.
                  </p>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.wilayah.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan Perubahan
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface Wilayah {
  id_wilayah: number
  kode_wilayah: string
  nama_wilayah: string
  level_wilayah: string
  parent_id?: number
  status: string
  created_at: string
  updated_at: string
  parent?: {
    nama_wilayah: string
  }
  children?: any[]
}

interface ParentWilayah {
  id_wilayah: number
  nama_wilayah: string
  level_wilayah: string
}

const props = defineProps<{
  wilayah: Wilayah
  parentWilayah: ParentWilayah[]
  levels: Record<string, string>
}>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Wilayah', href: '/admin/wilayah' },
  { title: 'Edit Wilayah', href: `/admin/wilayah/${props.wilayah.id_wilayah}/edit` }
]

const form = useForm({
  kode_wilayah: props.wilayah.kode_wilayah,
  nama_wilayah: props.wilayah.nama_wilayah,
  level_wilayah: props.wilayah.level_wilayah,
  parent_id: props.wilayah.parent_id?.toString() || '',
  status: props.wilayah.status
})

const hasChildren = computed(() => {
  return props.wilayah.children && props.wilayah.children.length > 0
})

// Clear parent when level changes to provinsi
watch(() => form.level_wilayah, (newLevel) => {
  if (newLevel === 'provinsi') {
    form.parent_id = ''
  }
})

const getAvailableParents = () => {
  if (form.level_wilayah === 'provinsi') {
    return []
  }
  
  if (form.level_wilayah === 'kabupaten' || form.level_wilayah === 'kota') {
    return props.parentWilayah.filter(w => w.level_wilayah === 'provinsi')
  }
  
  return props.parentWilayah.filter(w => 
    w.level_wilayah === 'kabupaten' || w.level_wilayah === 'kota'
  )
}

const submit = () => {
  form.put(route('admin.wilayah.update', props.wilayah.id_wilayah), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
