<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import TextLink from '@/components/TextLink.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthBase from '@/layouts/AuthLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { LoaderCircle } from 'lucide-vue-next';

interface Wilayah {
    id_wilayah: number;
    nama_wilayah: string;
}

const props = defineProps<{
    wilayah: Wilayah[];
}>();

const form = useForm({
    username: '',
    nama_lengkap: '',
    email: '',
    password: '',
    password_confirmation: '',
    nik: '',
    tempat_lahir: '',
    tanggal_lahir: '',
    jenis_kelamin: '',
    alamat: '',
    id_wilayah: '',
    no_telepon: '',
});

const submit = () => {
    form.post(route('register'), {
        onFinish: () => form.reset('password', 'password_confirmation'),
    });
};
</script>

<template>
    <AuthBase title="Daftar Peserta MTQ" description="Lengkapi data diri Anda untuk mendaftar sebagai peserta MTQ Lampung">
        <Head title="Register" />

        <form @submit.prevent="submit" class="flex flex-col gap-6">
            <div class="grid gap-6">
                <div class="grid gap-2">
                    <Label for="username">Username</Label>
                    <Input id="username" type="text" required autofocus :tabindex="1" autocomplete="username" v-model="form.username" placeholder="Username" />
                    <InputError :message="form.errors.username" />
                </div>

                <div class="grid gap-2">
                    <Label for="nama_lengkap">Nama Lengkap</Label>
                    <Input id="nama_lengkap" type="text" required :tabindex="2" autocomplete="name" v-model="form.nama_lengkap" placeholder="Nama lengkap" />
                    <InputError :message="form.errors.nama_lengkap" />
                </div>

                <div class="grid gap-2">
                    <Label for="email">Email address</Label>
                    <Input id="email" type="email" required :tabindex="3" autocomplete="email" v-model="form.email" placeholder="<EMAIL>" />
                    <InputError :message="form.errors.email" />
                </div>

                <div class="grid gap-2">
                    <Label for="password">Password</Label>
                    <Input
                        id="password"
                        type="password"
                        required
                        :tabindex="4"
                        autocomplete="new-password"
                        v-model="form.password"
                        placeholder="Password"
                    />
                    <InputError :message="form.errors.password" />
                </div>

                <div class="grid gap-2">
                    <Label for="password_confirmation">Confirm password</Label>
                    <Input
                        id="password_confirmation"
                        type="password"
                        required
                        :tabindex="5"
                        autocomplete="new-password"
                        v-model="form.password_confirmation"
                        placeholder="Confirm password"
                    />
                    <InputError :message="form.errors.password_confirmation" />
                </div>

                <div class="grid gap-2">
                    <Label for="nik">NIK</Label>
                    <Input id="nik" type="text" required :tabindex="6" v-model="form.nik" placeholder="Nomor Induk Kependudukan (16 digit)" maxlength="16" />
                    <InputError :message="form.errors.nik" />
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div class="grid gap-2">
                        <Label for="tempat_lahir">Tempat Lahir</Label>
                        <Input id="tempat_lahir" type="text" required :tabindex="7" v-model="form.tempat_lahir" placeholder="Tempat lahir" />
                        <InputError :message="form.errors.tempat_lahir" />
                    </div>
                    <div class="grid gap-2">
                        <Label for="tanggal_lahir">Tanggal Lahir</Label>
                        <Input id="tanggal_lahir" type="date" required :tabindex="8" v-model="form.tanggal_lahir" />
                        <InputError :message="form.errors.tanggal_lahir" />
                    </div>
                </div>

                <div class="grid gap-2">
                    <Label for="jenis_kelamin">Jenis Kelamin</Label>
                    <select id="jenis_kelamin" required :tabindex="9" v-model="form.jenis_kelamin" class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50">
                        <option value="all">Pilih Jenis Kelamin</option>
                        <option value="L">Laki-laki</option>
                        <option value="P">Perempuan</option>
                    </select>
                    <InputError :message="form.errors.jenis_kelamin" />
                </div>

                <div class="grid gap-2">
                    <Label for="alamat">Alamat</Label>
                    <textarea id="alamat" required :tabindex="10" v-model="form.alamat" placeholder="Alamat lengkap" rows="3" class="flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"></textarea>
                    <InputError :message="form.errors.alamat" />
                </div>

                <div class="grid gap-2">
                    <Label for="id_wilayah">Wilayah</Label>
                    <select id="id_wilayah" required :tabindex="11" v-model="form.id_wilayah" class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50">
                        <option value="all">Pilih Wilayah</option>
                        <option v-for="w in wilayah" :key="w.id_wilayah" :value="w.id_wilayah">{{ w.nama_wilayah }}</option>
                    </select>
                    <InputError :message="form.errors.id_wilayah" />
                </div>

                <div class="grid gap-2">
                    <Label for="no_telepon">No. Telepon (Opsional)</Label>
                    <Input id="no_telepon" type="tel" :tabindex="12" v-model="form.no_telepon" placeholder="Nomor telepon" />
                    <InputError :message="form.errors.no_telepon" />
                </div>

                <Button type="submit" class="mt-2 w-full" tabindex="13" :disabled="form.processing">
                    <LoaderCircle v-if="form.processing" class="h-4 w-4 animate-spin" />
                    {{ form.processing ? 'Mendaftar...' : 'Daftar Sekarang' }}
                </Button>
            </div>

            <div class="text-center text-sm text-muted-foreground">
                Sudah punya akun?
                <TextLink :href="route('login')" class="underline underline-offset-4" :tabindex="14">Masuk</TextLink>
            </div>
        </form>
    </AuthBase>
</template>
