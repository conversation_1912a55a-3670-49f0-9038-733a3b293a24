<?php

namespace App\Services;

use App\Models\Peserta;
use App\Models\Pendaftaran;
use App\Models\GolonganDocumentRequirement;
use Illuminate\Support\Facades\Log;

class DocumentCompletionService
{
    /**
     * Update document completion status for a participant
     */
    public function updateParticipantDocumentStatus(Peserta $participant): void
    {
        $allDocumentsComplete = true;
        
        foreach ($participant->pendaftaran as $registration) {
            if (!$this->isRegistrationDocumentComplete($registration)) {
                $allDocumentsComplete = false;
                break;
            }
        }

        $participant->update([
            'documents_complete' => $allDocumentsComplete,
            'documents_completed_at' => $allDocumentsComplete ? now() : null
        ]);

        Log::info('Document completion status updated', [
            'participant_id' => $participant->id_peserta,
            'participant_name' => $participant->nama_lengkap,
            'documents_complete' => $allDocumentsComplete
        ]);
    }

    /**
     * Update document completion status for a specific registration
     */
    public function updateRegistrationDocumentStatus(Pendaftaran $registration): void
    {
        $isComplete = $this->isRegistrationDocumentComplete($registration);
        
        // Update the participant's overall status
        $this->updateParticipantDocumentStatus($registration->peserta);
    }

    /**
     * Check if a registration has all required documents uploaded
     */
    public function isRegistrationDocumentComplete(Pendaftaran $registration): bool
    {
        $requiredDocuments = GolonganDocumentRequirement::where('id_golongan', $registration->id_golongan)
            ->where('is_required', true)
            ->count();
            
        $uploadedDocuments = $registration->dokumenPeserta()->count();
        
        return $uploadedDocuments >= $requiredDocuments;
    }

    /**
     * Get document completion details for a registration
     */
    public function getRegistrationDocumentDetails(Pendaftaran $registration): array
    {
        $requiredDocuments = GolonganDocumentRequirement::with('documentType')
            ->where('id_golongan', $registration->id_golongan)
            ->where('is_required', true)
            ->get();

        $uploadedDocuments = $registration->dokumenPeserta->keyBy('document_type_id');
        
        $documentStatus = [];
        foreach ($requiredDocuments as $requirement) {
            $documentStatus[] = [
                'document_type' => $requirement->documentType,
                'is_required' => true,
                'uploaded' => isset($uploadedDocuments[$requirement->document_type_id]),
                'document' => $uploadedDocuments[$requirement->document_type_id] ?? null
            ];
        }

        return [
            'required_count' => $requiredDocuments->count(),
            'uploaded_count' => $uploadedDocuments->count(),
            'is_complete' => $uploadedDocuments->count() >= $requiredDocuments->count(),
            'documents' => $documentStatus
        ];
    }

    /**
     * Get document completion details for a participant (all registrations)
     */
    public function getParticipantDocumentDetails(Peserta $participant): array
    {
        $registrationDetails = [];
        $overallComplete = true;
        $totalRequired = 0;
        $totalUploaded = 0;

        foreach ($participant->pendaftaran as $registration) {
            $details = $this->getRegistrationDocumentDetails($registration);
            $registrationDetails[] = [
                'registration' => $registration,
                'details' => $details
            ];

            if (!$details['is_complete']) {
                $overallComplete = false;
            }

            $totalRequired += $details['required_count'];
            $totalUploaded += $details['uploaded_count'];
        }

        return [
            'registrations' => $registrationDetails,
            'overall_complete' => $overallComplete,
            'total_required' => $totalRequired,
            'total_uploaded' => $totalUploaded,
            'completion_percentage' => $totalRequired > 0 ? round(($totalUploaded / $totalRequired) * 100) : 0
        ];
    }

    /**
     * Bulk update document completion status for multiple participants
     */
    public function bulkUpdateDocumentStatus(array $participantIds): void
    {
        $participants = Peserta::with('pendaftaran.dokumenPeserta')
            ->whereIn('id_peserta', $participantIds)
            ->get();

        foreach ($participants as $participant) {
            $this->updateParticipantDocumentStatus($participant);
        }

        Log::info('Bulk document completion status update completed', [
            'participant_count' => $participants->count(),
            'participant_ids' => $participantIds
        ]);
    }

    /**
     * Update document completion status when a document is uploaded
     */
    public function handleDocumentUpload(Pendaftaran $registration): void
    {
        $this->updateRegistrationDocumentStatus($registration);
    }

    /**
     * Update document completion status when a document is deleted
     */
    public function handleDocumentDeletion(Pendaftaran $registration): void
    {
        $this->updateRegistrationDocumentStatus($registration);
    }

    /**
     * Check and update all participants' document completion status
     * Useful for maintenance or after system changes
     */
    public function refreshAllDocumentStatuses(): void
    {
        $participants = Peserta::with('pendaftaran.dokumenPeserta')->get();
        
        $updatedCount = 0;
        foreach ($participants as $participant) {
            $oldStatus = $participant->documents_complete;
            $this->updateParticipantDocumentStatus($participant);
            
            if ($oldStatus !== $participant->fresh()->documents_complete) {
                $updatedCount++;
            }
        }

        Log::info('Document completion status refresh completed', [
            'total_participants' => $participants->count(),
            'updated_count' => $updatedCount
        ]);
    }
}
