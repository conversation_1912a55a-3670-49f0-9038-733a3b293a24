<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('participant_verifications', function (Blueprint $table) {
            $table->id('id_verification');
            $table->unsignedBigInteger('id_peserta');
            $table->unsignedBigInteger('verification_type_id');
            $table->enum('status', ['pending', 'verified', 'rejected'])->default('pending');
            $table->unsignedBigInteger('verified_by')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->text('notes')->nullable(); // Notes from verifier
            $table->json('verification_data')->nullable(); // Additional data specific to verification type
            $table->timestamps();

            // Foreign keys
            $table->foreign('id_peserta')->references('id_peserta')->on('peserta')->onDelete('cascade');
            $table->foreign('verification_type_id')->references('id_verification_type')->on('verification_types')->onDelete('cascade');
            $table->foreign('verified_by')->references('id_user')->on('users')->onDelete('set null');

            // Unique constraint to prevent duplicate verifications
            $table->unique(['id_peserta', 'verification_type_id'], 'unique_participant_verification');

            // Indexes
            $table->index(['id_peserta']);
            $table->index(['verification_type_id']);
            $table->index(['status']);
            $table->index(['verified_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('participant_verifications');
    }
};
