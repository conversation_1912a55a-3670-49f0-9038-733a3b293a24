<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Mimbar extends Model
{
    protected $table = 'mimbar';
    protected $primaryKey = 'id_mimbar';

    protected $fillable = [
        'kode_mimbar',
        'nama_mimbar',
        'keterangan',
        'kapasitas',
        'status'
    ];

    protected $casts = [
        'status' => 'string',
        'kapasitas' => 'integer'
    ];

    // Relationships
    public function pendaftaran(): HasMany
    {
        return $this->hasMany(Pendaftaran::class, 'id_mimbar', 'id_mimbar');
    }

    // Scopes
    public function scopeAktif($query)
    {
        return $query->where('status', 'aktif');
    }
}
