<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Kelola Persyaratan Dokumen" />
    <Heading title="Kelola Persyaratan Dokumen" />

    <div class="max-w-6xl mx-auto space-y-6">
      <!-- Golongan Information -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-2xl font-bold text-gray-900">{{ golongan.nama_golongan }}</h2>
              <p class="text-gray-600">{{ golongan.kode_golongan }} • {{ golongan.cabang_lomba?.nama_cabang }}</p>
              <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                <span>{{ golongan.jenis_kelamin === 'L' ? 'Putra' : 'Putri' }}</span>
                <span>{{ golongan.batas_umur_min }}-{{ golongan.batas_umur_max }} tahun</span>
                <span>Kuota: {{ golongan.kuota_max }}</span>
              </div>
            </div>
            <Badge :variant="golongan.status === 'aktif' ? 'default' : 'secondary'">
              {{ golongan.status }}
            </Badge>
          </div>
        </CardContent>
      </Card>

      <!-- Document Requirements Management -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-medium text-gray-900">Persyaratan Dokumen</h3>
            <div class="flex items-center space-x-2">
              <Button @click="resetToDefault" variant="outline" size="sm">
                <Icon name="refresh" class="w-4 h-4 mr-2" />
                Reset ke Default
              </Button>
              <Button @click="saveChanges" :disabled="form.processing">
                <Icon name="save" class="w-4 h-4 mr-2" />
                {{ form.processing ? 'Menyimpan...' : 'Simpan Perubahan' }}
              </Button>
            </div>
          </div>

          <div class="space-y-4">
            <div 
              v-for="docType in allDocumentTypes" 
              :key="docType.id_document_type"
              class="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
            >
              <div class="flex-1">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <Icon name="file" class="w-5 h-5 text-gray-400" />
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-gray-900">{{ docType.name }}</h4>
                    <p class="text-sm text-gray-500">{{ docType.description }}</p>
                    <div class="flex items-center space-x-4 mt-1 text-xs text-gray-400">
                      <span>Kode: {{ docType.code }}</span>
                      <span>Max: {{ formatFileSize(docType.max_file_size) }}</span>
                      <span>Types: {{ docType.allowed_file_types.join(', ').toUpperCase() }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <!-- Status Badge -->
                <Badge 
                  :variant="getRequirementStatus(docType.id_document_type) === 'required' ? 'destructive' : 
                           getRequirementStatus(docType.id_document_type) === 'optional' ? 'default' : 'secondary'"
                >
                  {{ getRequirementStatusText(docType.id_document_type) }}
                </Badge>

                <!-- Requirement Toggle -->
                <Select 
                  :model-value="getRequirementStatus(docType.id_document_type)"
                  @update:modelValue="updateRequirement(docType.id_document_type, $event)"
                >
                  <SelectTrigger class="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="required">Wajib</SelectItem>
                    <SelectItem value="optional">Opsional</SelectItem>
                    <SelectItem value="exclude">Tidak Perlu</SelectItem>
                  </SelectContent>
                </Select>

                <!-- Active Status -->
                <div class="flex items-center space-x-2">
                  <Badge :variant="docType.is_active ? 'default' : 'secondary'" class="text-xs">
                    {{ docType.is_active ? 'Aktif' : 'Non Aktif' }}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <!-- Summary -->
          <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 class="text-sm font-medium text-blue-900 mb-2">Ringkasan Persyaratan</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span class="font-medium text-blue-800">Dokumen Wajib:</span>
                <span class="ml-2">{{ getRequiredCount() }}</span>
              </div>
              <div>
                <span class="font-medium text-blue-800">Dokumen Opsional:</span>
                <span class="ml-2">{{ getOptionalCount() }}</span>
              </div>
              <div>
                <span class="font-medium text-blue-800">Total Dokumen:</span>
                <span class="ml-2">{{ getActiveCount() }}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Current Requirements (if any) -->
      <Card v-if="golongan.all_document_types?.length > 0">
        <CardContent class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Persyaratan Saat Ini</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div 
              v-for="docType in golongan.all_document_types" 
              :key="docType.id_document_type"
              class="flex items-center justify-between p-3 border rounded"
            >
              <div>
                <span class="text-sm font-medium">{{ docType.name }}</span>
                <p class="text-xs text-gray-500">{{ docType.code }}</p>
              </div>
              <Badge :variant="docType.pivot.is_required ? 'destructive' : 'default'">
                {{ docType.pivot.is_required ? 'Wajib' : 'Opsional' }}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex items-center justify-between">
        <Button
          variant="outline"
          @click="$inertia.visit(route('admin.golongan-document-requirements.index'))"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Kembali ke Daftar
        </Button>

        <div class="flex items-center space-x-2">
          <Button
            variant="outline"
            @click="previewChanges"
          >
            <Icon name="eye" class="w-4 h-4 mr-2" />
            Preview
          </Button>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { reactive, computed } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/Icon.vue'

const props = defineProps({
  golongan: Object,
  allDocumentTypes: Array
})

const breadcrumbItems = [
  { label: 'Dashboard', href: route('admin.dashboard') },
  { label: 'Persyaratan Dokumen Golongan', href: route('admin.golongan-document-requirements.index') },
  { label: 'Kelola Persyaratan', href: null }
]

// Track current requirements state
const requirements = reactive({})

// Initialize requirements from current golongan data
props.allDocumentTypes.forEach(docType => {
  const existing = props.golongan.all_document_types?.find(dt => dt.id_document_type === docType.id_document_type)
  if (existing) {
    requirements[docType.id_document_type] = existing.pivot.is_required ? 'required' : 'optional'
  } else {
    requirements[docType.id_document_type] = 'exclude'
  }
})

const form = useForm({
  requirements: []
})

const getRequirementStatus = (docTypeId) => {
  return requirements[docTypeId] || 'exclude'
}

const getRequirementStatusText = (docTypeId) => {
  const status = getRequirementStatus(docTypeId)
  switch (status) {
    case 'required': return 'Wajib'
    case 'optional': return 'Opsional'
    case 'exclude': return 'Tidak Perlu'
    default: return 'Tidak Perlu'
  }
}

const updateRequirement = (docTypeId, status) => {
  requirements[docTypeId] = status
}

const getRequiredCount = () => {
  return Object.values(requirements).filter(status => status === 'required').length
}

const getOptionalCount = () => {
  return Object.values(requirements).filter(status => status === 'optional').length
}

const getActiveCount = () => {
  return Object.values(requirements).filter(status => status !== 'exclude').length
}

const formatFileSize = (sizeInKB) => {
  if (!sizeInKB) return ''
  if (sizeInKB < 1024) {
    return `${sizeInKB} KB`
  }
  return `${(sizeInKB / 1024).toFixed(1)} MB`
}

const resetToDefault = () => {
  props.allDocumentTypes.forEach(docType => {
    requirements[docType.id_document_type] = docType.is_required_default ? 'required' : 'optional'
  })
}

const saveChanges = () => {
  const requirementsArray = Object.entries(requirements)
    .filter(([_, status]) => status !== 'exclude')
    .map(([docTypeId, status]) => ({
      document_type_id: parseInt(docTypeId),
      is_required: status === 'required'
    }))

  form.requirements = requirementsArray
  form.put(route('admin.golongan-document-requirements.update', props.golongan.id_golongan))
}

const previewChanges = () => {
  const changes = Object.entries(requirements)
    .map(([docTypeId, status]) => {
      const docType = props.allDocumentTypes.find(dt => dt.id_document_type === parseInt(docTypeId))
      return `${docType.name}: ${getRequirementStatusText(parseInt(docTypeId))}`
    })
    .join('\n')
  
  alert(`Preview Perubahan:\n\n${changes}`)
}
</script>
