<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DewaHakim extends Model
{
    protected $table = 'dewan_hakim';
    protected $primaryKey = 'id_dewan_hakim';

    protected $fillable = [
        'id_user',
        'nik',
        'nama_lengkap',
        'tempat_lahir',
        'tanggal_lahir',
        'pekerjaan',
        'unit_kerja',
        'alamat_rumah',
        'alamat_kantor',
        'no_telepon',
        'spesialisasi',
        'tipe_hakim',
        'id_wilayah',
        'status'
    ];

    protected $casts = [
        'tanggal_lahir' => 'date',
        'tipe_hakim' => 'string',
        'status' => 'string'
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id_user', 'id_user');
    }

    public function wilayah(): BelongsTo
    {
        return $this->belongsTo(Wilayah::class, 'id_wilayah', 'id_wilayah');
    }

    public function pendidikan(): HasMany
    {
        return $this->hasMany(DewaHakimPendidikan::class, 'id_dewan_hakim', 'id_dewan_hakim');
    }

    public function pengalaman(): HasMany
    {
        return $this->hasMany(DewaHakimPengalaman::class, 'id_dewan_hakim', 'id_dewan_hakim');
    }

    public function prestasi(): HasMany
    {
        return $this->hasMany(DewaHakimPrestasi::class, 'id_dewan_hakim', 'id_dewan_hakim');
    }

    public function nilaiPeserta(): HasMany
    {
        return $this->hasMany(NilaiPeserta::class, 'id_dewan_hakim', 'id_dewan_hakim');
    }

    // Scopes
    public function scopeAktif($query)
    {
        return $query->where('status', 'aktif');
    }

    public function scopeByTipe($query, $tipe)
    {
        return $query->where('tipe_hakim', $tipe);
    }

    public function scopeByWilayah($query, $idWilayah)
    {
        return $query->where('id_wilayah', $idWilayah);
    }

    // Helper methods
    public function isUndangan(): bool
    {
        return $this->tipe_hakim === 'undangan';
    }

    public function isKabupaten(): bool
    {
        return $this->tipe_hakim === 'kabupaten';
    }

    public function isAktif(): bool
    {
        return $this->status === 'aktif';
    }

    public function getFullNameAttribute(): string
    {
        return $this->nama_lengkap;
    }

    public function getAgeAttribute(): int
    {
        return $this->tanggal_lahir ? $this->tanggal_lahir->age : 0;
    }
}
