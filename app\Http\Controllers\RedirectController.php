<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class RedirectController extends Controller
{
    /**
     * Redirect user to appropriate dashboard based on role
     */
    public function redirect(): RedirectResponse
    {
        $user = Auth::user();
        
        switch ($user->role) {
            case 'superadmin':
            case 'admin':
                return redirect()->route('admin.dashboard');
            case 'admin_daerah':
                return redirect()->route('admin-daerah.dashboard');
            case 'peserta':
                return redirect()->route('peserta.dashboard');
            case 'dewan_hakim':
                return redirect()->route('hakim.dashboard');
            default:
                return redirect()->route('dashboard');
        }
    }
}
