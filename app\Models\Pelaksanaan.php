<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Pelaksanaan extends Model
{
    protected $table = 'pelaksanaan';
    protected $primaryKey = 'id_pelaksanaan';

    protected $fillable = [
        'tahun',
        'tema',
        'tempat',
        'tanggal_mulai',
        'tanggal_selesai',
        'tanggal_buka_pendaftaran',
        'tanggal_tutup_pendaftaran',
        'status'
    ];

    protected $casts = [
        'tahun' => 'integer',
        'tanggal_mulai' => 'date',
        'tanggal_selesai' => 'date',
        'tanggal_buka_pendaftaran' => 'date',
        'tanggal_tutup_pendaftaran' => 'date',
        'status' => 'string'
    ];

    // Scopes
    public function scopeAktif($query)
    {
        return $query->where('status', 'aktif');
    }

    public function scopeByTahun($query, $tahun)
    {
        return $query->where('tahun', $tahun);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Helper methods
    public function isDraft(): bool
    {
        return $this->status === 'draft';
    }

    public function isAktif(): bool
    {
        return $this->status === 'aktif';
    }

    public function isSelesai(): bool
    {
        return $this->status === 'selesai';
    }

    public function isPendaftaranTerbuka(): bool
    {
        $now = Carbon::now()->toDateString();
        return $this->isAktif() && 
               $now >= $this->tanggal_buka_pendaftaran->toDateString() && 
               $now <= $this->tanggal_tutup_pendaftaran->toDateString();
    }

    public function isPendaftaranBelumBuka(): bool
    {
        $now = Carbon::now()->toDateString();
        return $this->isAktif() && $now < $this->tanggal_buka_pendaftaran->toDateString();
    }

    public function isPendaftaranSudahTutup(): bool
    {
        $now = Carbon::now()->toDateString();
        return $this->isAktif() && $now > $this->tanggal_tutup_pendaftaran->toDateString();
    }

    public function getDurasiPelaksanaanAttribute(): int
    {
        return $this->tanggal_mulai->diffInDays($this->tanggal_selesai) + 1;
    }

    public function getDurasiPendaftaranAttribute(): int
    {
        return $this->tanggal_buka_pendaftaran->diffInDays($this->tanggal_tutup_pendaftaran) + 1;
    }

    public function getFormattedPeriodePelaksanaanAttribute(): string
    {
        if ($this->tanggal_mulai->isSameDay($this->tanggal_selesai)) {
            return $this->tanggal_mulai->format('d F Y');
        }
        
        if ($this->tanggal_mulai->isSameMonth($this->tanggal_selesai)) {
            return $this->tanggal_mulai->format('d') . ' - ' . $this->tanggal_selesai->format('d F Y');
        }
        
        return $this->tanggal_mulai->format('d F') . ' - ' . $this->tanggal_selesai->format('d F Y');
    }

    public function getFormattedPeriodePendaftaranAttribute(): string
    {
        if ($this->tanggal_buka_pendaftaran->isSameDay($this->tanggal_tutup_pendaftaran)) {
            return $this->tanggal_buka_pendaftaran->format('d F Y');
        }
        
        if ($this->tanggal_buka_pendaftaran->isSameMonth($this->tanggal_tutup_pendaftaran)) {
            return $this->tanggal_buka_pendaftaran->format('d') . ' - ' . $this->tanggal_tutup_pendaftaran->format('d F Y');
        }
        
        return $this->tanggal_buka_pendaftaran->format('d F') . ' - ' . $this->tanggal_tutup_pendaftaran->format('d F Y');
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'draft' => 'Draft',
            'aktif' => 'Aktif',
            'selesai' => 'Selesai',
            default => ucfirst($this->status)
        };
    }

    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'draft' => 'badge-secondary',
            'aktif' => 'badge-success',
            'selesai' => 'badge-primary',
            default => 'badge-secondary'
        };
    }

    public function getPendaftaranStatusAttribute(): string
    {
        if ($this->isPendaftaranBelumBuka()) {
            return 'Belum Buka';
        } elseif ($this->isPendaftaranTerbuka()) {
            return 'Terbuka';
        } elseif ($this->isPendaftaranSudahTutup()) {
            return 'Tutup';
        } else {
            return 'Tidak Aktif';
        }
    }
}
