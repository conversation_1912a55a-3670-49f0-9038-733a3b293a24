<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Dashboard Verifikasi" />
    
    <div class="space-y-6">
      <!-- Header -->
      <div class="islamic-gradient p-6 rounded-lg islamic-shadow">
        <Heading title="Dashboard Verifikasi" class="text-white"/>
        <p class="text-green-100 mt-2">Ke<PERSON>la dan pantau semua proses verifikasi MTQ</p>
      </div>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card class="islamic-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-4">
              <div class="p-3 bg-blue-500 rounded-full">
                <Icon name="users" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-gray-900">{{ stats.participants.total }}</p>
                <p class="text-sm text-gray-600">Total Peserta</p>
                <div class="flex space-x-4 mt-1">
                  <span class="text-xs text-green-600">{{ stats.participants.verified }} Terverifikasi</span>
                  <span class="text-xs text-yellow-600">{{ stats.participants.pending }} Pending</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="islamic-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-4">
              <div class="p-3 bg-green-500 rounded-full">
                <Icon name="file-check" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-gray-900">{{ stats.documents.total }}</p>
                <p class="text-sm text-gray-600">Total Dokumen</p>
                <div class="flex space-x-4 mt-1">
                  <span class="text-xs text-green-600">{{ stats.documents.approved }} Disetujui</span>
                  <span class="text-xs text-red-600">{{ stats.documents.rejected }} Ditolak</span>
                  <span class="text-xs text-yellow-600">{{ stats.documents.pending }} Pending</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="islamic-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-4">
              <div class="p-3 bg-purple-500 rounded-full">
                <Icon name="user-check" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-gray-900">{{ stats.registrations.total }}</p>
                <p class="text-sm text-gray-600">Total Pendaftaran</p>
                <div class="flex space-x-4 mt-1">
                  <span class="text-xs text-green-600">{{ stats.registrations.verified }} Terverifikasi</span>
                  <span class="text-xs text-yellow-600">{{ stats.registrations.pending }} Pending</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Quick Actions -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <h3 class="text-lg font-semibold mb-4">Aksi Cepat</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div v-for="action in quickActions" :key="action.title">
              <Button 
                as-child 
                :variant="action.color === 'blue' ? 'default' : 'outline'"
                class="w-full h-auto p-4 flex flex-col items-center space-y-2"
              >
                <TextLink :href="action.url">
                  <Icon :name="action.icon" class="w-8 h-8" />
                  <div class="text-center">
                    <p class="font-medium">{{ action.title }}</p>
                    <p class="text-xs opacity-75">{{ action.description }}</p>
                    <Badge v-if="action.count > 0" :variant="action.color" class="mt-1">
                      {{ action.count }} pending
                    </Badge>
                  </div>
                </TextLink>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Verification Types Progress -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <h3 class="text-lg font-semibold mb-4">Progress Verifikasi per Jenis</h3>
          <div class="space-y-4">
            <div v-for="type in verificationTypes" :key="type.id" class="border rounded-lg p-4">
              <div class="flex justify-between items-center mb-2">
                <div>
                  <h4 class="font-medium">{{ type.name }}</h4>
                  <p class="text-sm text-gray-600">{{ type.description }}</p>
                </div>
                <div class="text-right">
                  <p class="text-lg font-bold">{{ type.progress_percentage }}%</p>
                  <p class="text-xs text-gray-500">{{ type.verified }}/{{ type.total }}</p>
                </div>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div 
                  class="bg-green-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${type.progress_percentage}%` }"
                ></div>
              </div>
              <div class="flex justify-between text-xs text-gray-500 mt-1">
                <span>{{ type.verified }} Terverifikasi</span>
                <span>{{ type.rejected }} Ditolak</span>
                <span>{{ type.pending }} Pending</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Recent Activities -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <h3 class="text-lg font-semibold mb-4">Aktivitas Terbaru</h3>
          <div class="space-y-3">
            <div v-for="activity in recentActivities" :key="activity.title + activity.verified_at" 
                 class="flex items-center space-x-3 p-3 border rounded-lg">
              <div class="flex-shrink-0">
                <Badge 
                  :variant="activity.status === 'verified' ? 'default' : 'destructive'"
                  class="w-2 h-2 p-0 rounded-full"
                ></Badge>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900">{{ activity.title }}</p>
                <p class="text-xs text-gray-500 truncate">{{ activity.description }}</p>
              </div>
              <div class="flex-shrink-0 text-right">
                <p class="text-xs text-gray-500">{{ formatDate(activity.verified_at) }}</p>
                <p class="text-xs text-gray-400">oleh {{ activity.verified_by }}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { type BreadcrumbItem } from '@/types'

interface Props {
  stats: {
    participants: {
      total: number
      verified: number
      pending: number
    }
    documents: {
      total: number
      approved: number
      rejected: number
      pending: number
    }
    registrations: {
      total: number
      verified: number
      rejected: number
      pending: number
    }
  }
  verificationTypes: Array<{
    id: number
    name: string
    description: string
    total: number
    verified: number
    rejected: number
    pending: number
    progress_percentage: number
  }>
  recentActivities: Array<{
    type: string
    title: string
    description: string
    status: string
    verified_by: string
    verified_at: string
    url: string
  }>
  quickActions: Array<{
    title: string
    description: string
    icon: string
    url: string
    count: number
    color: string
  }>
  userPermissions: any
}

const props = defineProps<Props>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard Admin', href: '/admin/dashboard' },
  { title: 'Dashboard Verifikasi', href: '#' }
]

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.islamic-gradient {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.islamic-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.islamic-shadow:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style>
