<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Manajemen User" />
    <Heading title="Manajemen User" />

    <div class="space-y-6">
      <!-- Filters and Search -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama, username, email..."
                @input="search"
              />
            </div>
            <div>
              <Label for="role">Role</Label>
              <Select v-model="filters.role" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Role</SelectItem>
                  <SelectItem v-for="(label, value) in roles" :key="value" :value="value">
                    {{ label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="status">Status</Label>
              <Select v-model="filters.status" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="aktif">Aktif</SelectItem>
                  <SelectItem value="non_aktif">Non Aktif</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="wilayah">Wilayah</Label>
              <Select v-model="filters.wilayah" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Wilayah" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Wilayah</SelectItem>
                  <SelectItem v-for="w in wilayah" :key="w.id_wilayah" :value="w.id_wilayah.toString()">
                    {{ w.nama_wilayah }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          Menampilkan {{ users.from }} - {{ users.to }} dari {{ users.total }} user
        </div>
        <Button @click="$inertia.visit(route('admin.users.create'))">
          <Icon name="plus" class="w-4 h-4 mr-2" />
          Tambah User
        </Button>
      </div>

      <!-- Users Table -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Wilayah
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dibuat
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="user in users.data" :key="user.id_user" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ user.nama_lengkap }}</div>
                      <div class="text-sm text-gray-500">{{ user.username }} • {{ user.email }}</div>
                      <div v-if="user.no_telepon" class="text-sm text-gray-500">{{ user.no_telepon }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="getRoleVariant(user.role)">
                      {{ roles[user.role] || user.role }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ user.wilayah?.nama_wilayah || '-' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="getStatusVariant(user.status)">
                      {{ getStatusLabel(user.status) }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(user.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.users.show', user.id_user))"
                    >
                      <Icon name="eye" class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.users.edit', user.id_user))"
                    >
                      <Icon name="edit" class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="toggleStatus(user)"
                      :disabled="user.role === 'superadmin'"
                    >
                      <Icon :name="user.status === 'aktif' ? 'user-x' : 'user-check'" class="w-4 h-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger as-child>
                        <Button variant="ghost" size="sm">
                          <Icon name="more-vertical" class="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem @click="showResetPasswordDialog(user)">
                          <Icon name="key" class="w-4 h-4 mr-2" />
                          Reset Password
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          @click="deleteUser(user)"
                          :disabled="user.role === 'superadmin'"
                          class="text-red-600"
                        >
                          <Icon name="trash" class="w-4 h-4 mr-2" />
                          Hapus
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <Pagination :links="users.links" />
    </div>

    <!-- Reset Password Dialog -->
    <Dialog v-model:open="resetPasswordDialog.show">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Reset Password</DialogTitle>
          <DialogDescription>
            Reset password untuk user: {{ resetPasswordDialog.user?.nama_lengkap }}
          </DialogDescription>
        </DialogHeader>
        <form @submit.prevent="resetPassword">
          <div class="space-y-4">
            <div>
              <Label for="password">Password Baru</Label>
              <Input
                id="password"
                v-model="resetPasswordForm.password"
                type="password"
                required
              />
            </div>
            <div>
              <Label for="password_confirmation">Konfirmasi Password</Label>
              <Input
                id="password_confirmation"
                v-model="resetPasswordForm.password_confirmation"
                type="password"
                required
              />
            </div>
          </div>
          <DialogFooter class="mt-6">
            <Button type="button" variant="outline" @click="resetPasswordDialog.show = false">
              Batal
            </Button>
            <Button type="submit" :disabled="resetPasswordForm.processing">
              Reset Password
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Head, router, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import Icon from '@/components/Icon.vue'
import Pagination from '@/components/Pagination.vue'
import { debounce } from 'lodash'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen User', href: '/admin/users' }
]

interface User {
  id_user: number
  username: string
  email: string
  nama_lengkap: string
  no_telepon?: string
  role: string
  status: string
  created_at: string
  wilayah?: {
    nama_wilayah: string
  }
}

interface PaginatedUsers {
  data: User[]
  links: any[]
  from: number
  to: number
  total: number
}

const props = defineProps<{
  users: PaginatedUsers
  filters: {
    search?: string
    role?: string
    status?: string
    wilayah?: string
  }
  wilayah: Array<{
    id_wilayah: number
    nama_wilayah: string
  }>
  roles: Record<string, string>
}>()

const filters = reactive({ ...props.filters })

const resetPasswordDialog = ref({
  show: false,
  user: null as User | null
})

const resetPasswordForm = useForm({
  password: '',
  password_confirmation: ''
})

const search = debounce(() => {
  router.get(route('admin.users.index'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const toggleStatus = (user: User) => {
  router.post(route('admin.users.toggle-status', user.id_user), {}, {
    preserveScroll: true
  })
}

const showResetPasswordDialog = (user: User) => {
  resetPasswordDialog.value.user = user
  resetPasswordDialog.value.show = true
  resetPasswordForm.reset()
}

const resetPassword = () => {
  if (!resetPasswordDialog.value.user) return

  resetPasswordForm.post(route('admin.users.reset-password', resetPasswordDialog.value.user.id_user), {
    onSuccess: () => {
      resetPasswordDialog.value.show = false
      resetPasswordForm.reset()
    }
  })
}

const deleteUser = (user: User) => {
  if (confirm(`Apakah Anda yakin ingin menghapus user ${user.nama_lengkap}?`)) {
    router.delete(route('admin.users.destroy', user.id_user))
  }
}

const getRoleVariant = (role: string) => {
  const variants: Record<string, string> = {
    superadmin: 'destructive',
    admin: 'default',
    admin_daerah: 'secondary',
    dewan_hakim: 'outline',
    peserta: 'secondary'
  }
  return variants[role] || 'secondary'
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    aktif: 'default',
    non_aktif: 'secondary',
    suspended: 'destructive'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    aktif: 'Aktif',
    non_aktif: 'Non Aktif',
    suspended: 'Suspended'
  }
  return labels[status] || status
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>
