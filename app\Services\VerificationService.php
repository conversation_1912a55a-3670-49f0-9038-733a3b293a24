<?php

namespace App\Services;

use App\Models\Peserta;
use App\Models\Pendaftaran;
use App\Models\DokumenPeserta;
use App\Models\VerificationType;
use App\Models\ParticipantVerification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

class VerificationService
{
    /**
     * Standardized verification statuses
     */
    public const STATUS_PENDING = 'pending';
    public const STATUS_VERIFIED = 'verified';
    public const STATUS_REJECTED = 'rejected';

    /**
     * Verify a participant for a specific verification type
     */
    public function verifyParticipant(
        Peserta $peserta,
        VerificationType $verificationType,
        string $status,
        ?string $notes = null,
        array $verificationData = []
    ): ParticipantVerification {
        $this->validateStatus($status);

        return ParticipantVerification::updateOrCreate(
            [
                'id_peserta' => $peserta->id_peserta,
                'verification_type_id' => $verificationType->id_verification_type
            ],
            [
                'status' => $status,
                'verified_by' => Auth::id(),
                'verified_at' => now(),
                'notes' => $notes,
                'verification_data' => $verificationData
            ]
        );
    }

    /**
     * Verify a document
     */
    public function verifyDocument(
        DokumenPeserta $dokumen,
        string $status,
        ?string $notes = null,
        array $metadata = []
    ): DokumenPeserta {
        $this->validateStatus($status);

        $dokumen->update([
            'status_verifikasi' => $status === self::STATUS_VERIFIED ? 'approved' : 'rejected',
            'catatan_verifikasi' => $notes,
            'verified_by' => Auth::id(),
            'verified_at' => now(),
            'metadata' => $metadata
        ]);

        return $dokumen;
    }

    /**
     * Verify a registration (overall approval/rejection)
     */
    public function verifyRegistration(
        Pendaftaran $pendaftaran,
        string $status,
        ?string $notes = null
    ): Pendaftaran {
        $this->validateStatus($status);

        DB::transaction(function () use ($pendaftaran, $status, $notes) {
            $pendaftaran->update([
                'status_pendaftaran' => $status,
                'verified_by' => Auth::id(),
                'verified_at' => now(),
                'catatan_verifikasi' => $notes
            ]);

            // Update peserta status as well
            $pendaftaran->peserta->update([
                'status_peserta' => $status
            ]);
        });

        return $pendaftaran;
    }

    /**
     * Bulk verify participants
     */
    public function bulkVerifyParticipants(
        Collection $participants,
        VerificationType $verificationType,
        string $status,
        ?string $notes = null
    ): array {
        $this->validateStatus($status);
        
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($participants as $peserta) {
            try {
                $this->verifyParticipant($peserta, $verificationType, $status, $notes);
                $results['success']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'peserta_id' => $peserta->id_peserta,
                    'error' => $e->getMessage()
                ];
                
                Log::error('Bulk participant verification failed', [
                    'peserta_id' => $peserta->id_peserta,
                    'verification_type_id' => $verificationType->id_verification_type,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Bulk verify documents
     */
    public function bulkVerifyDocuments(
        Collection $documents,
        string $status,
        ?string $notes = null
    ): array {
        $this->validateStatus($status);
        
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($documents as $dokumen) {
            try {
                $this->verifyDocument($dokumen, $status, $notes);
                $results['success']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'document_id' => $dokumen->id_dokumen,
                    'error' => $e->getMessage()
                ];
                
                Log::error('Bulk document verification failed', [
                    'document_id' => $dokumen->id_dokumen,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Bulk verify registrations
     */
    public function bulkVerifyRegistrations(
        Collection $registrations,
        string $status,
        ?string $notes = null
    ): array {
        $this->validateStatus($status);
        
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($registrations as $pendaftaran) {
            try {
                $this->verifyRegistration($pendaftaran, $status, $notes);
                $results['success']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'registration_id' => $pendaftaran->id_pendaftaran,
                    'error' => $e->getMessage()
                ];
                
                Log::error('Bulk registration verification failed', [
                    'registration_id' => $pendaftaran->id_pendaftaran,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Calculate verification progress for a participant
     */
    public function calculateParticipantProgress(Peserta $peserta): array
    {
        $verificationTypes = VerificationType::active()->required()->get();
        $verifications = $peserta->verifications()->with('verificationType')->get();
        
        $progress = [
            'total_required' => $verificationTypes->count(),
            'completed' => 0,
            'verified' => 0,
            'rejected' => 0,
            'pending' => 0,
            'percentage' => 0,
            'details' => []
        ];

        foreach ($verificationTypes as $type) {
            $verification = $verifications->firstWhere('verification_type_id', $type->id_verification_type);
            
            $detail = [
                'verification_type' => $type,
                'status' => $verification ? $verification->status : self::STATUS_PENDING,
                'verified_at' => $verification?->verified_at,
                'notes' => $verification?->notes
            ];

            if ($verification) {
                $progress['completed']++;
                if ($verification->status === self::STATUS_VERIFIED) {
                    $progress['verified']++;
                } elseif ($verification->status === self::STATUS_REJECTED) {
                    $progress['rejected']++;
                }
            } else {
                $progress['pending']++;
            }

            $progress['details'][] = $detail;
        }

        $progress['percentage'] = $progress['total_required'] > 0 
            ? round(($progress['completed'] / $progress['total_required']) * 100, 2)
            : 0;

        return $progress;
    }

    /**
     * Get verification statistics
     */
    public function getVerificationStatistics(): array
    {
        return [
            'participants' => [
                'total' => Peserta::count(),
                'verified' => Peserta::whereHas('verifications', function($q) {
                    $q->where('status', self::STATUS_VERIFIED);
                })->count(),
                'pending' => Peserta::whereDoesntHave('verifications')->count()
            ],
            'documents' => [
                'total' => DokumenPeserta::count(),
                'approved' => DokumenPeserta::where('status_verifikasi', 'approved')->count(),
                'rejected' => DokumenPeserta::where('status_verifikasi', 'rejected')->count(),
                'pending' => DokumenPeserta::where('status_verifikasi', 'pending')->count()
            ],
            'registrations' => [
                'total' => Pendaftaran::count(),
                'verified' => Pendaftaran::where('status_pendaftaran', self::STATUS_VERIFIED)->count(),
                'rejected' => Pendaftaran::where('status_pendaftaran', self::STATUS_REJECTED)->count(),
                'pending' => Pendaftaran::whereIn('status_pendaftaran', ['submitted', 'paid'])->count()
            ]
        ];
    }

    /**
     * Validate verification status
     */
    private function validateStatus(string $status): void
    {
        $validStatuses = [self::STATUS_PENDING, self::STATUS_VERIFIED, self::STATUS_REJECTED];
        
        if (!in_array($status, $validStatuses)) {
            throw new \InvalidArgumentException("Invalid verification status: {$status}");
        }
    }

    /**
     * Get standardized status message
     */
    public function getStatusMessage(string $status, string $type = 'verification'): string
    {
        $messages = [
            self::STATUS_VERIFIED => [
                'verification' => 'berhasil diverifikasi',
                'document' => 'berhasil disetujui',
                'registration' => 'berhasil disetujui'
            ],
            self::STATUS_REJECTED => [
                'verification' => 'ditolak',
                'document' => 'ditolak',
                'registration' => 'ditolak'
            ]
        ];

        return $messages[$status][$type] ?? 'status tidak dikenal';
    }
}
