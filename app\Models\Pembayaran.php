<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Pembayaran extends Model
{
    protected $table = 'pembayaran';
    protected $primaryKey = 'id_pembayaran';

    protected $fillable = [
        'id_pendaftaran',
        'nomor_transaksi',
        'jumlah_bayar',
        'metode_pembayaran',
        'status_pembayaran',
        'tanggal_bayar',
        'bukti_pembayaran',
        'reference_number',
        'verified_by',
        'verified_at',
        'catatan'
    ];

    protected $casts = [
        'jumlah_bayar' => 'decimal:2',
        'metode_pembayaran' => 'string',
        'status_pembayaran' => 'string',
        'tanggal_bayar' => 'datetime',
        'verified_at' => 'datetime'
    ];

    // Relationships
    public function pendaftaran(): BelongsTo
    {
        return $this->belongsTo(Pendaftaran::class, 'id_pendaftaran', 'id_pendaftaran');
    }

    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by', 'id_user');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status_pembayaran', $status);
    }

    public function scopeByMetode($query, $metode)
    {
        return $query->where('metode_pembayaran', $metode);
    }
}
