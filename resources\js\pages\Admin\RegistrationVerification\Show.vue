<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head :title="`Verifikasi Pendaftaran - ${registration.nomor_pendaftaran}`" />

    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div>
          <Heading :title="`Verifikasi Pendaftaran`" />
          <p class="text-gray-600 mt-1">{{ registration.nomor_pendaftaran }}</p>
        </div>
        <div class="flex items-center space-x-3">
          <Button @click="router.back()" variant="outline">
            <Icon name="arrowLeft" class="w-4 h-4 mr-2" />
            Kembali
          </Button>
          <Button
            v-if="canVerify"
            @click="showVerifyModal = true"
            class="bg-green-600 hover:bg-green-700 text-white"
          >
            <Icon name="check" class="w-4 h-4 mr-2" />
            Verifikasi Pendaftaran
          </Button>
        </div>
      </div>

      <!-- Overall Progress -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">Progress Verifikasi</h3>
            <Badge :variant="getStatusVariant(registration.status_pendaftaran)">
              {{ getStatusLabel(registration.status_pendaftaran) }}
            </Badge>
          </div>

          <div class="space-y-4">
            <!-- Overall Progress Bar -->
            <div>
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium">Progress Keseluruhan</span>
                <span class="text-sm text-gray-500">{{ verificationProgress.overall_percentage }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-3">
                <div
                  class="bg-blue-600 h-3 rounded-full transition-all duration-500"
                  :style="{ width: `${verificationProgress.overall_percentage}%` }"
                ></div>
              </div>
            </div>

            <!-- Document Progress -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium">Dokumen</span>
                  <span class="text-sm text-gray-500">
                    {{ verificationProgress.documents.verified }}/{{ verificationProgress.documents.required }}
                  </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-green-600 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${verificationProgress.documents.percentage}%` }"
                  ></div>
                </div>
              </div>

              <div>
                <div class="flex items-center justify-between mb-2">
                  <span class="text-sm font-medium">Data Peserta</span>
                  <span class="text-sm text-gray-500">
                    {{ verificationProgress.verifications.completed }}/{{ verificationProgress.verifications.required }}
                  </span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-purple-600 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${verificationProgress.verifications.percentage}%` }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Participant Information -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <h3 class="text-lg font-semibold mb-4">Informasi Peserta</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-3">
              <div>
                <Label class="text-sm font-medium text-gray-500">Nama Lengkap</Label>
                <p class="text-sm">{{ registration.peserta.nama_lengkap }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">NIK</Label>
                <p class="text-sm">{{ registration.peserta.nik }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Tempat, Tanggal Lahir</Label>
                <p class="text-sm">{{ registration.peserta.tempat_lahir }}, {{ formatDate(registration.peserta.tanggal_lahir) }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Jenis Kelamin</Label>
                <p class="text-sm">{{ registration.peserta.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</p>
              </div>
            </div>
            <div class="space-y-3">
              <div>
                <Label class="text-sm font-medium text-gray-500">Wilayah</Label>
                <p class="text-sm">{{ registration.peserta.wilayah?.nama_wilayah }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">No. Telepon</Label>
                <p class="text-sm">{{ registration.peserta.no_telepon }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Email</Label>
                <p class="text-sm">{{ registration.peserta.email }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Instansi Asal</Label>
                <p class="text-sm">{{ registration.peserta.instansi_asal || '-' }}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Competition Information -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <h3 class="text-lg font-semibold mb-4">Informasi Lomba</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <Label class="text-sm font-medium text-gray-500">Cabang Lomba</Label>
              <p class="text-sm">{{ registration.golongan.cabang_lomba.nama_cabang }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Golongan</Label>
              <p class="text-sm">{{ registration.golongan.nama_golongan }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Tanggal Daftar</Label>
              <p class="text-sm">{{ formatDate(registration.created_at) }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Participant Verification -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">Verifikasi Data Peserta</h3>
          </div>

          <div class="space-y-4">
            <div v-for="verificationType in verificationTypes" :key="verificationType.id_verification_type">
              <ParticipantVerificationCard
                :verification-type="verificationType"
                :registration="registration"
                @verify="handleParticipantVerification"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Document Verification -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">Verifikasi Dokumen</h3>
            <Button
              v-if="hasDocumentsPending"
              @click="showBulkDocumentModal = true"
              variant="outline"
              size="sm"
            >
              <Icon name="check" class="w-4 h-4 mr-2" />
              Verifikasi Massal
            </Button>
          </div>

          <div class="space-y-4">
            <!-- Required Documents -->
            <div v-for="docType in requiredDocuments" :key="docType.id_document_type">
              <DocumentVerificationCard
                :document-type="docType"
                :documents="getDocumentsByType(docType.id_document_type)"
                @verify="handleDocumentVerification"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Verification History -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <h3 class="text-lg font-semibold mb-4">Riwayat Verifikasi</h3>
          <VerificationHistory :registration="registration" />
        </CardContent>
      </Card>
    </div>

    <!-- Verification Modal -->
    <VerificationModal
      v-model:show="showVerifyModal"
      :registration="registration"
      @verify="handleRegistrationVerification"
    />

    <!-- Bulk Document Verification Modal -->
    <BulkDocumentVerifyModal
      v-model:show="showBulkDocumentModal"
      :documents="pendingDocuments"
      @verify="handleBulkDocumentVerification"
    />
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, router } from '@inertiajs/vue3'
import { ref, computed } from 'vue'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import Icon from '@/components/Icon.vue'
import ParticipantVerificationCard from './ParticipantVerificationCard.vue'
import DocumentVerificationCard from './DocumentVerificationCard.vue'
import VerificationHistory from './VerificationHistory.vue'
import VerificationModal from './VerificationModal.vue'
import BulkDocumentVerifyModal from './BulkDocumentVerifyModal.vue'
import { type BreadcrumbItem } from '@/types'

// Props
interface Props {
  registration: any
  documents: any[]
  requiredDocuments: any[]
  verificationTypes: any[]
  verificationProgress: any
}

const props = defineProps<Props>()

// Breadcrumbs
const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard Admin', href: '/admin/dashboard' },
  { title: 'Verifikasi Pendaftaran', href: '/admin/registration-verification' },
  { title: props.registration.nomor_pendaftaran, href: '#' }
]

// Reactive data
const showVerifyModal = ref(false)
const showBulkDocumentModal = ref(false)

// Computed
const canVerify = computed(() => {
  return ['submitted', 'paid'].includes(props.registration.status_pendaftaran)
})

const hasDocumentsPending = computed(() => {
  return props.documents.some(doc => doc.status_verifikasi === 'pending')
})

const pendingDocuments = computed(() => {
  return props.documents.filter(doc => doc.status_verifikasi === 'pending')
})

// Methods
const getDocumentsByType = (documentTypeId: number) => {
  return props.documents.filter(doc => doc.document_type_id === documentTypeId)
}

const handleRegistrationVerification = (data: any) => {
  router.post(`/admin/registration-verification/${props.registration.id_pendaftaran}/verify`, data, {
    onSuccess: () => {
      showVerifyModal.value = false
    }
  })
}

const handleParticipantVerification = (data: any) => {
  router.post(`/admin/registration-verification/${props.registration.id_pendaftaran}/verify-participant-data`, data)
}

const handleDocumentVerification = (data: any) => {
  router.post(`/admin/registration-verification/documents/${data.documentId}/verify`, {
    status_verifikasi: data.action,
    catatan_verifikasi: data.notes
  })
}

const handleBulkDocumentVerification = (data: any) => {
  router.post('/admin/registration-verification/documents/bulk-verify', {
    document_ids: data.documentIds,
    action: data.action,
    notes: data.notes
  }, {
    onSuccess: () => {
      showBulkDocumentModal.value = false
    }
  })
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'draft': 'secondary',
    'submitted': 'default',
    'payment_pending': 'outline',
    'paid': 'default',
    'verified': 'default',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'draft': 'Draft',
    'submitted': 'Submitted',
    'payment_pending': 'Menunggu Pembayaran',
    'paid': 'Dibayar',
    'verified': 'Terverifikasi',
    'approved': 'Terverifikasi', // Map to standardized label
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>
