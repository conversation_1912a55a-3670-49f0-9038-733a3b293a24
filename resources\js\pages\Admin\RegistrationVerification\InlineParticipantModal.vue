<template>
  <Dialog :open="show" @update:open="$emit('update:show', $event)">
    <DialogContent class="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>Verifikasi Data Peserta</DialogTitle>
        <DialogDescription>
          {{ registration?.nomor_pendaftaran }} - {{ registration?.peserta?.nama_lengkap }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- Participant Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardContent class="p-4">
              <h3 class="font-medium mb-3">Informasi Peserta</h3>
              <div class="space-y-3 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600"><PERSON>a <PERSON>:</span>
                  <span class="font-medium">{{ registration?.peserta?.nama_lengkap }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">NIK:</span>
                  <span class="font-medium font-mono">{{ registration?.peserta?.nik }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Tempat, Tanggal Lahir:</span>
                  <span class="font-medium">
                    {{ registration?.peserta?.tempat_lahir }}, {{ formatDate(registration?.peserta?.tanggal_lahir) }}
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Jenis Kelamin:</span>
                  <span class="font-medium">{{ registration?.peserta?.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Wilayah:</span>
                  <span class="font-medium">{{ registration?.peserta?.wilayah?.nama_wilayah }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">No. Telepon:</span>
                  <span class="font-medium">{{ registration?.peserta?.no_telepon }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Email:</span>
                  <span class="font-medium">{{ registration?.peserta?.email }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Instansi:</span>
                  <span class="font-medium">{{ registration?.peserta?.instansi_asal || '-' }}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent class="p-4">
              <h3 class="font-medium mb-3">Informasi Lomba</h3>
              <div class="space-y-3 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">Cabang Lomba:</span>
                  <span class="font-medium">{{ registration?.golongan?.cabang_lomba?.nama_cabang }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Golongan:</span>
                  <span class="font-medium">{{ registration?.golongan?.nama_golongan }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Tanggal Daftar:</span>
                  <span class="font-medium">{{ formatDate(registration?.created_at) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Status:</span>
                  <Badge :variant="getStatusVariant(registration?.status_pendaftaran)">
                    {{ getStatusLabel(registration?.status_pendaftaran) }}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Verification Types -->
        <div class="space-y-4">
          <h3 class="font-medium">Jenis Verifikasi</h3>

          <div v-if="activeVerificationTypes.length > 0" class="space-y-4">
            <div
              v-for="verificationType in activeVerificationTypes"
              :key="verificationType.id_verification_type"
              class="border rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-3">
                <div>
                  <h4 class="font-medium">{{ verificationType.name }}</h4>
                  <p class="text-sm text-gray-600">{{ verificationType.description }}</p>
                </div>
                <Badge :variant="getVerificationStatusVariant(getVerificationStatus(verificationType.id_verification_type))">
                  {{ getVerificationStatusLabel(getVerificationStatus(verificationType.id_verification_type)) }}
                </Badge>
              </div>

              <!-- Existing Verification -->
              <div v-if="getExistingVerification(verificationType.id_verification_type)" class="mb-4 p-3 bg-blue-50 rounded border border-blue-200">
                <h5 class="font-medium text-blue-800 mb-2">Verifikasi Sebelumnya</h5>
                <div class="text-sm text-blue-700 space-y-1">
                  <p><strong>Status:</strong> {{ getVerificationStatusLabel(getExistingVerification(verificationType.id_verification_type).status) }}</p>
                  <p v-if="getExistingVerification(verificationType.id_verification_type).verified_by">
                    <strong>Diverifikasi oleh:</strong> {{ getExistingVerification(verificationType.id_verification_type).verified_by.nama_lengkap }}
                  </p>
                  <p v-if="getExistingVerification(verificationType.id_verification_type).verified_at">
                    <strong>Tanggal:</strong> {{ formatDate(getExistingVerification(verificationType.id_verification_type).verified_at) }}
                  </p>
                  <p v-if="getExistingVerification(verificationType.id_verification_type).notes">
                    <strong>Catatan:</strong> {{ getExistingVerification(verificationType.id_verification_type).notes }}
                  </p>
                </div>
              </div>

              <!-- Verification Form -->
              <form @submit.prevent="handleVerification(verificationType)" class="space-y-3">
                <!-- Status Selection -->
                <div class="grid grid-cols-2 gap-3">
                  <Button
                    type="button"
                    @click="setVerificationAction(verificationType.id_verification_type, 'verified')"
                    :variant="getVerificationAction(verificationType.id_verification_type) === 'verified' ? 'default' : 'outline'"
                    class="justify-center"
                    :class="{
                      'bg-green-600 hover:bg-green-700 text-white': getVerificationAction(verificationType.id_verification_type) === 'verified',
                      'border-green-600 text-green-600 hover:bg-green-50': getVerificationAction(verificationType.id_verification_type) !== 'verified'
                    }"
                  >
                    <Icon name="check" class="w-4 h-4 mr-2" />
                    Terverifikasi
                  </Button>
                  <Button
                    type="button"
                    @click="setVerificationAction(verificationType.id_verification_type, 'rejected')"
                    :variant="getVerificationAction(verificationType.id_verification_type) === 'rejected' ? 'destructive' : 'outline'"
                    class="justify-center"
                    :class="{
                      'border-red-600 text-red-600 hover:bg-red-50': getVerificationAction(verificationType.id_verification_type) !== 'rejected'
                    }"
                  >
                    <Icon name="x" class="w-4 h-4 mr-2" />
                    Ditolak
                  </Button>
                </div>

                <!-- Notes -->
                <div v-if="getVerificationAction(verificationType.id_verification_type)">
                  <Label :for="`notes-${verificationType.id_verification_type}`">
                    Catatan Verifikasi
                    <span v-if="getVerificationAction(verificationType.id_verification_type) === 'rejected'" class="text-red-500">*</span>
                  </Label>
                  <Textarea
                    :id="`notes-${verificationType.id_verification_type}`"
                    v-model="verificationForms[verificationType.id_verification_type].notes"
                    :placeholder="getVerificationAction(verificationType.id_verification_type) === 'verified' ? 'Tambahkan catatan verifikasi (opsional)' : 'Jelaskan alasan penolakan (wajib)'"
                    rows="2"
                    :required="getVerificationAction(verificationType.id_verification_type) === 'rejected'"
                  />
                </div>

                <!-- NIK Specific Verification -->
                <div v-if="verificationType.code === 'nik' && getVerificationAction(verificationType.id_verification_type) === 'verified'" class="p-3 bg-green-50 rounded border border-green-200">
                  <h5 class="font-medium text-green-800 mb-2">Validasi NIK</h5>
                  <div class="grid grid-cols-2 gap-4 text-sm text-green-700">
                    <div>
                      <span class="font-medium">NIK:</span> {{ registration?.peserta?.nik }}
                    </div>
                    <div>
                      <span class="font-medium">Nama:</span> {{ registration?.peserta?.nama_lengkap }}
                    </div>
                  </div>
                </div>

                <!-- Submit Button -->
                <Button
                  v-if="getVerificationAction(verificationType.id_verification_type)"
                  type="submit"
                  :disabled="isSubmitting || (getVerificationAction(verificationType.id_verification_type) === 'rejected' && !verificationForms[verificationType.id_verification_type].notes?.trim())"
                  class="w-full"
                  :class="{
                    'bg-green-600 hover:bg-green-700': getVerificationAction(verificationType.id_verification_type) === 'verified',
                    'bg-red-600 hover:bg-red-700': getVerificationAction(verificationType.id_verification_type) === 'rejected'
                  }"
                >
                  <Icon
                    v-if="isSubmitting"
                    name="loader"
                    class="w-4 h-4 mr-2 animate-spin"
                  />
                  {{ getVerificationAction(verificationType.id_verification_type) === 'verified' ? 'Verifikasi Data' : 'Tolak Verifikasi' }}
                </Button>
              </form>
            </div>
          </div>

          <div v-else class="text-center py-8 text-gray-500">
            <Icon name="userX" class="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>Tidak ada jenis verifikasi yang tersedia</p>
          </div>
        </div>
      </div>

      <!-- Footer Actions -->
      <DialogFooter>
        <Button
          type="button"
          variant="outline"
          @click="$emit('update:show', false)"
        >
          Tutup
        </Button>
        <Button
          v-if="hasUnverifiedData"
          @click="verifyAllData"
          class="bg-blue-600 hover:bg-blue-700"
        >
          <Icon name="checkCircle" class="w-4 h-4 mr-2" />
          Verifikasi Semua
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  show: boolean
  registration: any
  verificationTypes?: Array<{
    id_verification_type: number
    name: string
    description: string
    code: string
    is_required: boolean
    is_active: boolean
  }>
}

const props = withDefaults(defineProps<Props>(), {
  verificationTypes: () => []
})

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'verify': [data: { registrationId: number; verification_type_id: number; status: string; notes: string; verification_data?: any }]
}>()

// Reactive data
const isSubmitting = ref(false)
const verificationForms = ref<Record<number, { action: string; notes: string }>>({})

// Computed
const activeVerificationTypes = computed(() => {
  return props.verificationTypes.filter(vt => vt.is_active)
})

const hasUnverifiedData = computed(() => {
  return activeVerificationTypes.value.some(vt =>
    getVerificationStatus(vt.id_verification_type) === 'pending'
  )
})

// Watch for modal close to reset form
watch(() => props.show, (newValue) => {
  if (!newValue) {
    verificationForms.value = {}
  }
})

// Methods
const getExistingVerification = (verificationTypeId: number) => {
  return props.registration?.participant_verifications?.find(
    (v: any) => v.verification_type_id === verificationTypeId
  )
}

const getVerificationStatus = (verificationTypeId: number) => {
  const existing = getExistingVerification(verificationTypeId)
  return existing?.status || 'pending'
}

const getVerificationAction = (verificationTypeId: number) => {
  return verificationForms.value[verificationTypeId]?.action || ''
}

const setVerificationAction = (verificationTypeId: number, action: string) => {
  if (!verificationForms.value[verificationTypeId]) {
    verificationForms.value[verificationTypeId] = { action: '', notes: '' }
  }
  verificationForms.value[verificationTypeId].action = action
}

const handleVerification = async (verificationType: any) => {
  const form = verificationForms.value[verificationType.id_verification_type]
  if (!form?.action) return
  if (form.action === 'rejected' && !form.notes?.trim()) return

  isSubmitting.value = true

  try {
    const verificationData: any = {}

    // Add specific data based on verification type
    if (verificationType.code === 'nik') {
      verificationData.nik = props.registration.peserta.nik
      verificationData.nama_lengkap = props.registration.peserta.nama_lengkap
    }

    emit('verify', {
      registrationId: props.registration.id_pendaftaran,
      verification_type_id: verificationType.id_verification_type,
      status: form.action,
      notes: form.notes,
      verification_data: verificationData
    })

    // Reset form for this verification type
    verificationForms.value[verificationType.id_verification_type] = { action: '', notes: '' }
  } finally {
    isSubmitting.value = false
  }
}

const verifyAllData = () => {
  // Verify all pending verifications as verified
  activeVerificationTypes.value.forEach(vt => {
    if (getVerificationStatus(vt.id_verification_type) === 'pending') {
      setVerificationAction(vt.id_verification_type, 'verified')
      handleVerification(vt)
    }
  })
}

const getVerificationStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'pending': 'outline',
    'verified': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getVerificationStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'pending': 'Pending',
    'verified': 'Terverifikasi',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'draft': 'secondary',
    'submitted': 'default',
    'payment_pending': 'outline',
    'paid': 'default',
    'verified': 'default',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'draft': 'Draft',
    'submitted': 'Submitted',
    'payment_pending': 'Menunggu Pembayaran',
    'paid': 'Dibayar',
    'verified': 'Terverifikasi',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>
