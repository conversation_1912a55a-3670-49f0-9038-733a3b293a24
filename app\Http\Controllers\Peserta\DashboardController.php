<?php

namespace App\Http\Controllers\Peserta;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\DokumenPeserta;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the peserta dashboard
     */
    public function index(): Response
    {
        $user = Auth::user();
        $peserta = $user->peserta;

        // Get recent registrations
        $pendaftaran = Pendaftaran::with(['golongan.cabangLomba', 'pembayaran'])
            ->where('id_peserta', $peserta->id_peserta)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Calculate statistics
        $stats = [
            'total_pendaftaran' => Pendaftaran::where('id_peserta', $peserta->id_peserta)->count(),
            'pendaftaran_approved' => Pendaftaran::where('id_peserta', $peserta->id_peserta)
                ->where('status_pendaftaran', 'approved')->count(),
            'pendaftaran_pending' => Pendaftaran::where('id_peserta', $peserta->id_peserta)
                ->whereIn('status_pendaftaran', ['draft', 'submitted', 'payment_pending', 'paid', 'verified'])
                ->count(),
            'dokumen_pending' => DokumenPeserta::whereHas('pendaftaran', function ($query) use ($peserta) {
                $query->where('id_peserta', $peserta->id_peserta);
            })->where('status_verifikasi', 'pending')->count(),
        ];

        return Inertia::render('Peserta/Dashboard', [
            'peserta' => $peserta->load('wilayah'),
            'pendaftaran' => $pendaftaran,
            'stats' => $stats
        ]);
    }
}
