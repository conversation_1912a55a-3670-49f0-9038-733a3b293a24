<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Tambah Golongan" />
    <Heading title="Tambah Golongan" />

    <div class="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Informasi Golongan Baru</CardTitle>
          <CardDescription>
            Lengkapi form di bawah untuk menambahkan golongan baru
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Basic Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Dasar</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="kode_golongan">Kode Golongan *</Label>
                  <Input
                    id="kode_golongan"
                    v-model="form.kode_golongan"
                    type="text"
                    required
                    placeholder="Contoh: TIL-1Pa, TAH-5Pi"
                    :class="{ 'border-red-500': form.errors.kode_golongan }"
                  />
                  <p v-if="form.errors.kode_golongan" class="text-sm text-red-600 mt-1">
                    {{ form.errors.kode_golongan }}
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="non_aktif">Non Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="nama_golongan">Nama Golongan *</Label>
                <Input
                  id="nama_golongan"
                  v-model="form.nama_golongan"
                  type="text"
                  required
                  placeholder="Contoh: Tilawah 1 Putra, Tahfidz 5 Juz Putri"
                  :class="{ 'border-red-500': form.errors.nama_golongan }"
                />
                <p v-if="form.errors.nama_golongan" class="text-sm text-red-600 mt-1">
                  {{ form.errors.nama_golongan }}
                </p>
              </div>

              <div>
                <Label for="id_cabang">Cabang Lomba *</Label>
                <Select v-model="form.id_cabang" required>
                  <SelectTrigger :class="{ 'border-red-500': form.errors.id_cabang }">
                    <SelectValue placeholder="Pilih Cabang Lomba" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem v-for="cabang in cabangLomba" :key="cabang.id_cabang" :value="cabang.id_cabang.toString()">
                      {{ cabang.nama_cabang }} ({{ cabang.kode_cabang }})
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.id_cabang" class="text-sm text-red-600 mt-1">
                  {{ form.errors.id_cabang }}
                </p>
              </div>
            </div>

            <!-- Criteria -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Kriteria Peserta</h3>
              
              <div>
                <Label for="jenis_kelamin">Jenis Kelamin *</Label>
                <Select v-model="form.jenis_kelamin" required>
                  <SelectTrigger :class="{ 'border-red-500': form.errors.jenis_kelamin }">
                    <SelectValue placeholder="Pilih Jenis Kelamin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="L">Laki-laki</SelectItem>
                    <SelectItem value="P">Perempuan</SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.jenis_kelamin" class="text-sm text-red-600 mt-1">
                  {{ form.errors.jenis_kelamin }}
                </p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="batas_umur_min">Batas Umur Minimum *</Label>
                  <Input
                    id="batas_umur_min"
                    v-model="form.batas_umur_min"
                    type="number"
                    required
                    min="1"
                    max="100"
                    placeholder="Contoh: 7"
                    :class="{ 'border-red-500': form.errors.batas_umur_min }"
                  />
                  <p v-if="form.errors.batas_umur_min" class="text-sm text-red-600 mt-1">
                    {{ form.errors.batas_umur_min }}
                  </p>
                </div>

                <div>
                  <Label for="batas_umur_max">Batas Umur Maksimum *</Label>
                  <Input
                    id="batas_umur_max"
                    v-model="form.batas_umur_max"
                    type="number"
                    required
                    min="1"
                    max="100"
                    placeholder="Contoh: 12"
                    :class="{ 'border-red-500': form.errors.batas_umur_max }"
                  />
                  <p v-if="form.errors.batas_umur_max" class="text-sm text-red-600 mt-1">
                    {{ form.errors.batas_umur_max }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Competition Settings -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Pengaturan Lomba</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="kuota_max">Kuota Maksimum *</Label>
                  <Input
                    id="kuota_max"
                    v-model="form.kuota_max"
                    type="number"
                    required
                    min="1"
                    placeholder="Contoh: 50"
                    :class="{ 'border-red-500': form.errors.kuota_max }"
                  />
                  <p v-if="form.errors.kuota_max" class="text-sm text-red-600 mt-1">
                    {{ form.errors.kuota_max }}
                  </p>
                </div>

                <div>
                  <Label for="biaya_pendaftaran">Biaya Pendaftaran *</Label>
                  <Input
                    id="biaya_pendaftaran"
                    v-model="form.biaya_pendaftaran"
                    type="number"
                    required
                    min="0"
                    step="1000"
                    placeholder="Contoh: 150000"
                    :class="{ 'border-red-500': form.errors.biaya_pendaftaran }"
                  />
                  <p v-if="form.errors.biaya_pendaftaran" class="text-sm text-red-600 mt-1">
                    {{ form.errors.biaya_pendaftaran }}
                  </p>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="nomor_urut_awal">Nomor Urut Awal</Label>
                  <Input
                    id="nomor_urut_awal"
                    v-model="form.nomor_urut_awal"
                    type="number"
                    min="1"
                    placeholder="Default: 1"
                    :class="{ 'border-red-500': form.errors.nomor_urut_awal }"
                  />
                  <p v-if="form.errors.nomor_urut_awal" class="text-sm text-red-600 mt-1">
                    {{ form.errors.nomor_urut_awal }}
                  </p>
                </div>

                <div>
                  <Label for="nomor_urut_akhir">Nomor Urut Akhir</Label>
                  <Input
                    id="nomor_urut_akhir"
                    v-model="form.nomor_urut_akhir"
                    type="number"
                    min="1"
                    placeholder="Default: 999"
                    :class="{ 'border-red-500': form.errors.nomor_urut_akhir }"
                  />
                  <p v-if="form.errors.nomor_urut_akhir" class="text-sm text-red-600 mt-1">
                    {{ form.errors.nomor_urut_akhir }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Information -->
            <div class="bg-blue-50 p-4 rounded-lg">
              <h4 class="font-medium text-blue-900 mb-2">Informasi Golongan</h4>
              <div class="text-sm text-blue-800 space-y-1">
                <p><strong>Kode Golongan:</strong> Kombinasi kode cabang + nomor + jenis kelamin (Pa/Pi)</p>
                <p><strong>Batas Umur:</strong> Rentang usia peserta yang boleh mengikuti golongan ini</p>
                <p><strong>Kuota:</strong> Jumlah maksimum peserta yang dapat mendaftar</p>
                <p><strong>Nomor Urut:</strong> Range nomor peserta untuk golongan ini (opsional)</p>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.golongan.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan Golongan
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Golongan', href: '/admin/golongan' },
  { title: 'Tambah Golongan', href: '/admin/golongan/create' }
]

interface CabangLomba {
  id_cabang: number
  kode_cabang: string
  nama_cabang: string
}

const props = defineProps<{
  cabangLomba: CabangLomba[]
  selectedCabang?: string
}>()

const form = useForm({
  kode_golongan: '',
  nama_golongan: '',
  id_cabang: props.selectedCabang || '',
  jenis_kelamin: '',
  batas_umur_min: '',
  batas_umur_max: '',
  kuota_max: '',
  biaya_pendaftaran: '',
  nomor_urut_awal: '',
  nomor_urut_akhir: '',
  status: 'aktif'
})

const submit = () => {
  form.post(route('admin.golongan.store'), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}
</script>
