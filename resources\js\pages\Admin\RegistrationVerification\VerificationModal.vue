<template>
  <Dialog :open="show" @update:open="$emit('update:show', $event)">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>Verifikasi Pendaftaran</DialogTitle>
        <DialogDescription>
          Verifikasi pendaftaran {{ registration.nomor_pendaftaran }}
        </DialogDescription>
      </DialogHeader>

      <form @submit.prevent="handleSubmit" class="space-y-4">
        <!-- Action Selection -->
        <div class="space-y-2">
          <Label for="action">Aksi Verifikasi</Label>
          <Select v-model="form.action" required>
            <SelectTrigger>
              <SelectValue placeholder="Pilih aksi verifikasi" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="approve">
                <div class="flex items-center space-x-2">
                  <Icon name="check" class="w-4 h-4 text-green-600" />
                  <span>Setujui Pendaftaran</span>
                </div>
              </SelectItem>
              <SelectItem value="reject">
                <div class="flex items-center space-x-2">
                  <Icon name="x" class="w-4 h-4 text-red-600" />
                  <span>Tolak Pendaftaran</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- Notes -->
        <div class="space-y-2">
          <Label for="notes">Catatan Verifikasi</Label>
          <Textarea
            id="notes"
            v-model="form.notes"
            placeholder="Tambahkan catatan untuk verifikasi ini"
            rows="3"
            :required="form.action === 'reject'"
          />
          <p v-if="form.action === 'reject'" class="text-xs text-red-600">
            Catatan wajib diisi untuk penolakan
          </p>
        </div>

        <!-- Warning Message -->
        <div v-if="form.action" class="p-4 rounded-lg border">
          <div v-if="form.action === 'approve'" class="flex items-start space-x-2 text-green-700 bg-green-50 p-3 rounded">
            <Icon name="checkCircle" class="w-5 h-5 mt-0.5 flex-shrink-0" />
            <div>
              <p class="font-medium">Menyetujui Pendaftaran</p>
              <p class="text-sm text-green-600 mt-1">
                Pendaftaran akan berstatus "Terverifikasi" dan dapat melanjutkan ke tahap berikutnya.
              </p>
            </div>
          </div>
          <div v-else-if="form.action === 'reject'" class="flex items-start space-x-2 text-red-700 bg-red-50 p-3 rounded">
            <Icon name="xCircle" class="w-5 h-5 mt-0.5 flex-shrink-0" />
            <div>
              <p class="font-medium">Menolak Pendaftaran</p>
              <p class="text-sm text-red-600 mt-1">
                Pendaftaran akan berstatus "Ditolak" dan peserta perlu memperbaiki data mereka.
              </p>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <DialogFooter>
          <Button 
            type="button" 
            variant="outline" 
            @click="$emit('update:show', false)"
          >
            Batal
          </Button>
          <Button 
            type="submit" 
            :disabled="!form.action || isSubmitting || (form.action === 'reject' && !form.notes.trim())"
            :class="{
              'bg-green-600 hover:bg-green-700': form.action === 'approve',
              'bg-red-600 hover:bg-red-700': form.action === 'reject'
            }"
          >
            <Icon 
              v-if="isSubmitting" 
              name="loader" 
              class="w-4 h-4 mr-2 animate-spin" 
            />
            <Icon 
              v-else-if="form.action === 'approve'" 
              name="check" 
              class="w-4 h-4 mr-2" 
            />
            <Icon 
              v-else-if="form.action === 'reject'" 
              name="x" 
              class="w-4 h-4 mr-2" 
            />
            {{ getSubmitButtonText() }}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  show: boolean
  registration: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'verify': [data: { action: string; notes: string }]
}>()

// Reactive data
const isSubmitting = ref(false)
const form = ref({
  action: '',
  notes: ''
})

// Watch for modal close to reset form
watch(() => props.show, (newValue) => {
  if (!newValue) {
    form.value = {
      action: '',
      notes: ''
    }
  }
})

// Methods
const handleSubmit = async () => {
  if (!form.value.action) return
  if (form.value.action === 'reject' && !form.value.notes.trim()) return

  isSubmitting.value = true
  
  try {
    emit('verify', {
      action: form.value.action,
      notes: form.value.notes
    })
  } finally {
    isSubmitting.value = false
  }
}

const getSubmitButtonText = () => {
  if (isSubmitting.value) {
    return 'Memproses...'
  }
  
  if (form.value.action === 'approve') {
    return 'Setujui Pendaftaran'
  } else if (form.value.action === 'reject') {
    return 'Tolak Pendaftaran'
  }
  
  return 'Verifikasi'
}
</script>
