<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\CabangLomba;
use App\Models\Golongan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CabangLombaManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a superadmin user for testing
        $this->superadmin = User::factory()->create([
            'role' => 'superadmin',
            'status' => 'aktif'
        ]);
    }

    /** @test */
    public function superadmin_can_view_cabang_lomba_index()
    {
        $this->actingAs($this->superadmin);
        
        $response = $this->get(route('admin.cabang-lomba.index'));
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/CabangLomba/Index'));
    }

    /** @test */
    public function superadmin_can_create_cabang_lomba()
    {
        $this->actingAs($this->superadmin);
        
        $cabangLombaData = [
            'kode_cabang' => 'TIL',
            'nama_cabang' => 'Tilawatil Quran',
            'deskripsi' => 'Cabang lomba membaca Al-Quran dengan tartil',
            'status' => 'aktif'
        ];
        
        $response = $this->post(route('admin.cabang-lomba.store'), $cabangLombaData);
        
        $response->assertRedirect(route('admin.cabang-lomba.index'));
        $this->assertDatabaseHas('cabang_lomba', [
            'kode_cabang' => 'TIL',
            'nama_cabang' => 'Tilawatil Quran'
        ]);
    }

    /** @test */
    public function superadmin_can_update_cabang_lomba()
    {
        $this->actingAs($this->superadmin);
        
        $cabangLomba = CabangLomba::factory()->create();
        
        $updateData = [
            'kode_cabang' => 'TIL_UPDATED',
            'nama_cabang' => 'Tilawatil Quran Updated',
            'deskripsi' => 'Updated description',
            'status' => 'aktif'
        ];
        
        $response = $this->put(route('admin.cabang-lomba.update', $cabangLomba), $updateData);
        
        $response->assertRedirect(route('admin.cabang-lomba.index'));
        $this->assertDatabaseHas('cabang_lomba', [
            'id_cabang' => $cabangLomba->id_cabang,
            'kode_cabang' => 'TIL_UPDATED',
            'nama_cabang' => 'Tilawatil Quran Updated'
        ]);
    }

    /** @test */
    public function superadmin_can_view_cabang_lomba_golongan()
    {
        $this->actingAs($this->superadmin);
        
        $cabangLomba = CabangLomba::factory()->create();
        $golongan = Golongan::factory()->create([
            'id_cabang' => $cabangLomba->id_cabang
        ]);
        
        $response = $this->get(route('admin.cabang-lomba.golongan', $cabangLomba));
        
        $response->assertStatus(200);
        $response->assertJson([
            [
                'id_golongan' => $golongan->id_golongan,
                'nama_golongan' => $golongan->nama_golongan
            ]
        ]);
    }

    /** @test */
    public function cabang_lomba_creation_validates_required_fields()
    {
        $this->actingAs($this->superadmin);
        
        $response = $this->post(route('admin.cabang-lomba.store'), []);
        
        $response->assertSessionHasErrors([
            'kode_cabang',
            'nama_cabang',
            'status'
        ]);
    }

    /** @test */
    public function cabang_lomba_creation_validates_unique_kode()
    {
        $this->actingAs($this->superadmin);
        
        $existingCabang = CabangLomba::factory()->create();
        
        $response = $this->post(route('admin.cabang-lomba.store'), [
            'kode_cabang' => $existingCabang->kode_cabang,
            'nama_cabang' => 'Test Cabang',
            'status' => 'aktif'
        ]);
        
        $response->assertSessionHasErrors(['kode_cabang']);
    }

    /** @test */
    public function superadmin_cannot_delete_cabang_lomba_with_golongan()
    {
        $this->actingAs($this->superadmin);
        
        $cabangLomba = CabangLomba::factory()->create();
        $golongan = Golongan::factory()->create([
            'id_cabang' => $cabangLomba->id_cabang
        ]);
        
        $response = $this->delete(route('admin.cabang-lomba.destroy', $cabangLomba));
        
        $response->assertRedirect();
        $response->assertSessionHas('error');
        $this->assertDatabaseHas('cabang_lomba', ['id_cabang' => $cabangLomba->id_cabang]);
    }

    /** @test */
    public function cabang_lomba_search_functionality_works()
    {
        $this->actingAs($this->superadmin);
        
        CabangLomba::factory()->create(['nama_cabang' => 'Tilawatil Quran']);
        CabangLomba::factory()->create(['nama_cabang' => 'Tahfidzul Quran']);
        CabangLomba::factory()->create(['nama_cabang' => 'Fahmil Quran']);
        
        $response = $this->get(route('admin.cabang-lomba.index', ['search' => 'Tilawah']));
        
        $response->assertStatus(200);
    }

    /** @test */
    public function cabang_lomba_filtering_by_status_works()
    {
        $this->actingAs($this->superadmin);
        
        CabangLomba::factory()->create(['status' => 'aktif']);
        CabangLomba::factory()->create(['status' => 'non_aktif']);
        
        $response = $this->get(route('admin.cabang-lomba.index', ['status' => 'aktif']));
        
        $response->assertStatus(200);
    }

    /** @test */
    public function cabang_lomba_show_displays_correct_data()
    {
        $this->actingAs($this->superadmin);
        
        $cabangLomba = CabangLomba::factory()->create();
        $golongan1 = Golongan::factory()->create(['id_cabang' => $cabangLomba->id_cabang]);
        $golongan2 = Golongan::factory()->create(['id_cabang' => $cabangLomba->id_cabang]);
        
        $response = $this->get(route('admin.cabang-lomba.show', $cabangLomba));
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/CabangLomba/Show'));
    }

    /** @test */
    public function cabang_lomba_can_be_deleted_when_no_golongan()
    {
        $this->actingAs($this->superadmin);
        
        $cabangLomba = CabangLomba::factory()->create();
        
        $response = $this->delete(route('admin.cabang-lomba.destroy', $cabangLomba));
        
        $response->assertRedirect(route('admin.cabang-lomba.index'));
        $this->assertDatabaseMissing('cabang_lomba', ['id_cabang' => $cabangLomba->id_cabang]);
    }

    /** @test */
    public function cabang_lomba_status_can_be_changed()
    {
        $this->actingAs($this->superadmin);
        
        $cabangLomba = CabangLomba::factory()->create(['status' => 'aktif']);
        
        // Update to non_aktif
        $response = $this->put(route('admin.cabang-lomba.update', $cabangLomba), [
            'kode_cabang' => $cabangLomba->kode_cabang,
            'nama_cabang' => $cabangLomba->nama_cabang,
            'deskripsi' => $cabangLomba->deskripsi,
            'status' => 'non_aktif'
        ]);
        
        $response->assertRedirect(route('admin.cabang-lomba.index'));
        $this->assertDatabaseHas('cabang_lomba', [
            'id_cabang' => $cabangLomba->id_cabang,
            'status' => 'non_aktif'
        ]);
    }

    /** @test */
    public function cabang_lomba_create_form_loads_correctly()
    {
        $this->actingAs($this->superadmin);
        
        $response = $this->get(route('admin.cabang-lomba.create'));
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/CabangLomba/Create'));
    }

    /** @test */
    public function cabang_lomba_edit_form_loads_correctly()
    {
        $this->actingAs($this->superadmin);
        
        $cabangLomba = CabangLomba::factory()->create();
        
        $response = $this->get(route('admin.cabang-lomba.edit', $cabangLomba));
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/CabangLomba/Edit'));
    }
}
