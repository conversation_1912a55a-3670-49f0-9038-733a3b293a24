<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\VerificationService;
use App\Traits\HasVerificationPermissions;
use App\Models\VerificationType;
use App\Models\Peserta;
use App\Models\Pendaftaran;
use App\Models\DokumenPeserta;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class VerificationDashboardController extends Controller
{
    use HasVerificationPermissions;

    protected VerificationService $verificationService;

    public function __construct(VerificationService $verificationService)
    {
        $this->verificationService = $verificationService;
    }

    /**
     * Display the verification dashboard
     */
    public function index(Request $request): Response
    {
        $this->requireVerificationAccess();

        // Get verification statistics
        $stats = $this->verificationService->getVerificationStatistics();

        // Get verification types with progress
        $verificationTypes = VerificationType::active()
            ->withCount([
                'participantVerifications',
                'participantVerifications as verified_count' => function($query) {
                    $query->where('status', VerificationService::STATUS_VERIFIED);
                },
                'participantVerifications as rejected_count' => function($query) {
                    $query->where('status', VerificationService::STATUS_REJECTED);
                }
            ])
            ->get()
            ->map(function($type) {
                $total = $type->participant_verifications_count;
                $verified = $type->verified_count;
                $rejected = $type->rejected_count;
                $pending = $total - $verified - $rejected;
                
                return [
                    'id' => $type->id_verification_type,
                    'name' => $type->name,
                    'code' => $type->code,
                    'description' => $type->description,
                    'total' => $total,
                    'verified' => $verified,
                    'rejected' => $rejected,
                    'pending' => $pending,
                    'progress_percentage' => $total > 0 ? round(($verified / $total) * 100, 1) : 0
                ];
            });

        // Recent verification activities
        $recentActivities = collect([
            // Recent participant verifications
            ...Peserta::with(['verifications.verificationType', 'verifications.verifiedBy'])
                ->whereHas('verifications', function($query) {
                    $query->whereNotNull('verified_at')
                          ->orderBy('verified_at', 'desc');
                })
                ->limit(10)
                ->get()
                ->flatMap(function($peserta) {
                    return $peserta->verifications->map(function($verification) use ($peserta) {
                        return [
                            'type' => 'participant_verification',
                            'title' => "Verifikasi {$verification->verificationType->name}",
                            'description' => "Peserta: {$peserta->nama_lengkap}",
                            'status' => $verification->status,
                            'verified_by' => $verification->verifiedBy->name ?? 'Unknown',
                            'verified_at' => $verification->verified_at,
                            'url' => route('admin.participant-verifications.show', $peserta->id_peserta)
                        ];
                    });
                }),
            
            // Recent document verifications
            ...DokumenPeserta::with(['pendaftaran.peserta', 'verifiedBy'])
                ->whereNotNull('verified_at')
                ->orderBy('verified_at', 'desc')
                ->limit(10)
                ->get()
                ->map(function($dokumen) {
                    return [
                        'type' => 'document_verification',
                        'title' => 'Verifikasi Dokumen',
                        'description' => "Dokumen: {$dokumen->nama_file} - Peserta: {$dokumen->pendaftaran->peserta->nama_lengkap}",
                        'status' => $dokumen->status_verifikasi === 'approved' ? 'verified' : 'rejected',
                        'verified_by' => $dokumen->verifiedBy->name ?? 'Unknown',
                        'verified_at' => $dokumen->verified_at,
                        'url' => route('admin.dokumen-verifikasi.show', $dokumen->id_dokumen)
                    ];
                })
        ])
        ->sortByDesc('verified_at')
        ->take(15)
        ->values();

        // Pending items that need attention
        $pendingItems = [
            'registrations' => Pendaftaran::whereIn('status_pendaftaran', ['submitted', 'paid'])->count(),
            'documents' => DokumenPeserta::where('status_verifikasi', 'pending')->count(),
            'participants' => Peserta::whereDoesntHave('verifications', function($query) {
                $query->where('status', VerificationService::STATUS_VERIFIED);
            })->count()
        ];

        // Quick actions based on user permissions
        $quickActions = [
            [
                'title' => 'Verifikasi Pendaftaran',
                'description' => 'Verifikasi pendaftaran peserta yang masuk',
                'icon' => 'user-check',
                'url' => route('admin.registration-verification.index'),
                'count' => $pendingItems['registrations'],
                'color' => 'blue'
            ],
            [
                'title' => 'Verifikasi Dokumen',
                'description' => 'Verifikasi dokumen yang diunggah peserta',
                'icon' => 'file-check',
                'url' => route('admin.dokumen-verifikasi.index'),
                'count' => $pendingItems['documents'],
                'color' => 'green'
            ],
            [
                'title' => 'Verifikasi Peserta',
                'description' => 'Verifikasi data peserta (NIK, dll)',
                'icon' => 'users',
                'url' => route('admin.participant-verifications.index'),
                'count' => $pendingItems['participants'],
                'color' => 'purple'
            ]
        ];

        return Inertia::render('Admin/VerificationDashboard/Index', [
            'stats' => $stats,
            'verificationTypes' => $verificationTypes,
            'recentActivities' => $recentActivities,
            'pendingItems' => $pendingItems,
            'quickActions' => $quickActions,
            'userPermissions' => $this->getPermissionsSummary()
        ]);
    }

    /**
     * Get verification progress for a specific type
     */
    public function getVerificationProgress(Request $request, VerificationType $verificationType)
    {
        $this->requireVerificationAccess();

        $participants = Peserta::with(['verifications' => function($query) use ($verificationType) {
            $query->where('verification_type_id', $verificationType->id_verification_type);
        }])->get();

        $progress = $participants->map(function($peserta) use ($verificationType) {
            return $this->verificationService->calculateParticipantProgress($peserta);
        });

        return response()->json([
            'verification_type' => $verificationType,
            'progress' => $progress,
            'summary' => [
                'total' => $participants->count(),
                'verified' => $progress->where('status', VerificationService::STATUS_VERIFIED)->count(),
                'rejected' => $progress->where('status', VerificationService::STATUS_REJECTED)->count(),
                'pending' => $progress->where('status', VerificationService::STATUS_PENDING)->count()
            ]
        ]);
    }
}
