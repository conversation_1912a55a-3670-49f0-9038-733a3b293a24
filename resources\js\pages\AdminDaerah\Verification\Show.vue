<script setup lang="ts">
import { Head, router } from '@inertiajs/vue3'
import { ref } from 'vue'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/ui/Heading.vue'
import Card from '@/components/ui/Card.vue'
import CardContent from '@/components/ui/CardContent.vue'
import CardHeader from '@/components/ui/CardHeader.vue'
import CardTitle from '@/components/ui/CardTitle.vue'
import Button from '@/components/ui/Button.vue'
import TextLink from '@/components/ui/TextLink.vue'
import Icon from '@/components/ui/Icon.vue'
import Badge from '@/components/ui/Badge.vue'
import Textarea from '@/components/ui/Textarea.vue'
import { useToast } from '@/composables/useToast'

interface User {
  username: string
  email: string
}

interface Wilayah {
  nama_wilayah: string
}

interface DocumentType {
  id_document_type: number
  name: string
  description: string
}

interface Document {
  id_dokumen: number
  nama_file: string
  status_verifikasi: string
  document_type: DocumentType
}

interface Registration {
  id_pendaftaran: number
  nomor_pendaftaran: string
  status_pendaftaran: string
  golongan: {
    nama_golongan: string
    cabang_lomba: {
      nama_cabang: string
    }
  }
}

interface DocumentStatus {
  document_type: DocumentType
  is_required: boolean
  uploaded: boolean
  document: Document | null
}

interface RegistrationWithRequirements {
  registration: Registration
  required_documents: DocumentStatus[]
  documents_complete: boolean
}

interface Participant {
  id_peserta: number
  nama_lengkap: string
  nik: string
  tempat_lahir: string
  tanggal_lahir: string
  jenis_kelamin: string
  alamat: string
  no_telepon: string | null
  email: string | null
  nama_ayah: string | null
  nama_ibu: string | null
  pekerjaan: string | null
  instansi_asal: string | null
  status_peserta: string
  registration_type: string
  regional_verification_status: string
  regional_verification_notes: string | null
  regional_verified_at: string | null
  created_at: string
  user: User
  wilayah: Wilayah
  regional_verified_by?: {
    nama_lengkap: string
  }
}

interface Props {
  participant: Participant
  registrations: RegistrationWithRequirements[]
}

const props = defineProps<Props>()
const { showToast } = useToast()

// Reactive data
const isSubmitting = ref(false)
const verificationNotes = ref(props.participant.regional_verification_notes || '')

// Methods
const verify = async (action: 'verify' | 'reject') => {
  let notes = verificationNotes.value

  if (action === 'reject' && !notes.trim()) {
    notes = prompt('Masukkan alasan penolakan:') || ''
    if (!notes.trim()) return
  }

  if (action === 'verify' && !notes.trim()) {
    notes = 'Data peserta telah diverifikasi dan memenuhi persyaratan'
  }

  isSubmitting.value = true

  try {
    await router.post(route('admin-daerah.verification.verify', props.participant.id_peserta), {
      action,
      notes
    }, {
      onSuccess: () => {
        showToast(
          action === 'verify' 
            ? 'Peserta berhasil diverifikasi' 
            : 'Peserta berhasil ditolak',
          'success'
        )
      },
      onError: () => {
        showToast('Terjadi kesalahan', 'error')
      }
    })
  } finally {
    isSubmitting.value = false
  }
}

const getStatusColor = (status: string): string => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    verified: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

const getDocumentStatusColor = (uploaded: boolean, required: boolean): string => {
  if (!required) return 'bg-gray-100 text-gray-800'
  return uploaded ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
}

const breadcrumbItems = [
  { label: 'Dashboard', href: route('admin-daerah.dashboard') },
  { label: 'Verifikasi Regional', href: route('admin-daerah.verification.index') },
  { label: props.participant.nama_lengkap, href: route('admin-daerah.verification.show', props.participant.id_peserta) }
]
</script>

<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head :title="`Verifikasi ${participant.nama_lengkap} - Admin Daerah`" />
    
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <Heading :title="`Verifikasi ${participant.nama_lengkap}`" />
        <div class="flex space-x-2">
          <Button 
            v-if="participant.regional_verification_status === 'pending'"
            @click="verify('verify')"
            :disabled="isSubmitting"
            class="bg-green-600 hover:bg-green-700"
          >
            <Icon name="check" class="w-4 h-4 mr-2" />
            Verifikasi
          </Button>
          <Button 
            v-if="participant.regional_verification_status === 'pending'"
            @click="verify('reject')"
            :disabled="isSubmitting"
            variant="destructive"
          >
            <Icon name="x" class="w-4 h-4 mr-2" />
            Tolak
          </Button>
          <TextLink 
            :href="route('admin-daerah.verification.index')"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Icon name="arrow-left" class="w-4 h-4 mr-2" />
            Kembali
          </TextLink>
        </div>
      </div>

      <!-- Verification Status -->
      <Card>
        <CardHeader>
          <CardTitle>Status Verifikasi</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="flex items-center space-x-4">
            <Badge :class="getStatusColor(participant.regional_verification_status)" class="text-lg px-4 py-2">
              {{ participant.regional_verification_status }}
            </Badge>
            <div v-if="participant.regional_verified_by" class="text-sm text-gray-600">
              Diverifikasi oleh {{ participant.regional_verified_by.nama_lengkap }}
              pada {{ new Date(participant.regional_verified_at!).toLocaleString('id-ID') }}
            </div>
          </div>
          <div v-if="participant.regional_verification_notes" class="mt-4">
            <h4 class="font-medium text-gray-900 mb-2">Catatan Verifikasi:</h4>
            <p class="text-gray-700 bg-gray-50 p-3 rounded-md">{{ participant.regional_verification_notes }}</p>
          </div>
        </CardContent>
      </Card>

      <!-- Participant Information -->
      <Card>
        <CardHeader>
          <CardTitle>Informasi Peserta</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Data Pribadi</h4>
              <dl class="space-y-2">
                <div>
                  <dt class="text-sm font-medium text-gray-500">Nama Lengkap</dt>
                  <dd class="text-sm text-gray-900">{{ participant.nama_lengkap }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">NIK</dt>
                  <dd class="text-sm text-gray-900">{{ participant.nik }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Tempat, Tanggal Lahir</dt>
                  <dd class="text-sm text-gray-900">
                    {{ participant.tempat_lahir }}, {{ new Date(participant.tanggal_lahir).toLocaleDateString('id-ID') }}
                  </dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Jenis Kelamin</dt>
                  <dd class="text-sm text-gray-900">{{ participant.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Alamat</dt>
                  <dd class="text-sm text-gray-900">{{ participant.alamat }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Wilayah</dt>
                  <dd class="text-sm text-gray-900">{{ participant.wilayah.nama_wilayah }}</dd>
                </div>
              </dl>
            </div>
            <div>
              <h4 class="font-medium text-gray-900 mb-4">Kontak & Lainnya</h4>
              <dl class="space-y-2">
                <div>
                  <dt class="text-sm font-medium text-gray-500">Email</dt>
                  <dd class="text-sm text-gray-900">{{ participant.email || participant.user.email }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">No. Telepon</dt>
                  <dd class="text-sm text-gray-900">{{ participant.no_telepon || '-' }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Nama Ayah</dt>
                  <dd class="text-sm text-gray-900">{{ participant.nama_ayah || '-' }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Nama Ibu</dt>
                  <dd class="text-sm text-gray-900">{{ participant.nama_ibu || '-' }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Pekerjaan</dt>
                  <dd class="text-sm text-gray-900">{{ participant.pekerjaan || '-' }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">Instansi Asal</dt>
                  <dd class="text-sm text-gray-900">{{ participant.instansi_asal || '-' }}</dd>
                </div>
              </dl>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Registrations and Documents -->
      <div v-for="(regData, index) in registrations" :key="regData.registration.id_pendaftaran">
        <Card>
          <CardHeader>
            <CardTitle>
              Pendaftaran {{ index + 1 }}: {{ regData.registration.golongan.cabang_lomba.nama_cabang }} - 
              {{ regData.registration.golongan.nama_golongan }}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="mb-4">
              <div class="flex items-center space-x-2">
                <span class="text-sm font-medium">Nomor Pendaftaran:</span>
                <span class="text-sm">{{ regData.registration.nomor_pendaftaran }}</span>
                <Badge :class="regData.documents_complete ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                  {{ regData.documents_complete ? 'Dokumen Lengkap' : 'Dokumen Belum Lengkap' }}
                </Badge>
              </div>
            </div>

            <div class="space-y-3">
              <h5 class="font-medium text-gray-900">Dokumen yang Diperlukan:</h5>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div v-for="docStatus in regData.required_documents" :key="docStatus.document_type.id_document_type" 
                     class="border rounded-lg p-4">
                  <div class="flex items-center justify-between mb-2">
                    <h6 class="font-medium text-sm">{{ docStatus.document_type.name }}</h6>
                    <Badge :class="getDocumentStatusColor(docStatus.uploaded, docStatus.is_required)">
                      {{ docStatus.uploaded ? 'Uploaded' : 'Belum Upload' }}
                    </Badge>
                  </div>
                  <p class="text-xs text-gray-600 mb-2">{{ docStatus.document_type.description }}</p>
                  <div v-if="docStatus.document" class="text-xs">
                    <div class="flex items-center space-x-2">
                      <Icon name="file" class="w-3 h-3" />
                      <span>{{ docStatus.document.nama_file }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Verification Notes -->
      <Card v-if="participant.regional_verification_status === 'pending'">
        <CardHeader>
          <CardTitle>Catatan Verifikasi</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            v-model="verificationNotes"
            placeholder="Tambahkan catatan verifikasi (opsional)..."
            rows="4"
          />
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>
