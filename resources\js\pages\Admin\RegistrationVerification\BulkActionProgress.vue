<template>
  <Dialog :open="show" @update:open="$emit('update:show', $event)">
    <DialogContent class="sm:max-w-lg">
      <DialogHeader>
        <DialogTitle>{{ title }}</DialogTitle>
        <DialogDescription>
          {{ description }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- Progress Overview -->
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">Progress</span>
            <span class="text-sm text-gray-500">{{ processed }}/{{ total }}</span>
          </div>
          
          <!-- Progress Bar -->
          <div class="w-full bg-gray-200 rounded-full h-3">
            <div 
              class="h-3 rounded-full transition-all duration-300"
              :class="{
                'bg-blue-600': status === 'processing',
                'bg-green-600': status === 'completed',
                'bg-red-600': status === 'error'
              }"
              :style="{ width: `${progressPercentage}%` }"
            ></div>
          </div>
          
          <!-- Status Message -->
          <div class="flex items-center space-x-2">
            <Icon 
              :name="getStatusIcon()" 
              class="w-5 h-5"
              :class="{
                'text-blue-600 animate-spin': status === 'processing',
                'text-green-600': status === 'completed',
                'text-red-600': status === 'error'
              }"
            />
            <span class="text-sm font-medium">{{ getStatusMessage() }}</span>
          </div>
        </div>

        <!-- Processing Details -->
        <div v-if="showDetails" class="space-y-3">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium">Detail Progress</span>
            <Button 
              @click="showDetails = !showDetails" 
              variant="ghost" 
              size="sm"
            >
              <Icon name="chevronUp" class="w-4 h-4" />
            </Button>
          </div>
          
          <div class="max-h-40 overflow-y-auto space-y-2 border rounded p-3">
            <div 
              v-for="(item, index) in processedItems" 
              :key="index"
              class="flex items-center justify-between text-sm"
            >
              <span class="truncate">{{ item.name }}</span>
              <Icon 
                :name="item.success ? 'check' : 'x'" 
                class="w-4 h-4 flex-shrink-0"
                :class="{
                  'text-green-600': item.success,
                  'text-red-600': !item.success
                }"
              />
            </div>
          </div>
        </div>
        
        <!-- Summary -->
        <div v-if="status === 'completed'" class="p-4 rounded-lg border">
          <div class="flex items-start space-x-2">
            <Icon name="checkCircle" class="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
            <div>
              <p class="font-medium text-green-800">Proses Selesai</p>
              <div class="text-sm text-green-700 mt-1 space-y-1">
                <p>✓ {{ successCount }} item berhasil diproses</p>
                <p v-if="errorCount > 0" class="text-red-700">✗ {{ errorCount }} item gagal diproses</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Summary -->
        <div v-if="status === 'error'" class="p-4 rounded-lg border border-red-200 bg-red-50">
          <div class="flex items-start space-x-2">
            <Icon name="xCircle" class="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <p class="font-medium text-red-800">Terjadi Kesalahan</p>
              <p class="text-sm text-red-700 mt-1">{{ errorMessage }}</p>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <DialogFooter>
          <Button 
            v-if="status === 'processing'"
            @click="cancelProcess"
            variant="outline"
            :disabled="!canCancel"
          >
            <Icon name="x" class="w-4 h-4 mr-2" />
            Batalkan
          </Button>
          
          <Button 
            v-if="status === 'completed' || status === 'error'"
            @click="$emit('update:show', false)"
            variant="outline"
          >
            Tutup
          </Button>
          
          <Button 
            v-if="status === 'error' && canRetry"
            @click="retryProcess"
            class="bg-blue-600 hover:bg-blue-700"
          >
            <Icon name="refresh" class="w-4 h-4 mr-2" />
            Coba Lagi
          </Button>
        </DialogFooter>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  show: boolean
  title: string
  description: string
  total: number
  processed: number
  status: 'idle' | 'processing' | 'completed' | 'error'
  processedItems: Array<{ name: string; success: boolean }>
  errorMessage?: string
  canCancel?: boolean
  canRetry?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  canCancel: true,
  canRetry: true
})

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'cancel': []
  'retry': []
}>()

// Reactive data
const showDetails = ref(false)

// Computed
const progressPercentage = computed(() => {
  if (props.total === 0) return 0
  return Math.round((props.processed / props.total) * 100)
})

const successCount = computed(() => {
  return props.processedItems.filter(item => item.success).length
})

const errorCount = computed(() => {
  return props.processedItems.filter(item => !item.success).length
})

// Watch for status changes
watch(() => props.status, (newStatus) => {
  if (newStatus === 'completed' || newStatus === 'error') {
    showDetails.value = true
  }
})

// Methods
const getStatusIcon = () => {
  const icons = {
    'idle': 'clock',
    'processing': 'loader',
    'completed': 'checkCircle',
    'error': 'xCircle'
  }
  return icons[props.status] || 'clock'
}

const getStatusMessage = () => {
  const messages = {
    'idle': 'Menunggu...',
    'processing': 'Memproses...',
    'completed': 'Selesai',
    'error': 'Terjadi kesalahan'
  }
  return messages[props.status] || 'Unknown'
}

const cancelProcess = () => {
  emit('cancel')
}

const retryProcess = () => {
  emit('retry')
}
</script>
