<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CabangLomba;
use App\Models\Golongan;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CompetitionController extends Controller
{
    /**
     * Display a listing of competitions
     */
    public function index(Request $request): JsonResponse
    {
        $query = CabangLomba::with(['golongan' => function ($query) {
            $query->where('status', 'aktif')
                  ->orderBy('nama_golongan');
        }])->where('status', 'aktif');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nama_cabang', 'like', "%{$search}%")
                  ->orWhere('deskripsi', 'like', "%{$search}%");
            });
        }

        // Filter by gender
        if ($request->has('jenis_kelamin') && $request->jenis_kelamin !== 'all') {
            $query->whereHas('golongan', function ($q) use ($request) {
                $q->where('jenis_kelamin', $request->jenis_kelamin);
            });
        }

        $cabangLomba = $query->orderBy('nama_cabang')->get();

        // Get statistics
        $stats = [
            'total_cabang' => CabangLomba::where('status', 'aktif')->count(),
            'total_golongan' => Golongan::where('status', 'aktif')->count(),
            'total_putra' => Golongan::where('status', 'aktif')
                                   ->where('jenis_kelamin', 'putra')
                                   ->count(),
            'total_putri' => Golongan::where('status', 'aktif')
                                   ->where('jenis_kelamin', 'putri')
                                   ->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'competitions' => $cabangLomba,
                'stats' => $stats,
                'filters' => $request->only(['search', 'jenis_kelamin'])
            ]
        ]);
    }

    /**
     * Display the specified competition
     */
    public function show(string $id): JsonResponse
    {
        $cabangLomba = CabangLomba::with(['golongan' => function ($query) {
            $query->where('status', 'aktif')
                  ->orderBy('nama_golongan');
        }])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $cabangLomba
        ]);
    }

    /**
     * Get categories for a specific competition
     */
    public function categories(string $id): JsonResponse
    {
        $cabangLomba = CabangLomba::with(['golongan' => function ($query) {
            $query->where('status', 'aktif')
                  ->orderBy('nama_golongan');
        }])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'competition' => $cabangLomba,
                'categories' => $cabangLomba->golongan
            ]
        ]);
    }

    /**
     * Get groups for a specific category
     */
    public function groups(string $id): JsonResponse
    {
        $golongan = Golongan::with(['cabangLomba', 'pendaftaran' => function ($query) {
            $query->whereIn('status_pendaftaran', ['approved', 'verified'])
                  ->with('peserta');
        }])->findOrFail($id);

        $stats = [
            'total_pendaftar' => $golongan->pendaftaran->count(),
            'putra' => $golongan->pendaftaran->filter(function ($p) {
                return $p->peserta->jenis_kelamin === 'laki-laki';
            })->count(),
            'putri' => $golongan->pendaftaran->filter(function ($p) {
                return $p->peserta->jenis_kelamin === 'perempuan';
            })->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'group' => $golongan,
                'stats' => $stats,
                'participants' => $golongan->pendaftaran->map(function ($pendaftaran) {
                    return [
                        'id' => $pendaftaran->id_pendaftaran,
                        'nomor_peserta' => $pendaftaran->nomor_peserta,
                        'nama' => $pendaftaran->peserta->nama_lengkap,
                        'wilayah' => $pendaftaran->peserta->wilayah->nama_wilayah ?? null,
                        'status' => $pendaftaran->status_pendaftaran,
                    ];
                })
            ]
        ]);
    }
}
