<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DokumenPeserta;
use App\Services\VerificationService;
use App\Traits\HasVerificationPermissions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class DokumenVerifikasiController extends Controller
{
    use HasVerificationPermissions;

    protected VerificationService $verificationService;

    public function __construct(VerificationService $verificationService)
    {
        $this->verificationService = $verificationService;
    }

    /**
     * Display a listing of documents for verification
     */
    public function index(Request $request): Response
    {
        $this->requireVerificationAccess();

        $query = DokumenPeserta::with([
            'pendaftaran.peserta.wilayah',
            'pendaftaran.peserta.verifications.verificationType',
            'pendaftaran.golongan.cabangLomba',
            'uploadedBy',
            'verifiedBy'
        ]);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status_verifikasi', $request->status);
        }

        // Filter by document type
        if ($request->filled('jenis_dokumen')) {
            $query->where('jenis_dokumen', $request->jenis_dokumen);
        }

        // No regional filtering needed since only admin provinsi can access

        // Search by participant name or registration number
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('pendaftaran', function ($q) use ($search) {
                $q->where('nomor_pendaftaran', 'like', "%{$search}%")
                  ->orWhereHas('peserta', function ($pq) use ($search) {
                      $pq->where('nama_lengkap', 'like', "%{$search}%");
                  });
            });
        }

        $dokumen = $query->orderBy('created_at', 'desc')->paginate(20);

        $jenisOptions = [
            'foto' => 'Foto Peserta',
            'ktp' => 'KTP',
            'kartu_keluarga' => 'Kartu Keluarga',
            'surat_rekomendasi' => 'Surat Rekomendasi',
            'ijazah' => 'Ijazah Terakhir',
            'sertifikat' => 'Sertifikat',
            'lainnya' => 'Lainnya'
        ];

        return Inertia::render('Admin/DokumenVerifikasi/Index', [
            'dokumen' => $dokumen,
            'filters' => $request->only(['status', 'jenis_dokumen', 'search']),
            'jenisOptions' => $jenisOptions
        ]);
    }

    /**
     * Show the form for verifying a specific document
     */
    public function show(DokumenPeserta $dokumen): Response
    {
        $this->requireVerificationAccess();

        $dokumen->load([
            'pendaftaran.peserta.wilayah',
            'pendaftaran.peserta.verifications.verificationType',
            'pendaftaran.golongan.cabangLomba',
            'uploadedBy',
            'verifiedBy'
        ]);

        return Inertia::render('Admin/DokumenVerifikasi/Show', [
            'dokumen' => $dokumen
        ]);
    }

    /**
     * Verify a document (approve or reject)
     */
    public function verify(Request $request, DokumenPeserta $dokumen)
    {
        $this->requireVerificationAccess();

        $validated = $request->validate([
            'status_verifikasi' => 'required|in:approved,rejected',
            'catatan_verifikasi' => 'nullable|string|max:500'
        ]);

        $status = $validated['status_verifikasi'] === 'approved'
            ? VerificationService::STATUS_VERIFIED
            : VerificationService::STATUS_REJECTED;

        $this->verificationService->verifyDocument(
            $dokumen,
            $status,
            $validated['catatan_verifikasi']
        );

        $statusText = $this->verificationService->getStatusMessage($status, 'document');

        return back()->with('success', "Dokumen {$statusText}.");
    }

    /**
     * Download a document for verification
     */
    public function download(DokumenPeserta $dokumen)
    {
        $this->requireVerificationAccess();

        if (!file_exists(storage_path('app/public/' . $dokumen->path_file))) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->download(
            storage_path('app/public/' . $dokumen->path_file),
            $dokumen->nama_file
        );
    }

    /**
     * Bulk verify documents
     */
    public function bulkVerify(Request $request)
    {
        $this->requireVerificationAccess();

        $validated = $request->validate([
            'dokumen_ids' => 'required|array',
            'dokumen_ids.*' => 'exists:dokumen_peserta,id_dokumen',
            'status_verifikasi' => 'required|in:approved,rejected',
            'catatan_verifikasi' => 'nullable|string|max:500'
        ]);

        $query = DokumenPeserta::whereIn('id_dokumen', $validated['dokumen_ids']);

        // No regional filtering needed since only admin provinsi can access

        $updated = $query->update([
            'status_verifikasi' => $validated['status_verifikasi'],
            'catatan_verifikasi' => $validated['catatan_verifikasi'],
            'verified_by' => Auth::id(),
            'verified_at' => now()
        ]);

        $message = $validated['status_verifikasi'] === 'approved'
            ? "Berhasil menyetujui {$updated} dokumen."
            : "Berhasil menolak {$updated} dokumen.";

        return back()->with('success', $message);
    }
}
