<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Pembayaran;
use App\Models\Pendaftaran;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        $query = Pembayaran::with(['pendaftaran.peserta', 'pendaftaran.golongan.cabangLomba']);

        // Filter based on user role
        if ($user->role === 'peserta') {
            $query->whereHas('pendaftaran.peserta', function ($q) use ($user) {
                $q->where('id_user', $user->id_user);
            });
        } elseif ($user->role === 'admin_daerah') {
            $query->whereHas('pendaftaran.peserta', function ($q) use ($user) {
                $q->where('id_wilayah', $user->id_wilayah);
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status_pembayaran', $request->status);
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $payments
        ]);
    }

    /**
     * Display the specified payment
     */
    public function show(Pendaftaran $registration): JsonResponse
    {
        $user = Auth::user();

        // Check access permissions
        if ($user->role === 'peserta' && $registration->peserta->id_user !== $user->id_user) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if ($user->role === 'admin_daerah' && $registration->peserta->id_wilayah !== $user->id_wilayah) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $payment = $registration->pembayaran()->with(['pendaftaran.peserta', 'pendaftaran.golongan.cabangLomba'])->first();

        return response()->json([
            'success' => true,
            'data' => $payment
        ]);
    }

    /**
     * Process payment for registration
     */
    public function pay(Request $request, Pendaftaran $registration): JsonResponse
    {
        $user = Auth::user();

        // Check access permissions
        if ($user->role === 'peserta' && $registration->peserta->id_user !== $user->id_user) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        // Check if registration is eligible for payment
        if (!in_array($registration->status_pendaftaran, ['submitted', 'approved'])) {
            return response()->json([
                'success' => false,
                'message' => 'Registration is not eligible for payment'
            ], 422);
        }

        // Check if payment already exists
        if ($registration->pembayaran()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Payment already exists for this registration'
            ], 422);
        }

        $validated = $request->validate([
            'metode_pembayaran' => 'required|in:transfer_bank,e_wallet,cash',
            'jumlah_bayar' => 'required|numeric|min:0',
        ]);

        // Generate payment code
        $kodePembayaran = $this->generatePaymentCode();

        $payment = Pembayaran::create([
            'id_pendaftaran' => $registration->id_pendaftaran,
            'kode_pembayaran' => $kodePembayaran,
            'jumlah_bayar' => $validated['jumlah_bayar'],
            'metode_pembayaran' => $validated['metode_pembayaran'],
            'status_pembayaran' => 'pending',
            'tanggal_pembayaran' => now(),
        ]);

        // Update registration status if needed
        if ($registration->status_pendaftaran === 'submitted') {
            $registration->update(['status_pendaftaran' => 'payment_pending']);
        }

        return response()->json([
            'success' => true,
            'message' => 'Payment initiated successfully',
            'data' => $payment->load(['pendaftaran.peserta', 'pendaftaran.golongan.cabangLomba'])
        ], 201);
    }

    /**
     * Upload payment proof
     */
    public function uploadProof(Request $request, Pembayaran $payment): JsonResponse
    {
        $user = Auth::user();

        // Check access permissions
        if ($user->role === 'peserta' && $payment->pendaftaran->peserta->id_user !== $user->id_user) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validated = $request->validate([
            'bukti_pembayaran' => 'required|file|mimes:jpg,jpeg,png,pdf|max:2048',
            'keterangan' => 'nullable|string|max:500',
        ]);

        // Check if payment can be updated
        if (!in_array($payment->status_pembayaran, ['pending', 'rejected'])) {
            return response()->json([
                'success' => false,
                'message' => 'Payment proof cannot be uploaded in current status'
            ], 422);
        }

        // Store the file
        $file = $request->file('bukti_pembayaran');
        $filename = 'payment_proof_' . $payment->id_pembayaran . '_' . time() . '.' . $file->getClientOriginalExtension();
        $path = $file->storeAs('payment_proofs', $filename, 'public');

        $payment->update([
            'bukti_pembayaran' => $path,
            'keterangan' => $validated['keterangan'] ?? null,
            'status_pembayaran' => 'submitted',
            'tanggal_upload_bukti' => now(),
        ]);

        // Update registration status
        $payment->pendaftaran->update(['status_pendaftaran' => 'payment_submitted']);

        return response()->json([
            'success' => true,
            'message' => 'Payment proof uploaded successfully',
            'data' => $payment->load(['pendaftaran.peserta', 'pendaftaran.golongan.cabangLomba'])
        ]);
    }

    /**
     * Webhook handler for Midtrans
     */
    public function midtransWebhook(Request $request): JsonResponse
    {
        // Verify signature and process Midtrans notification
        $serverKey = config('services.midtrans.server_key');
        $hashed = hash('sha512', $request->order_id . $request->status_code . $request->gross_amount . $serverKey);

        if ($hashed !== $request->signature_key) {
            return response()->json(['message' => 'Invalid signature'], 401);
        }

        // Find payment by order ID
        $payment = Pembayaran::where('kode_pembayaran', $request->order_id)->first();

        if (!$payment) {
            return response()->json(['message' => 'Payment not found'], 404);
        }

        // Update payment status based on Midtrans response
        switch ($request->transaction_status) {
            case 'settlement':
                $payment->update([
                    'status_pembayaran' => 'approved',
                    'tanggal_verifikasi' => now(),
                ]);
                $payment->pendaftaran->update(['status_pendaftaran' => 'approved']);
                break;
            case 'pending':
                $payment->update(['status_pembayaran' => 'pending']);
                break;
            case 'deny':
            case 'cancel':
            case 'expire':
                $payment->update(['status_pembayaran' => 'rejected']);
                break;
        }

        return response()->json(['success' => true]);
    }

    /**
     * Webhook handler for Xendit
     */
    public function xenditWebhook(Request $request): JsonResponse
    {
        // Verify Xendit webhook token
        $webhookToken = config('services.xendit.webhook_token');

        if ($request->header('x-callback-token') !== $webhookToken) {
            return response()->json(['message' => 'Invalid token'], 401);
        }

        // Find payment by external ID
        $payment = Pembayaran::where('kode_pembayaran', $request->external_id)->first();

        if (!$payment) {
            return response()->json(['message' => 'Payment not found'], 404);
        }

        // Update payment status based on Xendit response
        switch ($request->status) {
            case 'PAID':
                $payment->update([
                    'status_pembayaran' => 'approved',
                    'tanggal_verifikasi' => now(),
                ]);
                $payment->pendaftaran->update(['status_pendaftaran' => 'approved']);
                break;
            case 'EXPIRED':
                $payment->update(['status_pembayaran' => 'rejected']);
                break;
        }

        return response()->json(['success' => true]);
    }

    /**
     * Generate unique payment code
     */
    private function generatePaymentCode(): string
    {
        $prefix = 'PAY';
        $year = date('Y');
        $month = date('m');

        $lastNumber = Pembayaran::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->count();

        return $prefix . $year . $month . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }
}
