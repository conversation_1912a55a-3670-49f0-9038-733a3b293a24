<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Pembayaran;
use App\Models\Pendaftaran;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments - DISABLED
     */
    public function index(Request $request): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Payment functionality is temporarily disabled. Registration is currently free.',
            'error' => 'PAYMENT_DISABLED'
        ], 503);
    }

    /**
     * Display the specified payment - DISABLED
     */
    public function show(Pendaftaran $registration): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Payment functionality is temporarily disabled. Registration is currently free.',
            'error' => 'PAYMENT_DISABLED'
        ], 503);
    }

    /**
     * Process payment for registration - DISABLED
     */
    public function pay(Request $request, Pendaftaran $registration): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Payment functionality is temporarily disabled. Registration is currently free.',
            'error' => 'PAYMENT_DISABLED'
        ], 503);

    }

    /**
     * Upload payment proof - DISABLED
     */
    public function uploadProof(Request $request, Pembayaran $payment): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Payment functionality is temporarily disabled. Registration is currently free.',
            'error' => 'PAYMENT_DISABLED'
        ], 503);

        // Update registration status
        $payment->pendaftaran->update(['status_pendaftaran' => 'payment_submitted']);

        return response()->json([
            'success' => true,
            'message' => 'Payment proof uploaded successfully',
            'data' => $payment->load(['pendaftaran.peserta', 'pendaftaran.golongan.cabangLomba'])
        ]);
    }

    /**
     * Webhook handler for Midtrans
     */
    public function midtransWebhook(Request $request): JsonResponse
    {
        // Verify signature and process Midtrans notification
        $serverKey = config('services.midtrans.server_key');
        $hashed = hash('sha512', $request->order_id . $request->status_code . $request->gross_amount . $serverKey);

        if ($hashed !== $request->signature_key) {
            return response()->json(['message' => 'Invalid signature'], 401);
        }

        // Find payment by order ID
        $payment = Pembayaran::where('kode_pembayaran', $request->order_id)->first();

        if (!$payment) {
            return response()->json(['message' => 'Payment not found'], 404);
        }

        // Update payment status based on Midtrans response
        switch ($request->transaction_status) {
            case 'settlement':
                $payment->update([
                    'status_pembayaran' => 'approved',
                    'tanggal_verifikasi' => now(),
                ]);
                $payment->pendaftaran->update(['status_pendaftaran' => 'approved']);
                break;
            case 'pending':
                $payment->update(['status_pembayaran' => 'pending']);
                break;
            case 'deny':
            case 'cancel':
            case 'expire':
                $payment->update(['status_pembayaran' => 'rejected']);
                break;
        }

        return response()->json(['success' => true]);
    }

    /**
     * Webhook handler for Xendit
     */
    public function xenditWebhook(Request $request): JsonResponse
    {
        // Verify Xendit webhook token
        $webhookToken = config('services.xendit.webhook_token');

        if ($request->header('x-callback-token') !== $webhookToken) {
            return response()->json(['message' => 'Invalid token'], 401);
        }

        // Find payment by external ID
        $payment = Pembayaran::where('kode_pembayaran', $request->external_id)->first();

        if (!$payment) {
            return response()->json(['message' => 'Payment not found'], 404);
        }

        // Update payment status based on Xendit response
        switch ($request->status) {
            case 'PAID':
                $payment->update([
                    'status_pembayaran' => 'approved',
                    'tanggal_verifikasi' => now(),
                ]);
                $payment->pendaftaran->update(['status_pendaftaran' => 'approved']);
                break;
            case 'EXPIRED':
                $payment->update(['status_pembayaran' => 'rejected']);
                break;
        }

        return response()->json(['success' => true]);
    }

    /**
     * Generate unique payment code
     */
    private function generatePaymentCode(): string
    {
        $prefix = 'PAY';
        $year = date('Y');
        $month = date('m');

        $lastNumber = Pembayaran::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->count();

        return $prefix . $year . $month . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }
}
