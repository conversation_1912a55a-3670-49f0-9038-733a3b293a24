<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pendaftaran', function (Blueprint $table) {
            // Add nomor_urut field for competition sequence numbering
            $table->integer('nomor_urut')->nullable()->after('nomor_peserta');

            // Add index for performance
            $table->index(['id_golongan', 'nomor_urut']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pendaftaran', function (Blueprint $table) {
            // Drop index first
            $table->dropIndex(['id_golongan', 'nomor_urut']);

            // Drop the column
            $table->dropColumn('nomor_urut');
        });
    }
};
