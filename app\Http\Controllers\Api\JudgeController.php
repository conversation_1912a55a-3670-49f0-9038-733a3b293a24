<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DewaHakim;
use App\Models\Pendaftaran;
use App\Models\NilaiPeserta;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class JudgeController extends Controller
{
    /**
     * Get judge dashboard data
     */
    public function dashboard(): JsonResponse
    {
        $user = Auth::user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return response()->json([
                'success' => false,
                'message' => 'Judge profile not found'
            ], 404);
        }

        // Get assignments based on judge specialization and region
        $assignmentsQuery = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'mimbar'
        ])->whereIn('status_pendaftaran', ['approved', 'verified']);

        // Filter by judge specialization
        if ($dewaHakim->spesialisasi) {
            $assignmentsQuery->whereHas('golongan.cabangLomba', function ($q) use ($dewaHakim) {
                $q->where('nama_cabang', 'like', '%' . $dewaHakim->spesialisasi . '%');
            });
        }

        // Regional judges should only score participants from their region
        if ($dewaHakim->tipe_hakim === 'kabupaten' && $dewaHakim->id_wilayah) {
            $assignmentsQuery->whereHas('peserta', function ($q) use ($dewaHakim) {
                $q->where('id_wilayah', $dewaHakim->id_wilayah);
            });
        }

        $totalAssignments = $assignmentsQuery->count();

        // Get pending assignments (not yet scored)
        $pendingAssignments = $assignmentsQuery->clone()
            ->whereDoesntHave('nilaiPeserta', function ($query) use ($dewaHakim) {
                $query->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim);
            })->count();

        // Get completed assignments
        $completedAssignments = $assignmentsQuery->clone()
            ->whereHas('nilaiPeserta', function ($query) use ($dewaHakim) {
                $query->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim);
            })->count();

        // Get recent scoring activities
        $recentScoring = NilaiPeserta::with([
            'pendaftaran.peserta',
            'pendaftaran.golongan.cabangLomba'
        ])->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim)
          ->orderBy('created_at', 'desc')
          ->limit(10)
          ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'judge' => $dewaHakim,
                'stats' => [
                    'total_assignments' => $totalAssignments,
                    'pending_assignments' => $pendingAssignments,
                    'completed_assignments' => $completedAssignments,
                    'completion_rate' => $totalAssignments > 0 ? round(($completedAssignments / $totalAssignments) * 100, 2) : 0
                ],
                'recent_scoring' => $recentScoring
            ]
        ]);
    }

    /**
     * Get assignments for the judge
     */
    public function assignments(Request $request): JsonResponse
    {
        $user = Auth::user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return response()->json([
                'success' => false,
                'message' => 'Judge profile not found'
            ], 404);
        }

        $query = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'mimbar',
            'nilaiPeserta' => function ($q) use ($dewaHakim) {
                $q->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim);
            }
        ])->whereIn('status_pendaftaran', ['approved', 'verified']);

        // Apply judge filters
        if ($dewaHakim->spesialisasi) {
            $query->whereHas('golongan.cabangLomba', function ($q) use ($dewaHakim) {
                $q->where('nama_cabang', 'like', '%' . $dewaHakim->spesialisasi . '%');
            });
        }

        if ($dewaHakim->tipe_hakim === 'kabupaten' && $dewaHakim->id_wilayah) {
            $query->whereHas('peserta', function ($q) use ($dewaHakim) {
                $q->where('id_wilayah', $dewaHakim->id_wilayah);
            });
        }

        // Filter by status
        if ($request->has('status')) {
            if ($request->status === 'pending') {
                $query->whereDoesntHave('nilaiPeserta', function ($q) use ($dewaHakim) {
                    $q->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim);
                });
            } elseif ($request->status === 'completed') {
                $query->whereHas('nilaiPeserta', function ($q) use ($dewaHakim) {
                    $q->where('id_dewan_hakim', $dewaHakim->id_dewan_hakim);
                });
            }
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nomor_peserta', 'like', "%{$search}%")
                  ->orWhereHas('peserta', function ($q) use ($search) {
                      $q->where('nama_lengkap', 'like', "%{$search}%");
                  });
            });
        }

        $assignments = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $assignments
        ]);
    }

    /**
     * Get judge profile
     */
    public function profile(): JsonResponse
    {
        $user = Auth::user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return response()->json([
                'success' => false,
                'message' => 'Judge profile not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $dewaHakim->load(['user', 'wilayah'])
        ]);
    }

    /**
     * Update judge profile
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $user = Auth::user();
        $dewaHakim = $user->dewaHakim;

        if (!$dewaHakim) {
            return response()->json([
                'success' => false,
                'message' => 'Judge profile not found'
            ], 404);
        }

        $validated = $request->validate([
            'nama_lengkap' => 'required|string|max:100',
            'no_telepon' => 'nullable|string|max:20',
            'alamat' => 'nullable|string',
            'pendidikan' => 'nullable|string|max:100',
            'pengalaman' => 'nullable|string',
        ]);

        $dewaHakim->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => $dewaHakim->load(['user', 'wilayah'])
        ]);
    }
}
