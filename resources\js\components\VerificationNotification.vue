<template>
  <Transition
    enter-active-class="transition ease-out duration-300"
    enter-from-class="opacity-0 transform translate-y-2"
    enter-to-class="opacity-100 transform translate-y-0"
    leave-active-class="transition ease-in duration-200"
    leave-from-class="opacity-100 transform translate-y-0"
    leave-to-class="opacity-0 transform translate-y-2"
  >
    <div
      v-if="show"
      class="fixed top-4 right-4 z-50 max-w-sm w-full bg-white rounded-lg shadow-lg border border-gray-200"
    >
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <Icon
              :name="type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : 'info'"
              :class="[
                'w-5 h-5',
                type === 'success' ? 'text-green-500' : type === 'error' ? 'text-red-500' : 'text-blue-500'
              ]"
            />
          </div>
          <div class="ml-3 w-0 flex-1">
            <p class="text-sm font-medium text-gray-900">
              {{ title }}
            </p>
            <p v-if="message" class="mt-1 text-sm text-gray-500">
              {{ message }}
            </p>
            
            <!-- Progress bar for ongoing operations -->
            <div v-if="showProgress && progress !== null" class="mt-2">
              <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                <span>Progress</span>
                <span>{{ progress }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-1.5">
                <div
                  class="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                  :style="{ width: `${progress}%` }"
                ></div>
              </div>
            </div>

            <!-- Error details -->
            <div v-if="errors && errors.length > 0" class="mt-2">
              <details class="text-xs">
                <summary class="cursor-pointer text-red-600 hover:text-red-700">
                  {{ errors.length }} error(s) - Click to view
                </summary>
                <div class="mt-1 max-h-20 overflow-y-auto bg-red-50 rounded p-2">
                  <ul class="text-red-700 space-y-1">
                    <li v-for="(error, index) in errors" :key="index">
                      • {{ error }}
                    </li>
                  </ul>
                </div>
              </details>
            </div>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button
              @click="$emit('close')"
              class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <span class="sr-only">Close</span>
              <Icon name="x" class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup>
import { watch } from 'vue'
import Icon from '@/components/Icon.vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'info',
    validator: (value) => ['success', 'error', 'info', 'warning'].includes(value)
  },
  title: {
    type: String,
    required: true
  },
  message: {
    type: String,
    default: ''
  },
  showProgress: {
    type: Boolean,
    default: false
  },
  progress: {
    type: Number,
    default: null
  },
  errors: {
    type: Array,
    default: () => []
  },
  autoClose: {
    type: Boolean,
    default: true
  },
  autoCloseDelay: {
    type: Number,
    default: 5000
  }
})

const emit = defineEmits(['close'])

// Auto close functionality
watch(() => props.show, (newValue) => {
  if (newValue && props.autoClose && !props.showProgress) {
    setTimeout(() => {
      emit('close')
    }, props.autoCloseDelay)
  }
})
</script>
