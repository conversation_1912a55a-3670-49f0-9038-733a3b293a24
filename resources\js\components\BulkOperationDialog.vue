<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>{{ title }}</DialogTitle>
        <DialogDescription>
          {{ description }}
        </DialogDescription>
      </DialogHeader>
      
      <div class="space-y-4">
        <!-- Selected items summary -->
        <div class="rounded-lg bg-gray-50 p-4">
          <div class="flex items-center justify-between text-sm">
            <span class="font-medium text-gray-700">Peserta yang dipilih:</span>
            <span class="font-bold text-gray-900">{{ selectedCount }} peserta</span>
          </div>
        </div>

        <!-- Operation details -->
        <div class="space-y-2">
          <Label for="operation-notes">Catatan (opsional)</Label>
          <Textarea
            id="operation-notes"
            v-model="notes"
            placeholder="Tambahkan catatan untuk operasi ini..."
            rows="3"
            :disabled="isProcessing"
          />
        </div>

        <!-- Progress indicator -->
        <div v-if="isProcessing" class="space-y-2">
          <div class="flex items-center justify-between text-sm">
            <span>Progress:</span>
            <span>{{ processedCount }}/{{ selectedCount }}</span>
          </div>
          <Progress :value="progressPercentage" class="h-2" />
          <div v-if="currentOperation" class="text-xs text-gray-500">
            {{ currentOperation }}
          </div>
        </div>

        <!-- Error summary -->
        <div v-if="errors.length > 0" class="rounded-lg bg-red-50 p-4">
          <div class="flex items-center mb-2">
            <Icon name="alert-circle" class="w-4 h-4 text-red-600 mr-2" />
            <span class="text-sm font-medium text-red-800">
              {{ errors.length }} error(s) occurred
            </span>
          </div>
          <div class="max-h-32 overflow-y-auto">
            <ul class="text-xs text-red-700 space-y-1">
              <li v-for="(error, index) in errors" :key="index">
                • {{ error }}
              </li>
            </ul>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button 
          variant="outline" 
          @click="handleCancel"
          :disabled="isProcessing"
        >
          {{ isProcessing ? 'Processing...' : 'Batal' }}
        </Button>
        <Button 
          @click="handleConfirm"
          :disabled="isProcessing || selectedCount === 0"
          :class="operationType === 'reject' ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'"
        >
          <Icon v-if="isProcessing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
          <Icon v-else :name="operationType === 'reject' ? 'x' : 'check'" class="w-4 h-4 mr-2" />
          {{ isProcessing ? 'Memproses...' : confirmButtonText }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Progress } from '@/components/ui/progress'
import Icon from '@/components/Icon.vue'

const props = defineProps({
  open: {
    type: Boolean,
    default: false
  },
  operationType: {
    type: String,
    required: true,
    validator: (value) => ['verify', 'reject'].includes(value)
  },
  selectedCount: {
    type: Number,
    default: 0
  },
  isProcessing: {
    type: Boolean,
    default: false
  },
  processedCount: {
    type: Number,
    default: 0
  },
  currentOperation: {
    type: String,
    default: ''
  },
  errors: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:open', 'confirm', 'cancel'])

const isOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const notes = ref('')

const title = computed(() => {
  return props.operationType === 'verify' 
    ? 'Konfirmasi Verifikasi Massal'
    : 'Konfirmasi Penolakan Massal'
})

const description = computed(() => {
  return props.operationType === 'verify'
    ? 'Anda akan memverifikasi peserta yang dipilih. Tindakan ini tidak dapat dibatalkan.'
    : 'Anda akan menolak verifikasi peserta yang dipilih. Tindakan ini tidak dapat dibatalkan.'
})

const confirmButtonText = computed(() => {
  return props.operationType === 'verify' 
    ? 'Verifikasi Sekarang'
    : 'Tolak Sekarang'
})

const progressPercentage = computed(() => {
  if (props.selectedCount === 0) return 0
  return Math.round((props.processedCount / props.selectedCount) * 100)
})

const handleConfirm = () => {
  emit('confirm', {
    operationType: props.operationType,
    notes: notes.value
  })
}

const handleCancel = () => {
  if (!props.isProcessing) {
    notes.value = ''
    emit('cancel')
  }
}

// Reset notes when dialog closes
watch(isOpen, (newValue) => {
  if (!newValue && !props.isProcessing) {
    notes.value = ''
  }
})
</script>
