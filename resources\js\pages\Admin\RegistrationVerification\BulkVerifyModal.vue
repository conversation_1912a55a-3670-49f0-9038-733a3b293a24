<template>
  <Dialog :open="show" @update:open="$emit('update:show', $event)">
    <DialogContent class="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>V<PERSON><PERSON><PERSON><PERSON></DialogTitle>
        <DialogDescription>
          Anda akan memverifikasi {{ selectedCount }} pendaftaran sekaligus.
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- Tabs for different bulk operations -->
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              class="py-2 px-1 border-b-2 font-medium text-sm transition-colors"
              :class="{
                'border-blue-500 text-blue-600': activeTab === tab.id,
                'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== tab.id
              }"
            >
              <Icon :name="tab.icon" class="w-4 h-4 mr-2 inline" />
              {{ tab.label }}
            </button>
          </nav>
        </div>

        <!-- Registration Verification Tab -->
        <div v-if="activeTab === 'registrations'">
          <!-- Selected Registrations Preview -->
          <div class="mb-6">
            <h3 class="font-medium mb-3">Pendaftaran yang Dipilih ({{ selectedCount }})</h3>
            <div class="max-h-40 overflow-y-auto border rounded-lg">
              <div
                v-for="registration in selectedRegistrations"
                :key="registration.id_pendaftaran"
                class="flex items-center justify-between p-3 border-b last:border-b-0 hover:bg-gray-50"
              >
                <div class="flex-1">
                  <p class="font-medium">{{ registration.peserta?.nama_lengkap }}</p>
                  <p class="text-sm text-gray-500">{{ registration.nomor_pendaftaran }} • {{ registration.golongan?.nama_golongan }}</p>
                </div>
                <Badge :variant="getStatusVariant(registration.status_pendaftaran)">
                  {{ getStatusLabel(registration.status_pendaftaran) }}
                </Badge>
              </div>
            </div>
          </div>

          <form @submit.prevent="handleSubmit" class="space-y-4">
            <!-- Action Selection -->
            <div class="space-y-2">
              <Label for="action">Aksi Verifikasi</Label>
              <div class="grid grid-cols-2 gap-3">
                <Button
                  type="button"
                  @click="form.action = 'approve'"
                  :variant="form.action === 'approve' ? 'default' : 'outline'"
                  class="justify-center"
                  :class="{
                    'bg-green-600 hover:bg-green-700 text-white': form.action === 'approve',
                    'border-green-600 text-green-600 hover:bg-green-50': form.action !== 'approve'
                  }"
                >
                  <Icon name="check" class="w-4 h-4 mr-2" />
                  Setujui Semua
                </Button>
                <Button
                  type="button"
                  @click="form.action = 'reject'"
                  :variant="form.action === 'reject' ? 'destructive' : 'outline'"
                  class="justify-center"
                  :class="{
                    'border-red-600 text-red-600 hover:bg-red-50': form.action !== 'reject'
                  }"
                >
                  <Icon name="x" class="w-4 h-4 mr-2" />
                  Tolak Semua
                </Button>
              </div>
            </div>

            <!-- Notes -->
            <div class="space-y-2">
              <Label for="notes">Catatan Verifikasi</Label>
              <Textarea
                id="notes"
                v-model="form.notes"
                placeholder="Tambahkan catatan untuk verifikasi ini (opsional)"
                rows="3"
                :required="form.action === 'reject'"
              />
              <p v-if="form.action === 'reject'" class="text-xs text-red-600">
                Catatan wajib diisi untuk penolakan
              </p>
            </div>

            <!-- Submit Button -->
            <Button
              type="submit"
              :disabled="!form.action || isSubmitting || (form.action === 'reject' && !form.notes.trim())"
              class="w-full"
              :class="{
                'bg-green-600 hover:bg-green-700': form.action === 'approve',
                'bg-red-600 hover:bg-red-700': form.action === 'reject'
              }"
            >
              <Icon
                v-if="isSubmitting"
                name="loader"
                class="w-4 h-4 mr-2 animate-spin"
              />
              {{ getSubmitButtonText() }}
            </Button>
          </form>
        </div>

        <!-- Document Verification Tab -->
        <div v-if="activeTab === 'documents'">
          <div class="space-y-4">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 class="font-medium text-blue-800 mb-2">Verifikasi Dokumen Massal</h3>
              <p class="text-sm text-blue-700">
                Verifikasi semua dokumen dari pendaftaran yang dipilih sekaligus.
              </p>
            </div>

            <form @submit.prevent="handleDocumentSubmit" class="space-y-4">
              <!-- Document Action Selection -->
              <div class="space-y-2">
                <Label>Aksi Verifikasi Dokumen</Label>
                <div class="grid grid-cols-2 gap-3">
                  <Button
                    type="button"
                    @click="documentForm.action = 'approve'"
                    :variant="documentForm.action === 'approve' ? 'default' : 'outline'"
                    class="justify-center"
                    :class="{
                      'bg-green-600 hover:bg-green-700 text-white': documentForm.action === 'approve',
                      'border-green-600 text-green-600 hover:bg-green-50': documentForm.action !== 'approve'
                    }"
                  >
                    <Icon name="check" class="w-4 h-4 mr-2" />
                    Setujui Semua Dokumen
                  </Button>
                  <Button
                    type="button"
                    @click="documentForm.action = 'reject'"
                    :variant="documentForm.action === 'reject' ? 'destructive' : 'outline'"
                    class="justify-center"
                    :class="{
                      'border-red-600 text-red-600 hover:bg-red-50': documentForm.action !== 'reject'
                    }"
                  >
                    <Icon name="x" class="w-4 h-4 mr-2" />
                    Tolak Semua Dokumen
                  </Button>
                </div>
              </div>

              <!-- Document Notes -->
              <div class="space-y-2">
                <Label for="document-notes">Catatan Verifikasi Dokumen</Label>
                <Textarea
                  id="document-notes"
                  v-model="documentForm.notes"
                  placeholder="Tambahkan catatan untuk verifikasi dokumen..."
                  rows="3"
                  :required="documentForm.action === 'reject'"
                />
              </div>

              <!-- Submit Document Verification -->
              <Button
                type="submit"
                :disabled="!documentForm.action || isSubmitting"
                class="w-full"
                :class="{
                  'bg-green-600 hover:bg-green-700': documentForm.action === 'approve',
                  'bg-red-600 hover:bg-red-700': documentForm.action === 'reject'
                }"
              >
                <Icon
                  v-if="isSubmitting"
                  name="loader"
                  class="w-4 h-4 mr-2 animate-spin"
                />
                {{ getDocumentSubmitButtonText() }}
              </Button>
            </form>
          </div>
        </div>

      </div>

      <!-- Footer Actions -->
      <DialogFooter>
        <Button
          type="button"
          variant="outline"
          @click="$emit('update:show', false)"
        >
          Batal
        </Button>
        <Button
          v-if="activeTab === 'registrations'"
          @click="handleSubmit"
          :disabled="!form.action || isSubmitting || (form.action === 'reject' && !form.notes.trim())"
          :class="{
            'bg-green-600 hover:bg-green-700': form.action === 'approve',
            'bg-red-600 hover:bg-red-700': form.action === 'reject'
          }"
        >
          <Icon
            v-if="isSubmitting"
            name="loader"
            class="w-4 h-4 mr-2 animate-spin"
          />
          {{ getSubmitButtonText() }}
        </Button>
        <Button
          v-if="activeTab === 'documents'"
          @click="handleDocumentSubmit"
          :disabled="!documentForm.action || isSubmitting"
          :class="{
            'bg-green-600 hover:bg-green-700': documentForm.action === 'approve',
            'bg-red-600 hover:bg-red-700': documentForm.action === 'reject'
          }"
        >
          <Icon
            v-if="isSubmitting"
            name="loader"
            class="w-4 h-4 mr-2 animate-spin"
          />
          {{ getDocumentSubmitButtonText() }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  show: boolean
  selectedCount: number
  selectedRegistrations?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  selectedRegistrations: () => []
})

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'verify': [data: { action: string; notes: string }]
  'verify-documents': [data: { action: string; notes: string }]
}>()

// Reactive data
const activeTab = ref('registrations')
const isSubmitting = ref(false)
const form = ref({
  action: '',
  notes: ''
})
const documentForm = ref({
  action: '',
  notes: ''
})

// Computed
const tabs = computed(() => [
  {
    id: 'registrations',
    label: 'Verifikasi Pendaftaran',
    icon: 'check'
  },
  {
    id: 'documents',
    label: 'Verifikasi Dokumen',
    icon: 'file'
  }
])

// Watch for modal close to reset form
watch(() => props.show, (newValue) => {
  if (!newValue) {
    activeTab.value = 'registrations'
    form.value = {
      action: '',
      notes: ''
    }
    documentForm.value = {
      action: '',
      notes: ''
    }
  }
})

// Methods
const handleSubmit = async () => {
  if (!form.value.action) return

  isSubmitting.value = true

  try {
    emit('verify', {
      action: form.value.action,
      notes: form.value.notes
    })
  } finally {
    isSubmitting.value = false
  }
}

const handleDocumentSubmit = async () => {
  if (!documentForm.value.action) return

  isSubmitting.value = true

  try {
    emit('verify-documents', {
      action: documentForm.value.action,
      notes: documentForm.value.notes
    })
  } finally {
    isSubmitting.value = false
  }
}

const getSubmitButtonText = () => {
  if (isSubmitting.value) {
    return 'Memproses...'
  }

  if (form.value.action === 'approve') {
    return `Setujui ${props.selectedCount} Pendaftaran`
  } else if (form.value.action === 'reject') {
    return `Tolak ${props.selectedCount} Pendaftaran`
  }

  return 'Verifikasi'
}

const getDocumentSubmitButtonText = () => {
  if (isSubmitting.value) {
    return 'Memproses...'
  }

  if (documentForm.value.action === 'approve') {
    return 'Setujui Semua Dokumen'
  } else if (documentForm.value.action === 'reject') {
    return 'Tolak Semua Dokumen'
  }

  return 'Verifikasi Dokumen'
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'draft': 'secondary',
    'submitted': 'default',
    'payment_pending': 'outline',
    'paid': 'default',
    'verified': 'default',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'draft': 'Draft',
    'submitted': 'Submitted',
    'payment_pending': 'Menunggu Pembayaran',
    'paid': 'Dibayar',
    'verified': 'Terverifikasi',
    'approved': 'Terverifikasi', // Map to standardized label
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}
</script>
