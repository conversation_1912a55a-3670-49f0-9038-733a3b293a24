<?php

namespace App\Http\Controllers;

use App\Models\CabangLomba;
use App\Models\Golongan;
use App\Models\Wilayah;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class CompetitionController extends Controller
{
    /**
     * Display a listing of available competitions
     */
    public function index(Request $request): Response
    {
        $query = CabangLomba::with(['golongan' => function ($query) {
            $query->where('status', 'aktif')
                  ->orderBy('nama_golongan');
        }])->where('status', 'aktif');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nama_cabang', 'like', "%{$search}%")
                  ->orWhere('deskripsi', 'like', "%{$search}%")
                  ->orWhereHas('golongan', function ($q) use ($search) {
                      $q->where('nama_golongan', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by gender
        if ($request->has('jenis_kelamin') && $request->jenis_kelamin !== 'all') {
            $query->whereHas('golongan', function ($q) use ($request) {
                $q->where('jenis_kelamin', $request->jenis_kelamin);
            });
        }

        $cabangLomba = $query->orderBy('nama_cabang')->get();

        // Get statistics
        $stats = [
            'total_cabang' => CabangLomba::where('status', 'aktif')->count(),
            'total_golongan' => Golongan::where('status', 'aktif')->count(),
            'total_golongan_laki' => Golongan::where('status', 'aktif')->where('jenis_kelamin', 'L')->count(),
            'total_golongan_perempuan' => Golongan::where('status', 'aktif')->where('jenis_kelamin', 'P')->count(),
        ];

        return Inertia::render('Competition/Index', [
            'cabangLomba' => $cabangLomba,
            'stats' => $stats,
            'filters' => $request->only(['search', 'jenis_kelamin'])
        ]);
    }

    /**
     * Display the specified competition details
     */
    public function show(string $id): Response
    {
        $cabangLomba = CabangLomba::with(['golongan' => function ($query) {
            $query->where('status', 'aktif')
                  ->orderBy('nama_golongan');
        }])->findOrFail($id);

        return Inertia::render('Competition/Show', [
            'cabangLomba' => $cabangLomba
        ]);
    }

    /**
     * Display golongan details for registration
     */
    public function golongan(string $id): Response
    {
        $golongan = Golongan::with(['cabangLomba'])
            ->where('status', 'aktif')
            ->findOrFail($id);

        // Check if user is authenticated and has peserta profile
        $canRegister = false;
        $peserta = null;
        
        if (auth()->check() && auth()->user()->role === 'peserta') {
            $peserta = auth()->user()->peserta;
            $canRegister = $peserta && $peserta->status_peserta === 'approved';
        }

        return Inertia::render('Competition/Golongan', [
            'golongan' => $golongan,
            'canRegister' => $canRegister,
            'peserta' => $peserta
        ]);
    }
}
