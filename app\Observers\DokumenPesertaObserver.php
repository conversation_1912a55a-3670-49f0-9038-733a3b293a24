<?php

namespace App\Observers;

use App\Models\DokumenPeserta;
use App\Services\DocumentCompletionService;

class DokumenPesertaObserver
{
    protected DocumentCompletionService $documentCompletionService;

    public function __construct(DocumentCompletionService $documentCompletionService)
    {
        $this->documentCompletionService = $documentCompletionService;
    }

    /**
     * Handle the DokumenPeserta "created" event.
     */
    public function created(DokumenPeserta $dokumenPeserta): void
    {
        $this->documentCompletionService->handleDocumentUpload($dokumenPeserta->pendaftaran);
    }

    /**
     * Handle the DokumenPeserta "updated" event.
     */
    public function updated(DokumenPeserta $dokumenPeserta): void
    {
        // Update document completion status when document status changes
        $this->documentCompletionService->handleDocumentUpload($dokumenPeserta->pendaftaran);
    }

    /**
     * Handle the DokumenPeserta "deleted" event.
     */
    public function deleted(DokumenPeserta $dokumenPeserta): void
    {
        $this->documentCompletionService->handleDocumentDeletion($dokumenPeserta->pendaftaran);
    }
}
