<?php

namespace App\Http\Controllers\Peserta;

use App\Http\Controllers\Controller;
use App\Models\DokumenPeserta;
use App\Models\Pendaftaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class DokumenController extends Controller
{
    /**
     * Display documents for a specific registration
     */
    public function index(string $pendaftaranId): Response
    {
        $peserta = Auth::user()->peserta;

        $pendaftaran = Pendaftaran::with(['golongan.cabangLomba', 'dokumenPeserta'])
            ->where('id_peserta', $peserta->id_peserta)
            ->findOrFail($pendaftaranId);

        $requiredDocuments = [
            'foto' => 'Foto Peserta',
            'ktp' => 'KTP',
            'kartu_keluarga' => 'Kartu Keluarga',
            'surat_rekomendasi' => 'Surat Rekomendasi',
            'ijazah' => 'Ijazah Terakhir'
        ];

        return Inertia::render('Peserta/Dokumen/Index', [
            'pendaftaran' => $pendaftaran,
            'requiredDocuments' => $requiredDocuments
        ]);
    }

    /**
     * Store a newly uploaded document
     */
    public function store(Request $request, string $pendaftaranId)
    {
        $peserta = Auth::user()->peserta;

        $pendaftaran = Pendaftaran::with('golongan.allDocumentTypes')
            ->where('id_peserta', $peserta->id_peserta)
            ->findOrFail($pendaftaranId);

        // Get allowed document types for this golongan
        $allowedDocumentTypes = $pendaftaran->golongan->allDocumentTypes()
            ->where('is_active', true)
            ->get();

        // Build validation rules dynamically
        $allowedCodes = $allowedDocumentTypes->pluck('code')->toArray();

        // Fallback to hardcoded types if no dynamic types exist
        if (empty($allowedCodes)) {
            $allowedCodes = ['foto', 'ktp', 'kartu_keluarga', 'surat_rekomendasi', 'ijazah', 'sertifikat', 'lainnya'];
        }

        $validated = $request->validate([
            'jenis_dokumen' => 'required|in:' . implode(',', $allowedCodes),
            'document_type_id' => 'nullable|exists:document_types,id_document_type',
            'file' => 'required|file|max:5120', // Default 5MB max, will be overridden below
            'keterangan' => 'nullable|string|max:255'
        ]);

        // Get specific document type for validation
        $documentType = null;
        if (!empty($validated['document_type_id'])) {
            $documentType = $allowedDocumentTypes->firstWhere('id_document_type', $validated['document_type_id']);
        } else {
            // Fallback: find by code
            $documentType = $allowedDocumentTypes->firstWhere('code', $validated['jenis_dokumen']);
        }

        // Apply document-specific validation if available
        if ($documentType) {
            $allowedMimes = implode(',', $documentType->allowed_file_types);
            $maxSize = $documentType->max_file_size; // in KB

            $request->validate([
                'file' => "required|file|mimes:{$allowedMimes}|max:{$maxSize}"
            ]);
        } else {
            // Fallback validation
            $request->validate([
                'file' => 'required|file|mimes:jpg,jpeg,png,pdf|max:5120'
            ]);
        }

        // Check if document already exists (check both old and new way)
        $existingQuery = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran);

        if ($documentType) {
            $existingQuery->where('document_type_id', $documentType->id_document_type);
        } else {
            $existingQuery->where('jenis_dokumen', $validated['jenis_dokumen']);
        }

        $existingDocument = $existingQuery->first();

        if ($existingDocument) {
            return back()->withErrors(['file' => 'Dokumen jenis ini sudah ada. Silakan hapus yang lama terlebih dahulu.']);
        }

        // Store file
        $file = $request->file('file');
        $fileName = $this->generateFileName($file, $validated['jenis_dokumen'], $pendaftaran);
        $filePath = $file->storeAs('dokumen-peserta', $fileName, 'public');

        // Create document record
        $documentData = [
            'id_pendaftaran' => $pendaftaran->id_pendaftaran,
            'jenis_dokumen' => $validated['jenis_dokumen'],
            'nama_file' => $fileName,
            'path_file' => $filePath,
            'ukuran_file' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'status_verifikasi' => 'pending',
            'uploaded_by' => Auth::id(),
            'keterangan' => $validated['keterangan']
        ];

        // Add document_type_id if available
        if ($documentType) {
            $documentData['document_type_id'] = $documentType->id_document_type;
        }

        DokumenPeserta::create($documentData);

        return back()->with('success', 'Dokumen berhasil diupload.');
    }

    /**
     * Download a document
     */
    public function download(string $pendaftaranId, string $dokumentId)
    {
        $peserta = Auth::user()->peserta;

        $pendaftaran = Pendaftaran::where('id_peserta', $peserta->id_peserta)
            ->findOrFail($pendaftaranId);

        $dokumen = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->findOrFail($dokumentId);

        if (!Storage::disk('public')->exists($dokumen->path_file)) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->download(
            Storage::disk('public')->path($dokumen->path_file),
            $dokumen->nama_file
        );
    }

    /**
     * Delete a document
     */
    public function destroy(string $pendaftaranId, string $dokumentId)
    {
        $peserta = Auth::user()->peserta;

        $pendaftaran = Pendaftaran::where('id_peserta', $peserta->id_peserta)
            ->findOrFail($pendaftaranId);

        $dokumen = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->findOrFail($dokumentId);

        // Check if document can be deleted (not verified yet)
        if ($dokumen->status_verifikasi === 'approved') {
            return back()->withErrors(['error' => 'Dokumen yang sudah diverifikasi tidak dapat dihapus.']);
        }

        // Delete file from storage
        if (Storage::disk('public')->exists($dokumen->path_file)) {
            Storage::disk('public')->delete($dokumen->path_file);
        }

        // Delete record
        $dokumen->delete();

        return back()->with('success', 'Dokumen berhasil dihapus.');
    }

    /**
     * Replace an existing document
     */
    public function replace(Request $request, string $pendaftaranId, string $dokumentId)
    {
        $peserta = Auth::user()->peserta;

        $pendaftaran = Pendaftaran::where('id_peserta', $peserta->id_peserta)
            ->findOrFail($pendaftaranId);

        $dokumen = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->findOrFail($dokumentId);

        // Check if document can be replaced
        if ($dokumen->status_verifikasi === 'approved') {
            return back()->withErrors(['error' => 'Dokumen yang sudah diverifikasi tidak dapat diganti.']);
        }

        $validated = $request->validate([
            'file' => 'required|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
            'keterangan' => 'nullable|string|max:255'
        ]);

        // Delete old file
        if (Storage::disk('public')->exists($dokumen->path_file)) {
            Storage::disk('public')->delete($dokumen->path_file);
        }

        // Store new file
        $file = $request->file('file');
        $fileName = $this->generateFileName($file, $dokumen->jenis_dokumen, $pendaftaran);
        $filePath = $file->storeAs('dokumen-peserta', $fileName, 'public');

        // Update document record
        $dokumen->update([
            'nama_file' => $fileName,
            'path_file' => $filePath,
            'ukuran_file' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'status_verifikasi' => 'pending',
            'keterangan' => $validated['keterangan'],
            'catatan_verifikasi' => null,
            'verified_at' => null,
            'verified_by' => null
        ]);

        return back()->with('success', 'Dokumen berhasil diganti.');
    }

    /**
     * Generate unique filename for uploaded document
     */
    private function generateFileName($file, string $jenisDoc, Pendaftaran $pendaftaran): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = date('YmdHis');
        $random = Str::random(6);

        return "{$pendaftaran->nomor_pendaftaran}_{$jenisDoc}_{$timestamp}_{$random}.{$extension}";
    }
}
