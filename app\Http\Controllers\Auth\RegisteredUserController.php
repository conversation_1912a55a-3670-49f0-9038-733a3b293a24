<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Peserta;
use App\Models\Wilayah;
use App\Rules\UniqueNikAcrossTables;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(): Response
    {
        $wilayah = Wilayah::where('status', 'aktif')
            ->where('level_wilayah', '!=', 'provinsi')
            ->orderBy('nama_wilayah')
            ->get();

        return Inertia::render('auth/Register', [
            'wilayah' => $wilayah
        ]);
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'username' => 'required|string|max:50|unique:users,username',
            'nama_lengkap' => 'required|string|max:100',
            'email' => 'required|string|lowercase|email|max:255|unique:users,email',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'nik' => ['required', 'string', 'size:16', new UniqueNikAcrossTables()],
            'tempat_lahir' => 'required|string|max:50',
            'tanggal_lahir' => 'required|date|before:today',
            'jenis_kelamin' => 'required|in:L,P',
            'alamat' => 'required|string',
            'id_wilayah' => 'required|exists:wilayah,id_wilayah',
            'no_telepon' => 'nullable|string|max:20',
        ]);

        $user = null;
        DB::transaction(function () use ($validated, &$user) {
            // Create user account
            $user = User::create([
                'username' => $validated['username'],
                'nama_lengkap' => $validated['nama_lengkap'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'role' => 'peserta',
                'status' => 'aktif',
                'id_wilayah' => $validated['id_wilayah']
            ]);

            // Create peserta profile
            Peserta::create([
                'id_user' => $user->id_user,
                'nik' => $validated['nik'],
                'nama_lengkap' => $validated['nama_lengkap'],
                'tempat_lahir' => $validated['tempat_lahir'],
                'tanggal_lahir' => $validated['tanggal_lahir'],
                'jenis_kelamin' => $validated['jenis_kelamin'],
                'alamat' => $validated['alamat'],
                'id_wilayah' => $validated['id_wilayah'],
                'no_telepon' => $validated['no_telepon'],
                'email' => $validated['email'],
                'registration_type' => 'mandiri',
                'status_peserta' => 'draft'
            ]);
        });

        event(new Registered($user));
        Auth::login($user);

        return to_route('redirect');
    }
}
