<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Tambah Cabang Lomba" />
    <Heading title="Tambah Cabang Lomba" />

    <div class="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Informasi Cabang Lomba Baru</CardTitle>
          <CardDescription>
            Lengkapi form di bawah untuk menambahkan cabang lomba baru
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Basic Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Dasar</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="kode_cabang">Kode Cabang *</Label>
                  <Input
                    id="kode_cabang"
                    v-model="form.kode_cabang"
                    type="text"
                    required
                    placeholder="Contoh: TIL, TAH, FAH"
                    :class="{ 'border-red-500': form.errors.kode_cabang }"
                  />
                  <p v-if="form.errors.kode_cabang" class="text-sm text-red-600 mt-1">
                    {{ form.errors.kode_cabang }}
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="non_aktif">Non Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="nama_cabang">Nama Cabang Lomba *</Label>
                <Input
                  id="nama_cabang"
                  v-model="form.nama_cabang"
                  type="text"
                  required
                  placeholder="Contoh: Tilawatil Quran, Tahfidzul Quran"
                  :class="{ 'border-red-500': form.errors.nama_cabang }"
                />
                <p v-if="form.errors.nama_cabang" class="text-sm text-red-600 mt-1">
                  {{ form.errors.nama_cabang }}
                </p>
              </div>

              <div>
                <Label for="deskripsi">Deskripsi</Label>
                <textarea
                  id="deskripsi"
                  v-model="form.deskripsi"
                  rows="4"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Deskripsi cabang lomba..."
                  :class="{ 'border-red-500': form.errors.deskripsi }"
                ></textarea>
                <p v-if="form.errors.deskripsi" class="text-sm text-red-600 mt-1">
                  {{ form.errors.deskripsi }}
                </p>
              </div>
            </div>

            <!-- Information -->
            <div class="bg-blue-50 p-4 rounded-lg">
              <h4 class="font-medium text-blue-900 mb-2">Informasi Cabang Lomba</h4>
              <div class="text-sm text-blue-800 space-y-1">
                <p><strong>Kode Cabang:</strong> Singkatan unik untuk identifikasi cabang lomba</p>
                <p><strong>Nama Cabang:</strong> Nama lengkap cabang lomba yang akan ditampilkan</p>
                <p><strong>Deskripsi:</strong> Penjelasan detail tentang cabang lomba ini</p>
                <p><strong>Status:</strong> Menentukan apakah cabang lomba dapat digunakan untuk pendaftaran</p>
              </div>
            </div>

            <!-- Examples -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">Contoh Cabang Lomba MTQ</h4>
              <div class="text-sm text-gray-600 space-y-2">
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <p><strong>TIL</strong> - Tilawatil Quran</p>
                    <p><strong>TAH</strong> - Tahfidzul Quran</p>
                    <p><strong>FAH</strong> - Fahmil Quran</p>
                    <p><strong>SYA</strong> - Syarhil Quran</p>
                  </div>
                  <div>
                    <p><strong>KAL</strong> - Kaligrafi</p>
                    <p><strong>NAS</strong> - Nasyid</p>
                    <p><strong>CER</strong> - Ceramah</p>
                    <p><strong>QIS</strong> - Qiraatul Kutub</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.cabang-lomba.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan Cabang Lomba
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Cabang Lomba', href: '/admin/cabang-lomba' },
  { title: 'Tambah Cabang Lomba', href: '/admin/cabang-lomba/create' }
]

const form = useForm({
  kode_cabang: '',
  nama_cabang: '',
  deskripsi: '',
  status: 'aktif'
})

const submit = () => {
  form.post(route('admin.cabang-lomba.store'), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}
</script>
