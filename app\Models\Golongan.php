<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Golongan extends Model
{
    protected $table = 'golongan';
    protected $primaryKey = 'id_golongan';

    protected $fillable = [
        'kode_golongan',
        'nama_golongan',
        'id_cabang',
        'jenis_kelamin',
        'batas_umur_min',
        'batas_umur_max',
        'kuota_max',
        'biaya_pendaftaran',
        'nomor_urut_awal',
        'nomor_urut_akhir',
        'status'
    ];

    protected $casts = [
        'jenis_kelamin' => 'string',
        'status' => 'string',
        'batas_umur_min' => 'integer',
        'batas_umur_max' => 'integer',
        'kuota_max' => 'integer',
        'biaya_pendaftaran' => 'decimal:2',
        'nomor_urut_awal' => 'integer',
        'nomor_urut_akhir' => 'integer'
    ];

    // Relationships
    public function cabangLomba(): BelongsTo
    {
        return $this->belongsTo(CabangLomba::class, 'id_cabang', 'id_cabang');
    }

    public function pendaftaran(): HasMany
    {
        return $this->hasMany(Pendaftaran::class, 'id_golongan', 'id_golongan');
    }

    public function documentRequirements(): HasMany
    {
        return $this->hasMany(GolonganDocumentRequirement::class, 'id_golongan', 'id_golongan');
    }

    public function requiredDocumentTypes(): BelongsToMany
    {
        return $this->belongsToMany(DocumentType::class, 'golongan_document_requirements', 'id_golongan', 'document_type_id')
                    ->withPivot('is_required')
                    ->withTimestamps()
                    ->wherePivot('is_required', true);
    }

    public function allDocumentTypes(): BelongsToMany
    {
        return $this->belongsToMany(DocumentType::class, 'golongan_document_requirements', 'id_golongan', 'document_type_id')
                    ->withPivot('is_required')
                    ->withTimestamps();
    }

    // Scopes
    public function scopeAktif($query)
    {
        return $query->where('status', 'aktif');
    }

    public function scopeByJenisKelamin($query, $jenisKelamin)
    {
        return $query->where('jenis_kelamin', $jenisKelamin);
    }
}
