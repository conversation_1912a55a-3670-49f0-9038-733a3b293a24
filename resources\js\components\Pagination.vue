<script setup lang="ts">
import { Button } from '@/components/ui/button';
import TextLink from '@/components/TextLink.vue';
defineProps<{

    links: Array<{
        url: string | null
        label: string
        active: boolean
    }>

}>()
</script>
<template>
    <div v-if="links" class="flex justify-center">
        <nav class="flex space-x-2">
            <div v-for="link in links" :key="link.label">
                <Button v-if="link.url" as-child :variant="link.active ? 'default' : 'ghost'" size="sm">
                    <TextLink :href="link.url">
                        {{ link.label }}
                    </TextLink>
                </Button>
                <span v-else class="px-3 py-2 text-sm text-gray-500" v-html="link.label" />
            </div>
        </nav>
    </div>
</template>
