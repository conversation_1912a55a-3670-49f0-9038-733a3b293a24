<template>
  <Dialog :open="show" @update:open="$emit('update:show', $event)">
    <DialogContent class="sm:max-w-lg">
      <DialogHeader>
        <DialogTitle>Verifikasi Pendaftaran</DialogTitle>
        <DialogDescription>
          {{ registration?.nomor_pendaftaran }} - {{ registration?.peserta?.nama_lengkap }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4">
        <!-- Registration Summary -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="font-medium mb-3">Ringkasan Pendaftaran</h4>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-600">Peserta:</span>
              <p class="font-medium">{{ registration?.peserta?.nama_lengkap }}</p>
            </div>
            <div>
              <span class="text-gray-600">NIK:</span>
              <p class="font-medium">{{ registration?.peserta?.nik }}</p>
            </div>
            <div>
              <span class="text-gray-600">Lomba:</span>
              <p class="font-medium">{{ registration?.golongan?.nama_golongan }}</p>
            </div>
            <div>
              <span class="text-gray-600">Wilayah:</span>
              <p class="font-medium">{{ registration?.peserta?.wilayah?.nama_wilayah }}</p>
            </div>
          </div>
        </div>

        <!-- Progress Overview -->
        <div class="grid grid-cols-2 gap-4">
          <div class="text-center p-3 bg-blue-50 rounded">
            <p class="text-2xl font-bold text-blue-600">{{ registration?.verification_progress?.overall_percentage || 0 }}%</p>
            <p class="text-sm text-blue-600">Progress Keseluruhan</p>
          </div>
          <div class="text-center p-3 bg-green-50 rounded">
            <p class="text-2xl font-bold text-green-600">
              {{ registration?.document_status?.approved || 0 }}/{{ registration?.document_status?.total || 0 }}
            </p>
            <p class="text-sm text-green-600">Dokumen Disetujui</p>
          </div>
        </div>

        <!-- Verification Form -->
        <form @submit.prevent="handleSubmit" class="space-y-4">
          <!-- Action Selection -->
          <div class="space-y-2">
            <Label for="action">Aksi Verifikasi</Label>
            <div class="grid grid-cols-2 gap-3">
              <Button
                type="button"
                @click="form.action = 'approve'"
                :variant="form.action === 'approve' ? 'default' : 'outline'"
                class="justify-center"
                :class="{
                  'bg-green-600 hover:bg-green-700 text-white': form.action === 'approve',
                  'border-green-600 text-green-600 hover:bg-green-50': form.action !== 'approve'
                }"
              >
                <Icon name="check" class="w-4 h-4 mr-2" />
                Setujui
              </Button>
              <Button
                type="button"
                @click="form.action = 'reject'"
                :variant="form.action === 'reject' ? 'destructive' : 'outline'"
                class="justify-center"
                :class="{
                  'border-red-600 text-red-600 hover:bg-red-50': form.action !== 'reject'
                }"
              >
                <Icon name="x" class="w-4 h-4 mr-2" />
                Tolak
              </Button>
            </div>
          </div>

          <!-- Notes -->
          <div class="space-y-2">
            <Label for="notes">
              Catatan Verifikasi
              <span v-if="form.action === 'reject'" class="text-red-500">*</span>
            </Label>
            <Textarea
              id="notes"
              v-model="form.notes"
              :placeholder="form.action === 'approve' ? 'Tambahkan catatan persetujuan (opsional)' : 'Jelaskan alasan penolakan (wajib)'"
              rows="3"
              :required="form.action === 'reject'"
            />
          </div>

          <!-- Pre-filled Rejection Reasons -->
          <div v-if="form.action === 'reject'" class="space-y-2">
            <Label>Alasan Umum (Pilih untuk mengisi otomatis)</Label>
            <div class="grid grid-cols-1 gap-2">
              <Button
                v-for="reason in rejectionReasons"
                :key="reason.value"
                type="button"
                @click="form.notes = reason.text"
                variant="outline"
                size="sm"
                class="justify-start text-left"
              >
                {{ reason.label }}
              </Button>
            </div>
          </div>

          <!-- Warning Message -->
          <div v-if="form.action" class="p-3 rounded-lg border">
            <div v-if="form.action === 'approve'" class="flex items-start space-x-2 text-green-700 bg-green-50 p-3 rounded">
              <Icon name="checkCircle" class="w-5 h-5 mt-0.5 flex-shrink-0" />
              <div>
                <p class="font-medium">Menyetujui Pendaftaran</p>
                <p class="text-sm text-green-600 mt-1">
                  Pendaftaran akan berstatus "Terverifikasi" dan dapat melanjutkan ke tahap berikutnya.
                </p>
              </div>
            </div>
            <div v-else-if="form.action === 'reject'" class="flex items-start space-x-2 text-red-700 bg-red-50 p-3 rounded">
              <Icon name="xCircle" class="w-5 h-5 mt-0.5 flex-shrink-0" />
              <div>
                <p class="font-medium">Menolak Pendaftaran</p>
                <p class="text-sm text-red-600 mt-1">
                  Pendaftaran akan berstatus "Ditolak" dan peserta perlu memperbaiki data mereka.
                </p>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              @click="$emit('update:show', false)"
            >
              Batal
            </Button>
            <Button 
              type="submit" 
              :disabled="!form.action || isSubmitting || (form.action === 'reject' && !form.notes.trim())"
              :class="{
                'bg-green-600 hover:bg-green-700': form.action === 'approve',
                'bg-red-600 hover:bg-red-700': form.action === 'reject'
              }"
            >
              <Icon 
                v-if="isSubmitting" 
                name="loader" 
                class="w-4 h-4 mr-2 animate-spin" 
              />
              <Icon 
                v-else-if="form.action === 'approve'" 
                name="check" 
                class="w-4 h-4 mr-2" 
              />
              <Icon 
                v-else-if="form.action === 'reject'" 
                name="x" 
                class="w-4 h-4 mr-2" 
              />
              {{ getSubmitButtonText() }}
            </Button>
          </DialogFooter>
        </form>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  show: boolean
  registration: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'verify': [data: { registrationId: number; action: string; notes: string }]
}>()

// Reactive data
const isSubmitting = ref(false)
const form = ref({
  action: '',
  notes: ''
})

const rejectionReasons = [
  {
    value: 'incomplete_documents',
    label: 'Dokumen tidak lengkap',
    text: 'Dokumen yang diunggah tidak lengkap. Silakan lengkapi semua dokumen yang diperlukan.'
  },
  {
    value: 'invalid_documents',
    label: 'Dokumen tidak valid',
    text: 'Dokumen yang diunggah tidak valid atau tidak sesuai dengan persyaratan.'
  },
  {
    value: 'unclear_documents',
    label: 'Dokumen tidak jelas',
    text: 'Dokumen yang diunggah tidak jelas atau tidak dapat dibaca dengan baik.'
  },
  {
    value: 'incorrect_category',
    label: 'Kategori lomba tidak sesuai',
    text: 'Kategori lomba yang dipilih tidak sesuai dengan profil peserta.'
  },
  {
    value: 'duplicate_registration',
    label: 'Pendaftaran duplikat',
    text: 'Peserta sudah terdaftar pada kategori lomba yang sama.'
  }
]

// Watch for modal close to reset form
watch(() => props.show, (newValue) => {
  if (!newValue) {
    form.value = {
      action: '',
      notes: ''
    }
  }
})

// Methods
const handleSubmit = async () => {
  if (!form.value.action) return
  if (form.value.action === 'reject' && !form.value.notes.trim()) return

  isSubmitting.value = true
  
  try {
    emit('verify', {
      registrationId: props.registration.id_pendaftaran,
      action: form.value.action,
      notes: form.value.notes
    })
    
    // Close modal after successful submission
    emit('update:show', false)
  } finally {
    isSubmitting.value = false
  }
}

const getSubmitButtonText = () => {
  if (isSubmitting.value) {
    return 'Memproses...'
  }
  
  if (form.value.action === 'approve') {
    return 'Setujui Pendaftaran'
  } else if (form.value.action === 'reject') {
    return 'Tolak Pendaftaran'
  }
  
  return 'Verifikasi'
}
</script>
