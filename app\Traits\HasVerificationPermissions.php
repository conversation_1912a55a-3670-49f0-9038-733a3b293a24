<?php

namespace App\Traits;

use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

trait HasVerificationPermissions
{
    /**
     * Check if current user can perform verification actions
     * Only admin provinsi (admin) and superadmin can verify
     */
    protected function requireVerificationAccess(): void
    {
        $user = Auth::user();
        
        if (!$this->canVerify()) {
            abort(Response::HTTP_FORBIDDEN, 
                'Akses ditolak. Hanya admin provinsi dan superadmin yang dapat melakukan verifikasi.'
            );
        }
    }

    /**
     * Check if current user is superadmin
     * Only superadmin can manage system configuration
     */
    protected function requireSuperAdminAccess(): void
    {
        $user = Auth::user();
        
        if (!$this->isSuperAdmin()) {
            abort(Response::HTTP_FORBIDDEN, 
                'Aks<PERSON> ditolak. Hanya superadmin yang dapat mengelola konfigurasi sistem.'
            );
        }
    }

    /**
     * Check if current user can manage admin-level features
     * Both admin provinsi and superadmin can manage admin features
     */
    protected function requireAdminAccess(): void
    {
        $user = Auth::user();
        
        if (!$this->isAdminProvinsi()) {
            abort(Response::HTTP_FORBIDDEN, 
                'Akses ditolak. Hanya admin provinsi dan superadmin yang dapat mengakses fitur ini.'
            );
        }
    }

    /**
     * Check if current user can register participants
     * Admin daerah, admin provinsi, and superadmin can register
     */
    protected function requireRegistrationAccess(): void
    {
        $user = Auth::user();
        
        if (!$this->canRegister()) {
            abort(Response::HTTP_FORBIDDEN, 
                'Akses ditolak. Anda tidak memiliki izin untuk mendaftarkan peserta.'
            );
        }
    }

    /**
     * Check if current user can view verification data
     * All admin roles can view, but with different scopes
     */
    protected function requireViewAccess(): void
    {
        $user = Auth::user();
        
        if (!$this->canViewVerifications()) {
            abort(Response::HTTP_FORBIDDEN, 
                'Akses ditolak. Anda tidak memiliki izin untuk melihat data verifikasi.'
            );
        }
    }

    /**
     * Check if user can perform verification actions
     */
    public function canVerify(): bool
    {
        $user = Auth::user();
        return in_array($user->role, ['admin', 'superadmin']);
    }

    /**
     * Check if user is superadmin
     */
    public function isSuperAdmin(): bool
    {
        $user = Auth::user();
        return $user->role === 'superadmin';
    }

    /**
     * Check if user is admin provinsi (includes superadmin)
     */
    public function isAdminProvinsi(): bool
    {
        $user = Auth::user();
        return in_array($user->role, ['admin', 'superadmin']);
    }

    /**
     * Check if user is admin daerah
     */
    public function isAdminDaerah(): bool
    {
        $user = Auth::user();
        return $user->role === 'admin_daerah';
    }

    /**
     * Check if user can register participants
     */
    public function canRegister(): bool
    {
        $user = Auth::user();
        return in_array($user->role, ['admin_daerah', 'admin', 'superadmin']);
    }

    /**
     * Check if user can view verification data
     */
    public function canViewVerifications(): bool
    {
        $user = Auth::user();
        return in_array($user->role, ['admin_daerah', 'admin', 'superadmin']);
    }

    /**
     * Check if user can manage verification types
     */
    public function canManageVerificationTypes(): bool
    {
        return $this->isSuperAdmin();
    }

    /**
     * Check if user can manage document types
     */
    public function canManageDocumentTypes(): bool
    {
        return $this->isSuperAdmin();
    }

    /**
     * Check if user can manage document requirements
     */
    public function canManageDocumentRequirements(): bool
    {
        return $this->isAdminProvinsi();
    }

    /**
     * Check if user can perform bulk operations
     */
    public function canBulkVerify(): bool
    {
        return $this->canVerify();
    }

    /**
     * Get user's verification scope
     * Returns the scope of data the user can access
     */
    public function getVerificationScope(): string
    {
        $user = Auth::user();
        
        switch ($user->role) {
            case 'superadmin':
                return 'all'; // Can access all data
            case 'admin':
                return 'provincial'; // Can access provincial data
            case 'admin_daerah':
                return 'regional'; // Can only access their regional data
            default:
                return 'none';
        }
    }

    /**
     * Get allowed verification actions for current user
     */
    public function getAllowedVerificationActions(): array
    {
        $user = Auth::user();
        
        $actions = [];
        
        if ($this->canVerify()) {
            $actions = array_merge($actions, [
                'verify_participant',
                'verify_document', 
                'verify_registration',
                'bulk_verify'
            ]);
        }
        
        if ($this->canRegister()) {
            $actions[] = 'register_participant';
        }
        
        if ($this->canViewVerifications()) {
            $actions[] = 'view_verifications';
        }
        
        if ($this->canManageVerificationTypes()) {
            $actions = array_merge($actions, [
                'create_verification_type',
                'update_verification_type',
                'delete_verification_type'
            ]);
        }
        
        if ($this->canManageDocumentTypes()) {
            $actions = array_merge($actions, [
                'create_document_type',
                'update_document_type',
                'delete_document_type'
            ]);
        }
        
        if ($this->canManageDocumentRequirements()) {
            $actions = array_merge($actions, [
                'manage_document_requirements',
                'bulk_update_requirements'
            ]);
        }
        
        return $actions;
    }

    /**
     * Check if user has specific permission
     */
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->getAllowedVerificationActions());
    }

    /**
     * Get user role display name
     */
    public function getRoleDisplayName(): string
    {
        $user = Auth::user();
        
        $roleNames = [
            'superadmin' => 'Super Administrator',
            'admin' => 'Admin Provinsi',
            'admin_daerah' => 'Admin Daerah',
            'peserta' => 'Peserta',
            'dewan_hakim' => 'Dewan Hakim'
        ];
        
        return $roleNames[$user->role] ?? 'Unknown Role';
    }

    /**
     * Get verification permissions summary for current user
     */
    public function getPermissionsSummary(): array
    {
        return [
            'role' => $this->getRoleDisplayName(),
            'scope' => $this->getVerificationScope(),
            'can_verify' => $this->canVerify(),
            'can_register' => $this->canRegister(),
            'can_view' => $this->canViewVerifications(),
            'can_bulk_verify' => $this->canBulkVerify(),
            'can_manage_types' => $this->canManageVerificationTypes(),
            'allowed_actions' => $this->getAllowedVerificationActions()
        ];
    }
}
