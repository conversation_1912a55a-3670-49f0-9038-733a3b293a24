<template>
  <Dialog :open="show" @update:open="$emit('update:show', $event)">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>{{ title }}</DialogTitle>
        <DialogDescription>
          {{ description }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4">
        <!-- Impact Summary -->
        <div class="p-4 rounded-lg border">
          <h4 class="font-medium mb-3">Ringkasan Dampak</h4>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span>Total item yang akan diproses:</span>
              <span class="font-medium">{{ itemCount }}</span>
            </div>
            <div v-if="estimatedTime" class="flex justify-between">
              <span>Estimasi waktu:</span>
              <span class="font-medium">{{ estimatedTime }}</span>
            </div>
            <div class="flex justify-between">
              <span>Aksi yang akan dilakukan:</span>
              <span class="font-medium" :class="actionClass">{{ actionLabel }}</span>
            </div>
          </div>
        </div>

        <!-- Warning Message -->
        <div v-if="isDestructive" class="p-4 rounded-lg border border-red-200 bg-red-50">
          <div class="flex items-start space-x-2">
            <Icon name="alertTriangle" class="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <p class="font-medium text-red-800">Peringatan</p>
              <p class="text-sm text-red-700 mt-1">
                Aksi ini tidak dapat dibatalkan. Pastikan Anda telah memeriksa semua item yang akan diproses.
              </p>
            </div>
          </div>
        </div>

        <!-- Item Preview -->
        <div v-if="showItemPreview && items.length > 0" class="space-y-2">
          <div class="flex items-center justify-between">
            <h4 class="font-medium">Item yang akan diproses:</h4>
            <Button 
              @click="showItemPreview = !showItemPreview" 
              variant="ghost" 
              size="sm"
            >
              <Icon name="chevronUp" class="w-4 h-4" />
            </Button>
          </div>
          
          <div class="max-h-32 overflow-y-auto border rounded p-3 space-y-1">
            <div 
              v-for="(item, index) in items.slice(0, maxPreviewItems)" 
              :key="index"
              class="text-sm text-gray-600"
            >
              {{ item.name || item.title || `Item ${index + 1}` }}
            </div>
            <div v-if="items.length > maxPreviewItems" class="text-sm text-gray-500 italic">
              ... dan {{ items.length - maxPreviewItems }} item lainnya
            </div>
          </div>
        </div>

        <!-- Additional Notes -->
        <div v-if="requiresNotes" class="space-y-2">
          <Label for="notes">Catatan ({{ isDestructive ? 'Wajib' : 'Opsional' }})</Label>
          <Textarea
            id="notes"
            v-model="notes"
            placeholder="Tambahkan catatan untuk aksi ini"
            rows="3"
            :required="isDestructive"
          />
        </div>

        <!-- Confirmation Checkbox -->
        <div v-if="requiresConfirmation" class="flex items-start space-x-2">
          <Checkbox v-model="confirmed" />
          <Label class="text-sm leading-relaxed">
            Saya memahami dampak dari aksi ini dan yakin ingin melanjutkan proses {{ actionLabel.toLowerCase() }} untuk {{ itemCount }} item.
          </Label>
        </div>
      </div>

      <!-- Action Buttons -->
      <DialogFooter>
        <Button 
          type="button" 
          variant="outline" 
          @click="$emit('update:show', false)"
        >
          Batal
        </Button>
        <Button 
          @click="handleConfirm"
          :disabled="!canProceed"
          :class="buttonClass"
        >
          <Icon :name="actionIcon" class="w-4 h-4 mr-2" />
          {{ actionLabel }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  show: boolean
  title: string
  description: string
  actionType: 'approve' | 'reject' | 'delete' | 'update'
  itemCount: number
  items?: Array<{ name?: string; title?: string; [key: string]: any }>
  isDestructive?: boolean
  requiresNotes?: boolean
  requiresConfirmation?: boolean
  estimatedTime?: string
  maxPreviewItems?: number
}

const props = withDefaults(defineProps<Props>(), {
  isDestructive: false,
  requiresNotes: false,
  requiresConfirmation: false,
  maxPreviewItems: 10,
  items: () => []
})

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'confirm': [data: { notes: string }]
}>()

// Reactive data
const notes = ref('')
const confirmed = ref(false)
const showItemPreview = ref(false)

// Computed
const actionLabel = computed(() => {
  const labels = {
    'approve': 'Setujui',
    'reject': 'Tolak',
    'delete': 'Hapus',
    'update': 'Update'
  }
  return labels[props.actionType] || 'Proses'
})

const actionIcon = computed(() => {
  const icons = {
    'approve': 'check',
    'reject': 'x',
    'delete': 'trash',
    'update': 'edit'
  }
  return icons[props.actionType] || 'check'
})

const actionClass = computed(() => {
  const classes = {
    'approve': 'text-green-600',
    'reject': 'text-red-600',
    'delete': 'text-red-600',
    'update': 'text-blue-600'
  }
  return classes[props.actionType] || 'text-gray-600'
})

const buttonClass = computed(() => {
  const classes = {
    'approve': 'bg-green-600 hover:bg-green-700',
    'reject': 'bg-red-600 hover:bg-red-700',
    'delete': 'bg-red-600 hover:bg-red-700',
    'update': 'bg-blue-600 hover:bg-blue-700'
  }
  return classes[props.actionType] || 'bg-gray-600 hover:bg-gray-700'
})

const canProceed = computed(() => {
  if (props.requiresConfirmation && !confirmed.value) return false
  if (props.requiresNotes && props.isDestructive && !notes.value.trim()) return false
  return true
})

// Watch for modal close to reset form
watch(() => props.show, (newValue) => {
  if (!newValue) {
    notes.value = ''
    confirmed.value = false
    showItemPreview.value = false
  } else {
    // Auto-show preview if there are items
    showItemPreview.value = props.items.length > 0
  }
})

// Methods
const handleConfirm = () => {
  if (!canProceed.value) return
  
  emit('confirm', {
    notes: notes.value
  })
}
</script>
