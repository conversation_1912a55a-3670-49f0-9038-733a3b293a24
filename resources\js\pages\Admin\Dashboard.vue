<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Dashboard Admin" />
    <Heading title="Dashboard Admin" />

    <div class="space-y-6">
      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <Icon name="users" class="h-8 w-8 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Peserta</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.total_peserta }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <Icon name="file-text" class="h-8 w-8 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Pendaftaran</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.total_pendaftaran }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <Icon name="clock" class="h-8 w-8 text-yellow-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Menunggu Verifikasi</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.pendaftaran_pending }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <Icon name="check-circle" class="h-8 w-8 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Disetujui</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.pendaftaran_approved }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Registrations -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Pendaftaran Terbaru</h3>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Peserta
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cabang Lomba
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Golongan
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tanggal
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="pendaftaran in recent_pendaftaran" :key="pendaftaran.id_pendaftaran">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">
                    {{ pendaftaran.peserta.nama_lengkap }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ pendaftaran.nomor_pendaftaran }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ pendaftaran.golongan.cabang_lomba.nama_cabang }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ pendaftaran.golongan.nama_golongan }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(pendaftaran.status_pendaftaran)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                    {{ getStatusText(pendaftaran.status_pendaftaran) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(pendaftaran.created_at) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head } from '@inertiajs/vue3';
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types';

const breadcrumbItems: BreadcrumbItem[] = [
    {
        title: 'Dashboard Admin',
        href: '/admin/dashboard',
    },
];

interface Stats {
  total_peserta: number
  total_pendaftaran: number
  total_users: number
  total_wilayah: number
  pendaftaran_pending: number
  pendaftaran_approved: number
}

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  status_pendaftaran: string
  created_at: string
  peserta: {
    nama_lengkap: string
  }
  golongan: {
    nama_golongan: string
    cabang_lomba: {
      nama_cabang: string
    }
  }
}

defineProps<{
  stats: Stats
  recent_pendaftaran: Pendaftaran[]
  calon_peserta: any
}>()

function getStatusClass(status: string): string {
  const classes = {
    draft: 'bg-gray-100 text-gray-800',
    submitted: 'bg-blue-100 text-blue-800',
    payment_pending: 'bg-yellow-100 text-yellow-800',
    paid: 'bg-green-100 text-green-800',
    verified: 'bg-indigo-100 text-indigo-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string): string {
  const texts = {
    draft: 'Draft',
    submitted: 'Disubmit',
    payment_pending: 'Menunggu Pembayaran',
    paid: 'Dibayar',
    verified: 'Diverifikasi',
    approved: 'Disetujui',
    rejected: 'Ditolak'
  }
  return texts[status as keyof typeof texts] || status
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>
