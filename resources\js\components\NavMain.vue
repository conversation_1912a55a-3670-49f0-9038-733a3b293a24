<script setup lang="ts">
import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarMenuSub, SidebarMenuSubItem, SidebarSeparator } from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/vue3';

defineProps<{
    items: NavItem[];
}>();

const page = usePage();

// Helper function to check if current route matches
const isActive = (href: string) => {
    return page.url === href || page.url.startsWith(href + '/');
};

// Helper function to check if any sub-item is active
const hasActiveSubItem = (items?: NavItem[]) => {
    if (!items) return false;
    return items.some(item => isActive(item.href));
};
</script>

<template>
    <SidebarGroup class="px-2 py-0">
        <SidebarGroupLabel>Platform</SidebarGroupLabel>
        <SidebarMenu>
            <SidebarMenuItem v-for="item in items" :key="item.title">
                <!-- Menu item without subitems -->
                <SidebarMenuButton
                    v-if="!item.items"
                    as-child
                    :is-active="isActive(item.href)"
                    :tooltip="item.title"
                >
                    <Link :href="item.href">
                        <component :is="item.icon" v-if="item.icon" />
                        <span>{{ item.title }}</span>
                    </Link>
                </SidebarMenuButton>

                <!-- Menu item with subitems (collapsible) -->
                <Collapsible v-else :default-open="hasActiveSubItem(item.items)" class="group/collapsible">
                    <CollapsibleTrigger as-child>
                        <SidebarMenuButton
                            :is-active="isActive(item.href) || hasActiveSubItem(item.items)"
                            :tooltip="item.title"
                        >
                            <component :is="item.icon" v-if="item.icon" />
                            <span>{{ item.title }}</span>
                        </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                        <SidebarMenuSub>
                            <SidebarMenuSubItem v-for="subItem in item.items" :key="subItem.title">
                                <SidebarMenuButton
                                    as-child
                                    :is-active="isActive(subItem.href)"
                                    size="sm"
                                >
                                    <Link :href="subItem.href">
                                        <component :is="subItem.icon" v-if="subItem.icon" />
                                        <span>{{ subItem.title }}</span>
                                    </Link>
                                </SidebarMenuButton>
                            </SidebarMenuSubItem>
                        </SidebarMenuSub>
                    </CollapsibleContent>
                </Collapsible>
            </SidebarMenuItem>
        </SidebarMenu>
    </SidebarGroup>
</template>
