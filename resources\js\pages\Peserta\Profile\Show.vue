<script setup lang="ts">
import { computed, ref } from 'vue'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, useForm } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'

interface Peserta {
  id_peserta: number
  nik: string
  nama_lengkap: string
  tempat_lahir: string
  tanggal_lahir: string
  jenis_kelamin: string
  alamat: string
  no_telepon: string | null
  email: string | null
  nama_ayah: string | null
  nama_ibu: string | null
  pekerjaan: string | null
  instansi_asal: string | null
  registration_type: string
  status_peserta: string
  nama_rekening: string | null
  no_rekening: string | null
  wilayah: {
    id_wilayah: number
    nama_wilayah: string
  }
  registered_by?: {
    nama_lengkap: string
  }
}

interface Wilayah {
  id_wilayah: number
  nama_wilayah: string
}

const props = defineProps<{
  peserta: Peserta
  wilayah: Wilayah[]
}>()

const isEditing = ref(false)
const photoInput = ref<HTMLInputElement>()

const form = useForm({
  nik: props.peserta.nik,
  nama_lengkap: props.peserta.nama_lengkap,
  tempat_lahir: props.peserta.tempat_lahir,
  tanggal_lahir: props.peserta.tanggal_lahir,
  jenis_kelamin: props.peserta.jenis_kelamin,
  alamat: props.peserta.alamat,
  id_wilayah: props.peserta.wilayah.id_wilayah.toString(),
  no_telepon: props.peserta.no_telepon || '',
  email: props.peserta.email || '',
  nama_ayah: props.peserta.nama_ayah || '',
  nama_ibu: props.peserta.nama_ibu || '',
  pekerjaan: props.peserta.pekerjaan || '',
  instansi_asal: props.peserta.instansi_asal || '',
  nama_rekening: props.peserta.nama_rekening || '',
  no_rekening: props.peserta.no_rekening || '',
})

const photoForm = useForm({
  photo: null as File | null
})

const profileStatus = computed(() => {
  switch (props.peserta.status_peserta) {
    case 'draft': return { text: 'Draft', color: 'bg-gray-100 text-gray-800', icon: 'edit' }
    case 'submitted': return { text: 'Disubmit', color: 'bg-blue-100 text-blue-800', icon: 'clock' }
    case 'verified': return { text: 'Terverifikasi', color: 'bg-indigo-100 text-indigo-800', icon: 'check' }
    case 'approved': return { text: 'Disetujui', color: 'bg-green-100 text-green-800', icon: 'check-circle' }
    case 'rejected': return { text: 'Ditolak', color: 'bg-red-100 text-red-800', icon: 'x-circle' }
    default: return { text: props.peserta.status_peserta, color: 'bg-gray-100 text-gray-800', icon: 'help-circle' }
  }
})

const canEdit = computed(() => {
  return ['draft', 'submitted'].includes(props.peserta.status_peserta)
})

const canSubmit = computed(() => {
  return props.peserta.status_peserta === 'draft'
})

function toggleEdit() {
  isEditing.value = !isEditing.value
  if (!isEditing.value) {
    // Reset form when canceling
    form.reset()
  }
}

function submitForm() {
  form.put(route('peserta.profile.update'), {
    onSuccess: () => {
      isEditing.value = false
    }
  })
}

function submitProfile() {
  form.post(route('peserta.profile.submit'))
}

function selectPhoto() {
  photoInput.value?.click()
}

function handlePhotoChange(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    photoForm.photo = file
    photoForm.post(route('peserta.profile.upload-photo'), {
      onSuccess: () => {
        photoForm.reset()
      }
    })
  }
}

function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}
</script>

<template>
  <AppLayout>
    <template #header>
      <Heading>Profil Saya</Heading>
    </template>

    <Head title="Profil Saya" />

    <div class="space-y-6">
      <!-- Profile Header -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center space-x-6">
            <div class="relative">
              <div class="h-20 w-20 rounded-full bg-blue-500 flex items-center justify-center">
                <span class="text-2xl font-medium text-white">
                  {{ getInitials(peserta.nama_lengkap) }}
                </span>
              </div>
              <button 
                v-if="canEdit"
                @click="selectPhoto"
                class="absolute -bottom-1 -right-1 h-8 w-8 rounded-full bg-white border-2 border-gray-300 flex items-center justify-center hover:bg-gray-50"
              >
                <Icon name="camera" class="h-4 w-4 text-gray-600" />
              </button>
              <input 
                ref="photoInput"
                type="file"
                accept="image/*"
                class="hidden"
                @change="handlePhotoChange"
              />
            </div>
            <div class="flex-1">
              <h2 class="text-2xl font-bold text-gray-900">{{ peserta.nama_lengkap }}</h2>
              <p class="text-gray-600">{{ peserta.wilayah.nama_wilayah }}</p>
              <div class="mt-2 flex items-center space-x-4">
                <Badge :class="profileStatus.color">
                  <Icon :name="profileStatus.icon" class="w-3 h-3 mr-1" />
                  {{ profileStatus.text }}
                </Badge>
                <span class="text-sm text-gray-500">NIK: {{ peserta.nik }}</span>
              </div>
            </div>
            <div class="flex space-x-2">
              <Button 
                v-if="canEdit && !isEditing" 
                @click="toggleEdit"
                variant="outline"
              >
                <Icon name="edit" class="w-4 h-4 mr-2" />
                Edit Profil
              </Button>
              <Button 
                v-if="canSubmit && !isEditing" 
                @click="submitProfile"
                :disabled="form.processing"
              >
                <Icon name="send" class="w-4 h-4 mr-2" />
                Submit untuk Verifikasi
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Status Alert -->
      <Alert v-if="peserta.status_peserta === 'draft'" class="border-yellow-200 bg-yellow-50">
        <Icon name="alert-triangle" class="h-4 w-4" />
        <AlertDescription>
          Profil Anda masih dalam status draft. Silakan lengkapi data dan submit untuk verifikasi.
        </AlertDescription>
      </Alert>

      <Alert v-else-if="peserta.status_peserta === 'submitted'" class="border-blue-200 bg-blue-50">
        <Icon name="clock" class="h-4 w-4" />
        <AlertDescription>
          Profil Anda telah disubmit dan sedang menunggu verifikasi admin.
        </AlertDescription>
      </Alert>

      <Alert v-else-if="peserta.status_peserta === 'rejected'" class="border-red-200 bg-red-50">
        <Icon name="x-circle" class="h-4 w-4" />
        <AlertDescription>
          Profil Anda ditolak. Silakan perbaiki data dan submit ulang.
        </AlertDescription>
      </Alert>

      <!-- Profile Form -->
      <Card>
        <CardHeader>
          <CardTitle>Data Pribadi</CardTitle>
          <CardDescription>
            Informasi pribadi Anda untuk pendaftaran MTQ
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submitForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- NIK -->
              <div>
                <Label for="nik">NIK *</Label>
                <Input
                  id="nik"
                  v-model="form.nik"
                  :disabled="!isEditing"
                  maxlength="16"
                  :class="{ 'border-red-500': form.errors.nik }"
                />
                <p v-if="form.errors.nik" class="text-sm text-red-600 mt-1">
                  {{ form.errors.nik }}
                </p>
              </div>

              <!-- Nama Lengkap -->
              <div>
                <Label for="nama_lengkap">Nama Lengkap *</Label>
                <Input
                  id="nama_lengkap"
                  v-model="form.nama_lengkap"
                  :disabled="!isEditing"
                  :class="{ 'border-red-500': form.errors.nama_lengkap }"
                />
                <p v-if="form.errors.nama_lengkap" class="text-sm text-red-600 mt-1">
                  {{ form.errors.nama_lengkap }}
                </p>
              </div>

              <!-- Tempat Lahir -->
              <div>
                <Label for="tempat_lahir">Tempat Lahir *</Label>
                <Input
                  id="tempat_lahir"
                  v-model="form.tempat_lahir"
                  :disabled="!isEditing"
                  :class="{ 'border-red-500': form.errors.tempat_lahir }"
                />
                <p v-if="form.errors.tempat_lahir" class="text-sm text-red-600 mt-1">
                  {{ form.errors.tempat_lahir }}
                </p>
              </div>

              <!-- Tanggal Lahir -->
              <div>
                <Label for="tanggal_lahir">Tanggal Lahir *</Label>
                <Input
                  id="tanggal_lahir"
                  v-model="form.tanggal_lahir"
                  type="date"
                  :disabled="!isEditing"
                  :class="{ 'border-red-500': form.errors.tanggal_lahir }"
                />
                <p v-if="form.errors.tanggal_lahir" class="text-sm text-red-600 mt-1">
                  {{ form.errors.tanggal_lahir }}
                </p>
              </div>

              <!-- Jenis Kelamin -->
              <div>
                <Label for="jenis_kelamin">Jenis Kelamin *</Label>
                <Select v-model="form.jenis_kelamin" :disabled="!isEditing">
                  <SelectTrigger :class="{ 'border-red-500': form.errors.jenis_kelamin }">
                    <SelectValue placeholder="Pilih jenis kelamin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="L">Laki-laki</SelectItem>
                    <SelectItem value="P">Perempuan</SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.jenis_kelamin" class="text-sm text-red-600 mt-1">
                  {{ form.errors.jenis_kelamin }}
                </p>
              </div>

              <!-- Wilayah -->
              <div>
                <Label for="id_wilayah">Wilayah *</Label>
                <Select v-model="form.id_wilayah" :disabled="!isEditing">
                  <SelectTrigger :class="{ 'border-red-500': form.errors.id_wilayah }">
                    <SelectValue placeholder="Pilih wilayah" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem 
                      v-for="w in wilayah" 
                      :key="w.id_wilayah" 
                      :value="w.id_wilayah.toString()"
                    >
                      {{ w.nama_wilayah }}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p v-if="form.errors.id_wilayah" class="text-sm text-red-600 mt-1">
                  {{ form.errors.id_wilayah }}
                </p>
              </div>
            </div>

            <!-- Alamat -->
            <div>
              <Label for="alamat">Alamat *</Label>
              <Textarea
                id="alamat"
                v-model="form.alamat"
                :disabled="!isEditing"
                rows="3"
                :class="{ 'border-red-500': form.errors.alamat }"
              />
              <p v-if="form.errors.alamat" class="text-sm text-red-600 mt-1">
                {{ form.errors.alamat }}
              </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- No Telepon -->
              <div>
                <Label for="no_telepon">No. Telepon</Label>
                <Input
                  id="no_telepon"
                  v-model="form.no_telepon"
                  :disabled="!isEditing"
                  :class="{ 'border-red-500': form.errors.no_telepon }"
                />
                <p v-if="form.errors.no_telepon" class="text-sm text-red-600 mt-1">
                  {{ form.errors.no_telepon }}
                </p>
              </div>

              <!-- Email -->
              <div>
                <Label for="email">Email</Label>
                <Input
                  id="email"
                  v-model="form.email"
                  type="email"
                  :disabled="!isEditing"
                  :class="{ 'border-red-500': form.errors.email }"
                />
                <p v-if="form.errors.email" class="text-sm text-red-600 mt-1">
                  {{ form.errors.email }}
                </p>
              </div>
            </div>

            <!-- Action Buttons -->
            <div v-if="isEditing" class="flex justify-end space-x-4">
              <Button type="button" variant="outline" @click="toggleEdit">
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon name="save" class="w-4 h-4 mr-2" />
                Simpan Perubahan
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>
