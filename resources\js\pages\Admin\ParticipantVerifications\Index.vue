<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Verifikasi Peserta" />
    <Heading title="Verifikasi Peserta" />

    <div class="space-y-6">
      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card class="hover:shadow-md transition-shadow cursor-pointer" @click="$inertia.visit(route('admin.participant-verifications.nik-verification'))">
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="id-card" class="w-8 h-8 text-blue-600" />
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-medium text-gray-900">Verifikasi NIK</h3>
                <p class="text-sm text-gray-500">Verifikasi kevalidan NIK peserta</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="hover:shadow-md transition-shadow cursor-pointer" @click="$inertia.visit(route('admin.dokumen-verifikasi.index'))">
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="file-check" class="w-8 h-8 text-green-600" />
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-medium text-gray-900">Verifikasi Dokumen</h3>
                <p class="text-sm text-gray-500">Verifikasi dokumen peserta</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="hover:shadow-md transition-shadow cursor-pointer" @click="$inertia.visit(route('admin.verification-types.index'))">
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="settings" class="w-8 h-8 text-purple-600" />
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-medium text-gray-900">Kelola Jenis Verifikasi</h3>
                <p class="text-sm text-gray-500">Atur jenis verifikasi</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Filters and Search -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama peserta, NIK..."
                @input="search"
              />
            </div>
            <div>
              <Label for="verification_status">Status Verifikasi</Label>
              <Select v-model="filters.verification_status" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="complete">Lengkap</SelectItem>
                  <SelectItem value="incomplete">Belum Lengkap</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="verification_type">Jenis Verifikasi</Label>
              <Select v-model="filters.verification_type" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Jenis" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Jenis</SelectItem>
                  <SelectItem v-for="verType in verificationTypes" :key="verType.id_verification_type" :value="verType.code">
                    {{ verType.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end">
              <Button @click="clearFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Participants List -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Peserta
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Golongan
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status Verifikasi
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="participant in participants.data" :key="participant.id_peserta" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ participant.nama_peserta }}</div>
                      <div class="text-sm text-gray-500">{{ participant.nik }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ participant.pendaftaran?.[0]?.golongan?.nama_golongan }}</div>
                      <div class="text-sm text-gray-500">{{ participant.pendaftaran?.[0]?.golongan?.cabang_lomba?.nama_cabang }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-wrap gap-1">
                      <Badge 
                        v-for="verType in verificationTypes" 
                        :key="verType.id_verification_type"
                        :variant="getVerificationBadgeVariant(participant, verType.code)"
                        class="text-xs"
                      >
                        {{ verType.name }}: {{ getVerificationStatus(participant, verType.code) }}
                      </Badge>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          :style="{ width: `${getVerificationProgress(participant)}%` }"
                        ></div>
                      </div>
                      <span class="text-xs text-gray-600">{{ getVerificationProgress(participant) }}%</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Button
                      @click="$inertia.visit(route('admin.participant-verifications.show', participant.id_peserta))"
                      size="sm"
                      variant="outline"
                    >
                      <Icon name="eye" class="w-4 h-4 mr-2" />
                      Detail
                    </Button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Menampilkan {{ participants.from }} sampai {{ participants.to }} dari {{ participants.total }} hasil
        </div>
        <Pagination
          :current-page="participants.current_page"
          :last-page="participants.last_page"
          :links="participants.links"
        />
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { reactive } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import { debounce } from 'lodash'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import Pagination from '@/components/Pagination.vue'
import Icon from '@/components/Icon.vue'

const props = defineProps({
  participants: Object,
  verificationTypes: Array,
  filters: Object
})

const breadcrumbItems = [
  { label: 'Dashboard', href: route('admin.dashboard') },
  { label: 'Verifikasi Peserta', href: null }
]

const filters = reactive({
  search: props.filters?.search || '',
  verification_status: props.filters?.verification_status || 'all',
  verification_type: props.filters?.verification_type || 'all'
})

const search = debounce(() => {
  router.get(route('admin.participant-verifications.index'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const clearFilters = () => {
  filters.search = ''
  filters.verification_status = 'all'
  filters.verification_type = 'all'
  search()
}

const getVerificationStatus = (participant, verificationTypeCode) => {
  const verification = participant.verifications?.find(v => v.verification_type?.code === verificationTypeCode)
  if (!verification) return 'Belum'
  
  switch (verification.status) {
    case 'verified': return 'Valid'
    case 'rejected': return 'Ditolak'
    case 'pending': return 'Pending'
    default: return 'Belum'
  }
}

const getVerificationBadgeVariant = (participant, verificationTypeCode) => {
  const verification = participant.verifications?.find(v => v.verification_type?.code === verificationTypeCode)
  if (!verification) return 'secondary'
  
  switch (verification.status) {
    case 'verified': return 'default'
    case 'rejected': return 'destructive'
    case 'pending': return 'outline'
    default: return 'secondary'
  }
}

const getVerificationProgress = (participant) => {
  const totalTypes = props.verificationTypes.filter(vt => vt.is_required).length
  if (totalTypes === 0) return 100
  
  const verifiedCount = props.verificationTypes.filter(vt => {
    if (!vt.is_required) return false
    const verification = participant.verifications?.find(v => v.verification_type?.code === vt.code)
    return verification && verification.status === 'verified'
  }).length
  
  return Math.round((verifiedCount / totalTypes) * 100)
}
</script>
