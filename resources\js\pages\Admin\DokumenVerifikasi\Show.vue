<script setup lang="ts">
import { useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head, Link } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/Icon.vue'

interface Wilayah {
  nama_wilayah: string
}

interface Peserta {
  nama_lengkap: string
  nik: string
  tempat_lahir: string
  tanggal_lahir: string
  jenis_kelamin: string
  alamat: string
  no_telepon: string
  email: string
  wilayah: Wilayah
}

interface CabangLomba {
  nama_cabang: string
}

interface Golongan {
  nama_golongan: string
  cabang_lomba: CabangLomba
}

interface Pendaftaran {
  nomor_pendaftaran: string
  nomor_peserta: string
  status_pendaftaran: string
  peserta: Peserta
  golongan: Golongan
}

interface User {
  name: string
}

interface DokumenPeserta {
  id_dokumen: number
  jenis_dokumen: string
  nama_file: string
  path_file: string
  ukuran_file: number
  mime_type: string
  status_verifikasi: string
  catatan_verifikasi: string | null
  verified_at: string | null
  created_at: string
  keterangan: string | null
  pendaftaran: Pendaftaran
  uploaded_by: User
  verified_by: User | null
}

const props = defineProps<{
  dokumen: DokumenPeserta
}>()

const verifyForm = useForm({
  status_verifikasi: '',
  catatan_verifikasi: props.dokumen.catatan_verifikasi || ''
})

function verifyDocument() {
  verifyForm.post(route('admin.dokumen-verifikasi.verify', props.dokumen.id_dokumen), {
    onSuccess: () => {
      verifyForm.reset()
    }
  })
}

function getStatusColor(status: string): string {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string): string {
  const texts = {
    pending: 'Menunggu Verifikasi',
    approved: 'Disetujui',
    rejected: 'Ditolak'
  }
  return texts[status as keyof typeof texts] || status
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function getJenisLabel(jenis: string): string {
  const labels = {
    foto: 'Foto Peserta',
    ktp: 'KTP',
    kartu_keluarga: 'Kartu Keluarga',
    surat_rekomendasi: 'Surat Rekomendasi',
    ijazah: 'Ijazah Terakhir',
    sertifikat: 'Sertifikat',
    lainnya: 'Lainnya'
  }
  return labels[jenis as keyof typeof labels] || jenis
}

function isImageFile(mimeType: string): boolean {
  return mimeType.startsWith('image/')
}

function getFileUrl(): string {
  return `/storage/${props.dokumen.path_file}`
}
</script>

<template>
  <AppLayout>
    <template #header>
      <div class="flex items-center space-x-4">
        <Button
          as="link"
          :href="route('admin.dokumen-verifikasi.index')"
          variant="ghost"
          size="sm"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Kembali
        </Button>
        <Heading>Detail Dokumen</Heading>
      </div>
    </template>

    <Head title="Detail Dokumen" />

    <div class="space-y-6">
      <!-- Document Info -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Document Details -->
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle>{{ getJenisLabel(dokumen.jenis_dokumen) }}</CardTitle>
              <Badge :class="getStatusColor(dokumen.status_verifikasi)">
                {{ getStatusText(dokumen.status_verifikasi) }}
              </Badge>
            </div>
            <CardDescription>{{ dokumen.nama_file }}</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="font-medium text-gray-500">Ukuran File:</span>
                <p>{{ formatFileSize(dokumen.ukuran_file) }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-500">Tipe File:</span>
                <p>{{ dokumen.mime_type }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-500">Diupload:</span>
                <p>{{ formatDate(dokumen.created_at) }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-500">Oleh:</span>
                <p>{{ dokumen.uploaded_by.name }}</p>
              </div>
            </div>

            <div v-if="dokumen.keterangan" class="pt-4 border-t">
              <span class="font-medium text-gray-500">Keterangan:</span>
              <p class="mt-1 text-sm">{{ dokumen.keterangan }}</p>
            </div>

            <div v-if="dokumen.verified_at" class="pt-4 border-t">
              <span class="font-medium text-gray-500">Diverifikasi:</span>
              <p class="mt-1 text-sm">
                {{ formatDate(dokumen.verified_at) }} oleh {{ dokumen.verified_by?.name }}
              </p>
            </div>

            <div v-if="dokumen.catatan_verifikasi" class="pt-4 border-t">
              <span class="font-medium text-gray-500">Catatan Verifikasi:</span>
              <p class="mt-1 text-sm text-red-600">{{ dokumen.catatan_verifikasi }}</p>
            </div>

            <div class="flex space-x-2 pt-4">
              <Button
                as="link"
                :href="route('admin.dokumen-verifikasi.download', dokumen.id_dokumen)"
                variant="outline"
              >
                <Icon name="download" class="w-4 h-4 mr-2" />
                Download
              </Button>

              <Button
                v-if="isImageFile(dokumen.mime_type)"
                as="link"
                :href="getFileUrl()"
                target="_blank"
                variant="outline"
              >
                <Icon name="external-link" class="w-4 h-4 mr-2" />
                Lihat Gambar
              </Button>
            </div>
          </CardContent>
        </Card>

        <!-- Participant Info -->
        <Card>
          <CardHeader>
            <CardTitle>Informasi Peserta</CardTitle>
            <CardDescription>{{ dokumen.pendaftaran.nomor_pendaftaran }}</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-1 gap-4 text-sm">
              <div>
                <span class="font-medium text-gray-500">Nama Lengkap:</span>
                <p>{{ dokumen.pendaftaran.peserta.nama_lengkap }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-500">NIK:</span>
                <p>{{ dokumen.pendaftaran.peserta.nik }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-500">Tempat, Tanggal Lahir:</span>
                <p>{{ dokumen.pendaftaran.peserta.tempat_lahir }}, {{ formatDate(dokumen.pendaftaran.peserta.tanggal_lahir) }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-500">Jenis Kelamin:</span>
                <p>{{ dokumen.pendaftaran.peserta.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-500">Alamat:</span>
                <p>{{ dokumen.pendaftaran.peserta.alamat }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-500">Wilayah:</span>
                <p>{{ dokumen.pendaftaran.peserta.wilayah.nama_wilayah }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-500">No. Telepon:</span>
                <p>{{ dokumen.pendaftaran.peserta.no_telepon || '-' }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-500">Email:</span>
                <p>{{ dokumen.pendaftaran.peserta.email || '-' }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-500">Cabang Lomba:</span>
                <p>{{ dokumen.pendaftaran.golongan.cabang_lomba.nama_cabang }}</p>
              </div>
              <div>
                <span class="font-medium text-gray-500">Golongan:</span>
                <p>{{ dokumen.pendaftaran.golongan.nama_golongan }}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Document Preview -->
      <Card v-if="isImageFile(dokumen.mime_type)">
        <CardHeader>
          <CardTitle>Preview Dokumen</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="flex justify-center">
            <img
              :src="getFileUrl()"
              :alt="dokumen.nama_file"
              class="max-w-full max-h-96 object-contain border rounded-lg shadow-sm"
            />
          </div>
        </CardContent>
      </Card>

      <!-- Verification Form -->
      <Card v-if="dokumen.status_verifikasi === 'pending'">
        <CardHeader>
          <CardTitle>Verifikasi Dokumen</CardTitle>
          <CardDescription>Setujui atau tolak dokumen ini</CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="verifyDocument" class="space-y-4">
            <div class="grid gap-2">
              <Label for="status_verifikasi">Status Verifikasi</Label>
              <select
                id="status_verifikasi"
                v-model="verifyForm.status_verifikasi"
                required
                class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="all">Pilih status</option>
                <option value="approved">Setujui</option>
                <option value="rejected">Tolak</option>
              </select>
            </div>

            <div class="grid gap-2">
              <Label for="catatan_verifikasi">Catatan Verifikasi (Opsional)</Label>
              <Textarea
                id="catatan_verifikasi"
                v-model="verifyForm.catatan_verifikasi"
                placeholder="Tambahkan catatan verifikasi..."
                rows="4"
              />
            </div>

            <div class="flex justify-end space-x-2">
              <Button
                as="link"
                :href="route('admin.dokumen-verifikasi.index')"
                variant="outline"
              >
                Kembali
              </Button>
              <Button type="submit" :disabled="verifyForm.processing">
                <Icon v-if="verifyForm.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Verifikasi Dokumen
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>
