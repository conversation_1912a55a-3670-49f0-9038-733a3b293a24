<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head :title="`Detail Cabang Lomba: ${cabangLomba.nama_cabang}`" />
    <Heading :title="`Detail Cabang Lomba: ${cabangLomba.nama_cabang}`" />

    <div class="max-w-4xl mx-auto space-y-6">
      <!-- Cabang Lomba Information -->
      <Card>
        <CardHeader>
          <div class="flex justify-between items-start">
            <div>
              <CardTitle class="flex items-center gap-3">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <Icon name="trophy" class="w-6 h-6 text-green-600" />
                </div>
                {{ cabangLomba.nama_cabang }}
              </CardTitle>
              <CardDescription>
                {{ cabangLomba.kode_cabang }}
              </CardDescription>
            </div>
            <div class="flex gap-2">
              <Badge :variant="getStatusVariant(cabangLomba.status)">
                {{ getStatusLabel(cabangLomba.status) }}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">Kode Cabang</Label>
                <p class="text-sm">{{ cabangLomba.kode_cabang }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Nama Cabang</Label>
                <p class="text-sm">{{ cabangLomba.nama_cabang }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Status</Label>
                <p class="text-sm">{{ getStatusLabel(cabangLomba.status) }}</p>
              </div>
            </div>
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">Deskripsi</Label>
                <p class="text-sm">{{ cabangLomba.deskripsi || '-' }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Dibuat</Label>
                <p class="text-sm">{{ formatDate(cabangLomba.created_at) }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Diperbarui</Label>
                <p class="text-sm">{{ formatDate(cabangLomba.updated_at) }}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Golongan List -->
      <Card>
        <CardHeader>
          <div class="flex justify-between items-center">
            <div>
              <CardTitle>Golongan Lomba</CardTitle>
              <CardDescription>
                Daftar golongan yang tersedia untuk cabang lomba {{ cabangLomba.nama_cabang }}
              </CardDescription>
            </div>
            <Button @click="$inertia.visit(route('admin.golongan.create', { cabang: cabangLomba.id_cabang }))">
              <Icon name="plus" class="w-4 h-4 mr-2" />
              Tambah Golongan
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div v-if="!cabangLomba.golongan || cabangLomba.golongan.length === 0" class="text-center py-8 text-gray-500">
            <Icon name="trophy" class="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>Belum ada golongan untuk cabang lomba ini</p>
            <Button 
              class="mt-4" 
              @click="$inertia.visit(route('admin.golongan.create', { cabang: cabangLomba.id_cabang }))"
            >
              Tambah Golongan Pertama
            </Button>
          </div>
          <div v-else class="space-y-4">
            <div
              v-for="golongan in cabangLomba.golongan"
              :key="golongan.id_golongan"
              class="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
              @click="$inertia.visit(route('admin.golongan.show', golongan.id_golongan))"
            >
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <h4 class="font-medium">{{ golongan.nama_golongan }}</h4>
                    <Badge :variant="getStatusVariant(golongan.status)">
                      {{ getStatusLabel(golongan.status) }}
                    </Badge>
                  </div>
                  <p class="text-sm text-gray-500 mb-2">{{ golongan.kode_golongan }}</p>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span class="text-gray-500">Jenis Kelamin:</span>
                      <p class="font-medium">{{ golongan.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</p>
                    </div>
                    <div>
                      <span class="text-gray-500">Usia:</span>
                      <p class="font-medium">{{ golongan.batas_umur_min }}-{{ golongan.batas_umur_max }} tahun</p>
                    </div>
                    <div>
                      <span class="text-gray-500">Kuota:</span>
                      <p class="font-medium">{{ golongan.kuota_max }} peserta</p>
                    </div>
                    <div>
                      <span class="text-gray-500">Biaya:</span>
                      <p class="font-medium text-green-600">Rp {{ formatCurrency(golongan.biaya_pendaftaran) }}</p>
                    </div>
                  </div>
                </div>
                <div class="flex gap-2 ml-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    @click.stop="$inertia.visit(route('admin.golongan.edit', golongan.id_golongan))"
                  >
                    <Icon name="edit" class="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="users" class="h-8 w-8 text-blue-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Golongan</p>
                <p class="text-2xl font-semibold text-gray-900">{{ cabangLomba.golongan?.length || 0 }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="check-circle" class="h-8 w-8 text-green-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Golongan Aktif</p>
                <p class="text-2xl font-semibold text-gray-900">
                  {{ cabangLomba.golongan?.filter(g => g.status === 'aktif').length || 0 }}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="dollar-sign" class="h-8 w-8 text-yellow-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Kuota</p>
                <p class="text-2xl font-semibold text-gray-900">
                  {{ getTotalKuota() }}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Actions -->
      <div class="flex justify-between">
        <Button
          variant="outline"
          @click="$inertia.visit(route('admin.cabang-lomba.index'))"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Kembali
        </Button>
        <div class="flex gap-2">
          <Button
            variant="outline"
            @click="$inertia.visit(route('admin.golongan.create', { cabang: cabangLomba.id_cabang }))"
          >
            <Icon name="plus" class="w-4 h-4 mr-2" />
            Tambah Golongan
          </Button>
          <Button @click="$inertia.visit(route('admin.cabang-lomba.edit', cabangLomba.id_cabang))">
            <Icon name="edit" class="w-4 h-4 mr-2" />
            Edit Cabang Lomba
          </Button>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface Golongan {
  id_golongan: number
  kode_golongan: string
  nama_golongan: string
  jenis_kelamin: string
  batas_umur_min: number
  batas_umur_max: number
  kuota_max: number
  biaya_pendaftaran: number
  status: string
}

interface CabangLomba {
  id_cabang: number
  kode_cabang: string
  nama_cabang: string
  deskripsi?: string
  status: string
  created_at: string
  updated_at: string
  golongan?: Golongan[]
}

const props = defineProps<{
  cabangLomba: CabangLomba
}>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Cabang Lomba', href: '/admin/cabang-lomba' },
  { title: 'Detail Cabang Lomba', href: `/admin/cabang-lomba/${props.cabangLomba.id_cabang}` }
]

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    aktif: 'default',
    non_aktif: 'secondary'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    aktif: 'Aktif',
    non_aktif: 'Non Aktif'
  }
  return labels[status] || status
}

const getTotalKuota = () => {
  if (!props.cabangLomba.golongan) return 0
  return props.cabangLomba.golongan.reduce((total, golongan) => total + golongan.kuota_max, 0)
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('id-ID').format(amount)
}
</script>
