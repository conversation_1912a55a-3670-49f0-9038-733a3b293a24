<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dokumen_peserta', function (Blueprint $table) {
            $table->id('id_dokumen');
            $table->unsignedBigInteger('id_pendaftaran');
            $table->enum('jenis_dokumen', [
                'foto', 'ktp', 'kartu_keluarga', 'surat_rekomendasi', 'ijazah', 'sertifikat', 'lainnya'
            ]);
            $table->string('nama_file', 255);
            $table->string('path_file', 500);
            $table->integer('ukuran_file');
            $table->string('mime_type', 100);
            $table->enum('status_verifikasi', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('catatan_verifikasi')->nullable();
            $table->unsignedBigInteger('verified_by')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->unsignedBigInteger('uploaded_by');
            $table->text('keterangan')->nullable();
            $table->timestamps();

            // Foreign keys
            $table->foreign('id_pendaftaran')->references('id_pendaftaran')->on('pendaftaran')->onDelete('cascade');
            $table->foreign('verified_by')->references('id_user')->on('users')->onDelete('set null');
            $table->foreign('uploaded_by')->references('id_user')->on('users')->onDelete('restrict');

            // Indexes
            $table->index(['jenis_dokumen']);
            $table->index(['status_verifikasi']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dokumen_peserta');
    }
};
