<?php

namespace App\Http\Controllers\Peserta;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\Peserta;
use App\Models\Golongan;
use App\Models\Pembayaran;
use App\Models\DokumenPeserta;
use App\Services\RegistrationNumberService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class PendaftaranController extends Controller
{
    /**
     * Display a listing of peserta's registrations
     */
    public function index(): Response
    {
        $peserta = Auth::user()->peserta;

        $pendaftaran = Pendaftaran::with(['golongan.cabangLomba', 'pembayaran', 'dokumenPeserta'])
            ->where('id_peserta', $peserta->id_peserta)
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Peserta/Pendaftaran/Index', [
            'pendaftaran' => $pendaftaran
        ]);
    }

    /**
     * Show the form for creating a new registration
     */
    public function create(Request $request): Response
    {
        $peserta = Auth::user()->peserta;

        // Check if peserta is approved
        if ($peserta->status_peserta !== 'approved') {
            return redirect()->route('peserta.dashboard')
                ->with('error', 'Profil Anda belum disetujui. Silakan lengkapi data dan tunggu persetujuan admin.');
        }

        $golongan = null;
        if ($request->has('golongan')) {
            $golongan = Golongan::with('cabangLomba')
                ->where('status', 'aktif')
                ->findOrFail($request->golongan);

            // Check eligibility
            if (!$this->checkEligibility($peserta, $golongan)) {
                return redirect()->route('competition.golongan', $golongan->id_golongan)
                    ->with('error', 'Anda tidak memenuhi syarat untuk golongan ini.');
            }

            // Check if already registered
            $existingRegistration = Pendaftaran::where('id_peserta', $peserta->id_peserta)
                ->where('id_golongan', $golongan->id_golongan)
                ->where('tahun_pendaftaran', date('Y'))
                ->first();

            if ($existingRegistration) {
                return redirect()->route('peserta.pendaftaran.show', $existingRegistration->id_pendaftaran)
                    ->with('info', 'Anda sudah terdaftar di golongan ini.');
            }
        }

        $availableGolongan = Golongan::with('cabangLomba')
            ->where('status', 'aktif')
            ->get()
            ->filter(function ($g) use ($peserta) {
                return $this->checkEligibility($peserta, $g);
            });

        return Inertia::render('Peserta/Pendaftaran/Create', [
            'golongan' => $golongan,
            'availableGolongan' => $availableGolongan,
            'peserta' => $peserta
        ]);
    }

    /**
     * Store a newly created registration
     */
    public function store(Request $request)
    {
        $peserta = Auth::user()->peserta;

        $validated = $request->validate([
            'id_golongan' => 'required|exists:golongan,id_golongan',
            'keterangan' => 'nullable|string|max:500'
        ]);

        $golongan = Golongan::findOrFail($validated['id_golongan']);

        // Check eligibility again
        if (!$this->checkEligibility($peserta, $golongan)) {
            return back()->withErrors(['id_golongan' => 'Anda tidak memenuhi syarat untuk golongan ini.']);
        }

        // Check if already registered
        $existingRegistration = Pendaftaran::where('id_peserta', $peserta->id_peserta)
            ->where('id_golongan', $golongan->id_golongan)
            ->where('tahun_pendaftaran', date('Y'))
            ->first();

        if ($existingRegistration) {
            return redirect()->route('peserta.pendaftaran.show', $existingRegistration->id_pendaftaran)
                ->with('info', 'Anda sudah terdaftar di golongan ini.');
        }

        DB::transaction(function () use ($validated, $peserta, $golongan) {
            // Generate registration numbers
            $tahun = date('Y');
            $numbers = RegistrationNumberService::generateAllNumbers($golongan, $tahun);

            // Create registration
            $pendaftaran = Pendaftaran::create([
                'id_peserta' => $peserta->id_peserta,
                'id_golongan' => $validated['id_golongan'],
                'nomor_pendaftaran' => $numbers['nomor_pendaftaran'],
                'nomor_peserta' => $numbers['nomor_peserta'],
                'nomor_urut' => $numbers['nomor_urut'],
                'tahun_pendaftaran' => $tahun,
                'status_pendaftaran' => 'draft',
                'keterangan' => $validated['keterangan']
            ]);

            // Create payment record
            Pembayaran::create([
                'id_pendaftaran' => $pendaftaran->id_pendaftaran,
                'nomor_transaksi' => $this->generateNomorTransaksi(),
                'jumlah_bayar' => $golongan->biaya_pendaftaran,
                'metode_pembayaran' => 'transfer_manual',
                'status_pembayaran' => 'pending'
            ]);
        });

        return redirect()->route('peserta.pendaftaran.index')
            ->with('success', 'Pendaftaran berhasil dibuat. Silakan lengkapi dokumen dan lakukan pembayaran.');
    }

    /**
     * Display the specified registration
     */
    public function show(string $id): Response
    {
        $peserta = Auth::user()->peserta;

        $pendaftaran = Pendaftaran::with([
            'golongan.cabangLomba',
            'pembayaran',
            'dokumenPeserta',
            'verifiedBy',
            'approvedBy'
        ])
        ->where('id_peserta', $peserta->id_peserta)
        ->findOrFail($id);

        return Inertia::render('Peserta/Pendaftaran/Show', [
            'pendaftaran' => $pendaftaran
        ]);
    }

    /**
     * Check if peserta is eligible for golongan
     */
    private function checkEligibility(Peserta $peserta, Golongan $golongan): bool
    {
        // Check gender
        if ($peserta->jenis_kelamin !== $golongan->jenis_kelamin) {
            return false;
        }

        // Check age
        $age = $this->calculateAge($peserta->tanggal_lahir);
        if ($age < $golongan->batas_umur_min || $age > $golongan->batas_umur_max) {
            return false;
        }

        return true;
    }

    /**
     * Calculate age from birth date
     */
    private function calculateAge($birthDate): int
    {
        $today = new \DateTime();
        $birth = new \DateTime($birthDate);
        return $today->diff($birth)->y;
    }

    /**
     * Generate unique transaction number
     */
    private function generateNomorTransaksi(): string
    {
        return RegistrationNumberService::generateNomorTransaksi();
    }
}
