<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_types', function (Blueprint $table) {
            $table->id('id_document_type');
            $table->string('name', 100); // Display name (e.g., "Foto Peserta")
            $table->string('code', 50)->unique(); // Code for system use (e.g., "foto")
            $table->text('description')->nullable(); // Description of the document
            $table->json('allowed_file_types')->default('["jpg","jpeg","png","pdf"]'); // Allowed file extensions
            $table->integer('max_file_size')->default(5120); // Max file size in KB (default 5MB)
            $table->boolean('is_required_default')->default(true); // Default requirement status
            $table->boolean('is_active')->default(true); // Whether this document type is active
            $table->integer('sort_order')->default(0); // For ordering in UI
            $table->timestamps();

            // Indexes
            $table->index(['is_active']);
            $table->index(['sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_types');
    }
};
