<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\VerificationType;
use App\Traits\HasVerificationPermissions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class VerificationTypeController extends Controller
{
    use HasVerificationPermissions;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $this->requireSuperAdminAccess();

        $query = VerificationType::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by active status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $verificationTypes = $query->ordered()->paginate(20);

        return Inertia::render('Admin/VerificationTypes/Index', [
            'verificationTypes' => $verificationTypes,
            'filters' => $request->only(['search', 'status'])
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        $this->requireSuperAdminAccess();

        return Inertia::render('Admin/VerificationTypes/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->requireSuperAdminAccess();

        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'code' => 'required|string|max:50|unique:verification_types,code',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
            'is_required' => 'boolean',
            'settings' => 'nullable|array',
            'sort_order' => 'integer|min:0'
        ]);

        VerificationType::create($validated);

        return redirect()->route('admin.verification-types.index')
            ->with('success', 'Jenis verifikasi berhasil dibuat.');
    }

    /**
     * Display the specified resource.
     */
    public function show(VerificationType $verificationType): Response
    {
        $this->requireSuperAdminAccess();

        $verificationType->load(['participantVerifications.peserta']);

        return Inertia::render('Admin/VerificationTypes/Show', [
            'verificationType' => $verificationType
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(VerificationType $verificationType): Response
    {
        $this->requireSuperAdminAccess();

        return Inertia::render('Admin/VerificationTypes/Edit', [
            'verificationType' => $verificationType
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, VerificationType $verificationType)
    {
        $this->requireSuperAdminAccess();

        $validated = $request->validate([
            'name' => 'required|string|max:100',
            'code' => 'required|string|max:50|unique:verification_types,code,' . $verificationType->id_verification_type . ',id_verification_type',
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
            'is_required' => 'boolean',
            'settings' => 'nullable|array',
            'sort_order' => 'integer|min:0'
        ]);

        $verificationType->update($validated);

        return redirect()->route('admin.verification-types.index')
            ->with('success', 'Jenis verifikasi berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(VerificationType $verificationType)
    {
        $this->requireSuperAdminAccess();

        // Check if verification type is being used
        if ($verificationType->participantVerifications()->exists()) {
            return back()->withErrors(['error' => 'Jenis verifikasi tidak dapat dihapus karena masih digunakan oleh verifikasi peserta.']);
        }

        $verificationType->delete();

        return redirect()->route('admin.verification-types.index')
            ->with('success', 'Jenis verifikasi berhasil dihapus.');
    }

    /**
     * Toggle active status
     */
    public function toggleStatus(VerificationType $verificationType)
    {
        $this->requireSuperAdminAccess();

        $verificationType->update([
            'is_active' => !$verificationType->is_active
        ]);

        $status = $verificationType->is_active ? 'diaktifkan' : 'dinonaktifkan';

        return back()->with('success', "Jenis verifikasi berhasil {$status}.");
    }
}
