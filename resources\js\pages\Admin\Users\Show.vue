<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head :title="`Detail User: ${user.nama_lengkap}`" />
    <Heading :title="`Detail User: ${user.nama_lengkap}`" />

    <div class="max-w-4xl mx-auto space-y-6">
      <!-- User Information -->
      <Card>
        <CardHeader>
          <div class="flex justify-between items-start">
            <div>
              <CardTitle class="flex items-center gap-3">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Icon name="user" class="w-6 h-6 text-blue-600" />
                </div>
                {{ user.nama_lengkap }}
              </CardTitle>
              <CardDescription>
                {{ user.username }} • {{ user.email }}
              </CardDescription>
            </div>
            <div class="flex gap-2">
              <Badge :variant="getRoleVariant(user.role)">
                {{ roles[user.role] || user.role }}
              </Badge>
              <Badge :variant="getStatusVariant(user.status)">
                {{ getStatusLabel(user.status) }}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">Username</Label>
                <p class="text-sm">{{ user.username }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Email</Label>
                <p class="text-sm">{{ user.email }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">No. Telepon</Label>
                <p class="text-sm">{{ user.no_telepon || '-' }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Role</Label>
                <p class="text-sm">{{ roles[user.role] || user.role }}</p>
              </div>
            </div>
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">Status</Label>
                <p class="text-sm">{{ getStatusLabel(user.status) }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Wilayah</Label>
                <p class="text-sm">{{ user.wilayah?.nama_wilayah || '-' }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Dibuat</Label>
                <p class="text-sm">{{ formatDate(user.created_at) }}</p>
              </div>
              <div v-if="user.created_by">
                <Label class="text-sm font-medium text-gray-500">Dibuat oleh</Label>
                <p class="text-sm">{{ user.created_by.nama_lengkap }}</p>
              </div>
              <div v-if="user.last_login">
                <Label class="text-sm font-medium text-gray-500">Login terakhir</Label>
                <p class="text-sm">{{ formatDate(user.last_login) }}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Peserta Profile (if role is peserta) -->
      <Card v-if="user.role === 'peserta' && user.peserta">
        <CardHeader>
          <CardTitle>Profil Peserta</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">NIK</Label>
                <p class="text-sm">{{ user.peserta.nik }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Tempat, Tanggal Lahir</Label>
                <p class="text-sm">{{ user.peserta.tempat_lahir }}, {{ formatDate(user.peserta.tanggal_lahir) }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Jenis Kelamin</Label>
                <p class="text-sm">{{ user.peserta.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Status Peserta</Label>
                <Badge :variant="user.peserta.status_peserta === 'approved' ? 'default' : 'secondary'">
                  {{ user.peserta.status_peserta }}
                </Badge>
              </div>
            </div>
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">Alamat</Label>
                <p class="text-sm">{{ user.peserta.alamat }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Nama Ayah</Label>
                <p class="text-sm">{{ user.peserta.nama_ayah || '-' }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Nama Ibu</Label>
                <p class="text-sm">{{ user.peserta.nama_ibu || '-' }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Pekerjaan</Label>
                <p class="text-sm">{{ user.peserta.pekerjaan || '-' }}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Dewan Hakim Profile (if role is dewan_hakim) -->
      <Card v-if="user.role === 'dewan_hakim' && user.dewa_hakim">
        <CardHeader>
          <CardTitle>Profil Dewan Hakim</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">NIK</Label>
                <p class="text-sm">{{ user.dewa_hakim.nik }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Tempat, Tanggal Lahir</Label>
                <p class="text-sm">{{ user.dewa_hakim.tempat_lahir }}, {{ formatDate(user.dewa_hakim.tanggal_lahir) }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Pekerjaan</Label>
                <p class="text-sm">{{ user.dewa_hakim.pekerjaan }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Unit Kerja</Label>
                <p class="text-sm">{{ user.dewa_hakim.unit_kerja }}</p>
              </div>
            </div>
            <div class="space-y-4">
              <div>
                <Label class="text-sm font-medium text-gray-500">Spesialisasi</Label>
                <p class="text-sm">{{ user.dewa_hakim.spesialisasi }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Tipe Hakim</Label>
                <p class="text-sm">{{ user.dewa_hakim.tipe_hakim }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-gray-500">Status</Label>
                <Badge :variant="user.dewa_hakim.status === 'aktif' ? 'default' : 'secondary'">
                  {{ user.dewa_hakim.status }}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between">
        <Button
          variant="outline"
          @click="$inertia.visit(route('admin.users.index'))"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Kembali
        </Button>
        <div class="flex gap-2">
          <Button
            variant="outline"
            @click="toggleStatus"
            :disabled="user.role === 'superadmin'"
          >
            <Icon :name="user.status === 'aktif' ? 'user-x' : 'user-check'" class="w-4 h-4 mr-2" />
            {{ user.status === 'aktif' ? 'Non-aktifkan' : 'Aktifkan' }}
          </Button>
          <Button @click="$inertia.visit(route('admin.users.edit', user.id_user))">
            <Icon name="edit" class="w-4 h-4 mr-2" />
            Edit User
          </Button>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface User {
  id_user: number
  username: string
  email: string
  nama_lengkap: string
  no_telepon?: string
  role: string
  status: string
  created_at: string
  last_login?: string
  wilayah?: {
    nama_wilayah: string
  }
  created_by?: {
    nama_lengkap: string
  }
  peserta?: {
    nik: string
    tempat_lahir: string
    tanggal_lahir: string
    jenis_kelamin: string
    alamat: string
    nama_ayah?: string
    nama_ibu?: string
    pekerjaan?: string
    status_peserta: string
  }
  dewa_hakim?: {
    nik: string
    tempat_lahir: string
    tanggal_lahir: string
    pekerjaan: string
    unit_kerja: string
    spesialisasi: string
    tipe_hakim: string
    status: string
  }
}

const props = defineProps<{
  user: User
}>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen User', href: '/admin/users' },
  { title: 'Detail User', href: `/admin/users/${props.user.id_user}` }
]

const roles: Record<string, string> = {
  superadmin: 'Super Admin',
  admin: 'Admin',
  admin_daerah: 'Admin Daerah',
  peserta: 'Peserta',
  dewan_hakim: 'Dewan Hakim'
}

const toggleStatus = () => {
  router.post(route('admin.users.toggle-status', props.user.id_user), {}, {
    preserveScroll: true
  })
}

const getRoleVariant = (role: string) => {
  const variants: Record<string, string> = {
    superadmin: 'destructive',
    admin: 'default',
    admin_daerah: 'secondary',
    dewan_hakim: 'outline',
    peserta: 'secondary'
  }
  return variants[role] || 'secondary'
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    aktif: 'default',
    non_aktif: 'secondary',
    suspended: 'destructive'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    aktif: 'Aktif',
    non_aktif: 'Non Aktif',
    suspended: 'Suspended'
  }
  return labels[status] || status
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}
</script>
