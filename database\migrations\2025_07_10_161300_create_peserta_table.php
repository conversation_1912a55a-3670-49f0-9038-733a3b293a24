<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('peserta', function (Blueprint $table) {
            $table->id('id_peserta');
            $table->unsignedBigInteger('id_user')->unique();
            $table->string('nik', 16)->unique();
            $table->string('nama_lengkap', 100);
            $table->string('tempat_lahir', 50);
            $table->date('tanggal_lahir');
            $table->enum('jenis_kelamin', ['L', 'P']);
            $table->text('alamat');
            $table->unsignedBigInteger('id_wilayah');
            $table->string('no_telepon', 20)->nullable();
            $table->string('email', 100)->nullable();
            $table->string('nama_ayah', 100)->nullable();
            $table->string('nama_ibu', 100)->nullable();
            $table->string('pekerjaan', 100)->nullable();
            $table->string('instansi_asal', 100)->nullable();

            // Informasi pendaftaran
            $table->unsignedBigInteger('registered_by')->nullable();
            $table->enum('registration_type', ['mandiri', 'admin_daerah'])->default('mandiri');
            $table->enum('status_peserta', ['draft', 'submitted', 'verified', 'approved', 'rejected'])->default('draft');

            // Informasi tambahan
            $table->string('nama_rekening', 100)->nullable();
            $table->string('no_rekening', 40)->nullable();
            $table->timestamps();

            // Foreign keys
            $table->foreign('id_user')->references('id_user')->on('users')->onDelete('cascade');
            $table->foreign('id_wilayah')->references('id_wilayah')->on('wilayah')->onDelete('restrict');
            $table->foreign('registered_by')->references('id_user')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['nik']);
            $table->index(['nama_lengkap']);
            $table->index(['id_wilayah']);
            $table->index(['status_peserta']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('peserta');
    }
};
