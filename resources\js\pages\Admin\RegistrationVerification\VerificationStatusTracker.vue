<template>
  <div class="space-y-6">
    <!-- Status Overview -->
    <Card class="islamic-shadow">
      <CardContent class="p-6">
        <h3 class="text-lg font-semibold mb-4">Status Verifikasi</h3>
        
        <!-- Progress Steps -->
        <div class="relative">
          <!-- Progress Line -->
          <div class="absolute top-5 left-0 w-full h-0.5 bg-gray-200">
            <div 
              class="h-full bg-blue-600 transition-all duration-500"
              :style="{ width: `${overallProgress}%` }"
            ></div>
          </div>
          
          <!-- Steps -->
          <div class="relative flex justify-between">
            <div 
              v-for="(step, index) in verificationSteps" 
              :key="step.id"
              class="flex flex-col items-center"
            >
              <!-- Step Circle -->
              <div 
                class="w-10 h-10 rounded-full border-2 flex items-center justify-center bg-white transition-all duration-300"
                :class="{
                  'border-blue-600 bg-blue-600 text-white': step.status === 'completed',
                  'border-blue-600 bg-blue-50 text-blue-600': step.status === 'current',
                  'border-gray-300 text-gray-400': step.status === 'pending',
                  'border-red-600 bg-red-600 text-white': step.status === 'failed'
                }"
              >
                <Icon 
                  v-if="step.status === 'completed'" 
                  name="check" 
                  class="w-5 h-5" 
                />
                <Icon 
                  v-else-if="step.status === 'failed'" 
                  name="x" 
                  class="w-5 h-5" 
                />
                <Icon 
                  v-else-if="step.status === 'current'" 
                  name="clock" 
                  class="w-5 h-5" 
                />
                <span v-else class="text-sm font-medium">{{ index + 1 }}</span>
              </div>
              
              <!-- Step Label -->
              <div class="mt-2 text-center">
                <p class="text-sm font-medium" :class="{
                  'text-blue-600': step.status === 'completed' || step.status === 'current',
                  'text-gray-500': step.status === 'pending',
                  'text-red-600': step.status === 'failed'
                }">
                  {{ step.title }}
                </p>
                <p class="text-xs text-gray-500 mt-1">{{ step.description }}</p>
                
                <!-- Step Progress -->
                <div v-if="step.progress !== undefined" class="mt-2 w-16 mx-auto">
                  <div class="w-full bg-gray-200 rounded-full h-1">
                    <div 
                      class="h-1 rounded-full transition-all duration-300"
                      :class="{
                        'bg-blue-600': step.status === 'completed' || step.status === 'current',
                        'bg-gray-300': step.status === 'pending',
                        'bg-red-600': step.status === 'failed'
                      }"
                      :style="{ width: `${step.progress}%` }"
                    ></div>
                  </div>
                  <p class="text-xs text-gray-500 mt-1">{{ step.progress }}%</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Detailed Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Document Verification Status -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h4 class="font-medium">Verifikasi Dokumen</h4>
            <Badge :variant="getDocumentStatusVariant()">
              {{ getDocumentStatusLabel() }}
            </Badge>
          </div>
          
          <div class="space-y-3">
            <!-- Document Progress -->
            <div>
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm">Progress Dokumen</span>
                <span class="text-sm text-gray-500">
                  {{ documentStatus.verified }}/{{ documentStatus.total }}
                </span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div 
                  class="bg-green-600 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${documentProgressPercentage}%` }"
                ></div>
              </div>
            </div>
            
            <!-- Document Breakdown -->
            <div class="grid grid-cols-3 gap-2 text-center">
              <div class="p-2 bg-green-50 rounded">
                <p class="text-lg font-semibold text-green-600">{{ documentStatus.approved }}</p>
                <p class="text-xs text-green-600">Disetujui</p>
              </div>
              <div class="p-2 bg-yellow-50 rounded">
                <p class="text-lg font-semibold text-yellow-600">{{ documentStatus.pending }}</p>
                <p class="text-xs text-yellow-600">Pending</p>
              </div>
              <div class="p-2 bg-red-50 rounded">
                <p class="text-lg font-semibold text-red-600">{{ documentStatus.rejected }}</p>
                <p class="text-xs text-red-600">Ditolak</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Participant Verification Status -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h4 class="font-medium">Verifikasi Data Peserta</h4>
            <Badge :variant="getParticipantStatusVariant()">
              {{ getParticipantStatusLabel() }}
            </Badge>
          </div>
          
          <div class="space-y-3">
            <!-- Participant Progress -->
            <div>
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm">Progress Verifikasi</span>
                <span class="text-sm text-gray-500">
                  {{ participantStatus.verified }}/{{ participantStatus.total }}
                </span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div 
                  class="bg-purple-600 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${participantProgressPercentage}%` }"
                ></div>
              </div>
            </div>
            
            <!-- Verification Types -->
            <div class="space-y-2">
              <div 
                v-for="verification in participantVerifications" 
                :key="verification.id"
                class="flex items-center justify-between p-2 bg-gray-50 rounded"
              >
                <span class="text-sm">{{ verification.name }}</span>
                <Badge :variant="getVerificationStatusVariant(verification.status)">
                  {{ getVerificationStatusLabel(verification.status) }}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Timeline -->
    <Card class="islamic-shadow">
      <CardContent class="p-6">
        <h4 class="font-medium mb-4">Timeline Verifikasi</h4>
        
        <div class="space-y-4">
          <div 
            v-for="(event, index) in timeline" 
            :key="index"
            class="flex items-start space-x-3"
          >
            <!-- Timeline Dot -->
            <div 
              class="w-3 h-3 rounded-full mt-2 flex-shrink-0"
              :class="{
                'bg-green-600': event.type === 'success',
                'bg-red-600': event.type === 'error',
                'bg-blue-600': event.type === 'info',
                'bg-yellow-600': event.type === 'warning'
              }"
            ></div>
            
            <!-- Timeline Content -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <p class="text-sm font-medium">{{ event.title }}</p>
                <span class="text-xs text-gray-500">{{ formatDate(event.timestamp) }}</span>
              </div>
              <p class="text-sm text-gray-600 mt-1">{{ event.description }}</p>
              <p v-if="event.user" class="text-xs text-gray-500 mt-1">
                oleh {{ event.user }}
              </p>
            </div>
          </div>
          
          <!-- No Timeline -->
          <div v-if="timeline.length === 0" class="text-center py-8 text-gray-500">
            <Icon name="clock" class="w-8 h-8 mx-auto mb-2 text-gray-300" />
            <p class="text-sm">Belum ada aktivitas verifikasi</p>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Next Actions -->
    <Card v-if="nextActions.length > 0" class="islamic-shadow">
      <CardContent class="p-6">
        <h4 class="font-medium mb-4">Aksi Selanjutnya</h4>
        
        <div class="space-y-3">
          <div 
            v-for="(action, index) in nextActions" 
            :key="index"
            class="flex items-center justify-between p-3 border rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <Icon :name="action.icon" class="w-5 h-5 text-blue-600" />
              <div>
                <p class="text-sm font-medium">{{ action.title }}</p>
                <p class="text-xs text-gray-500">{{ action.description }}</p>
              </div>
            </div>
            <Button 
              @click="$emit('action', action.id)"
              size="sm"
              variant="outline"
            >
              {{ action.buttonText }}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  verificationSteps: Array<{
    id: string
    title: string
    description: string
    status: 'pending' | 'current' | 'completed' | 'failed'
    progress?: number
  }>
  documentStatus: {
    total: number
    approved: number
    pending: number
    rejected: number
    verified: number
  }
  participantStatus: {
    total: number
    verified: number
  }
  participantVerifications: Array<{
    id: string
    name: string
    status: 'pending' | 'verified' | 'rejected'
  }>
  timeline: Array<{
    title: string
    description: string
    timestamp: string
    type: 'success' | 'error' | 'info' | 'warning'
    user?: string
  }>
  nextActions: Array<{
    id: string
    title: string
    description: string
    icon: string
    buttonText: string
  }>
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'action': [actionId: string]
}>()

// Computed
const overallProgress = computed(() => {
  const completedSteps = props.verificationSteps.filter(step => step.status === 'completed').length
  return Math.round((completedSteps / props.verificationSteps.length) * 100)
})

const documentProgressPercentage = computed(() => {
  if (props.documentStatus.total === 0) return 0
  return Math.round((props.documentStatus.verified / props.documentStatus.total) * 100)
})

const participantProgressPercentage = computed(() => {
  if (props.participantStatus.total === 0) return 0
  return Math.round((props.participantStatus.verified / props.participantStatus.total) * 100)
})

// Methods
const getDocumentStatusVariant = () => {
  if (props.documentStatus.rejected > 0) return 'destructive'
  if (props.documentStatus.verified === props.documentStatus.total) return 'default'
  if (props.documentStatus.pending > 0) return 'outline'
  return 'secondary'
}

const getDocumentStatusLabel = () => {
  if (props.documentStatus.rejected > 0) return 'Ada Ditolak'
  if (props.documentStatus.verified === props.documentStatus.total) return 'Lengkap'
  if (props.documentStatus.pending > 0) return 'Pending'
  return 'Belum Lengkap'
}

const getParticipantStatusVariant = () => {
  const hasRejected = props.participantVerifications.some(v => v.status === 'rejected')
  if (hasRejected) return 'destructive'
  if (props.participantStatus.verified === props.participantStatus.total) return 'default'
  return 'outline'
}

const getParticipantStatusLabel = () => {
  const hasRejected = props.participantVerifications.some(v => v.status === 'rejected')
  if (hasRejected) return 'Ada Ditolak'
  if (props.participantStatus.verified === props.participantStatus.total) return 'Lengkap'
  return 'Pending'
}

const getVerificationStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'pending': 'outline',
    'verified': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getVerificationStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'pending': 'Pending',
    'verified': 'Terverifikasi',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
