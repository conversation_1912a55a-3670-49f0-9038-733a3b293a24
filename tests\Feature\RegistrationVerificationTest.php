<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Peserta;
use App\Models\Pendaftaran;
use App\Models\Wilayah;
use App\Models\Golongan;
use App\Models\CabangLomba;
use App\Models\DokumenPeserta;
use App\Models\DocumentType;
use App\Models\VerificationType;
use App\Models\ParticipantVerification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class RegistrationVerificationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $adminProvinsi;
    protected $adminDaerah;
    protected $peserta;
    protected $pendaftaran;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create wilayah
        $wilayah = Wilayah::factory()->create([
            'level_wilayah' => 'kabupaten',
            'status' => 'aktif'
        ]);

        // Create users
        $this->adminProvinsi = User::factory()->create([
            'role' => 'admin',
            'id_wilayah' => $wilayah->id_wilayah,
            'status' => 'aktif'
        ]);

        $this->adminDaerah = User::factory()->create([
            'role' => 'admin_daerah',
            'id_wilayah' => $wilayah->id_wilayah,
            'status' => 'aktif'
        ]);

        $pesertaUser = User::factory()->create([
            'role' => 'peserta',
            'id_wilayah' => $wilayah->id_wilayah,
            'status' => 'aktif'
        ]);

        // Create cabang lomba and golongan
        $cabangLomba = CabangLomba::factory()->create(['status' => 'aktif']);
        $golongan = Golongan::factory()->create([
            'id_cabang' => $cabangLomba->id_cabang,
            'status' => 'aktif'
        ]);

        // Create peserta
        $this->peserta = Peserta::factory()->create([
            'id_user' => $pesertaUser->id_user,
            'id_wilayah' => $wilayah->id_wilayah,
            'status_peserta' => 'submitted'
        ]);

        // Create pendaftaran
        $this->pendaftaran = Pendaftaran::factory()->create([
            'id_peserta' => $this->peserta->id_peserta,
            'id_golongan' => $golongan->id_golongan,
            'status_pendaftaran' => 'submitted'
        ]);
    }

    /** @test */
    public function admin_provinsi_can_access_verification_dashboard()
    {
        $response = $this->actingAs($this->adminProvinsi)
            ->get('/admin/registration-verification');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('Admin/RegistrationVerification/Index')
        );
    }

    /** @test */
    public function admin_daerah_cannot_access_verification_dashboard()
    {
        $response = $this->actingAs($this->adminDaerah)
            ->get('/admin/registration-verification');

        $response->assertStatus(403);
    }

    /** @test */
    public function admin_provinsi_can_view_registration_details()
    {
        $response = $this->actingAs($this->adminProvinsi)
            ->get("/admin/registration-verification/{$this->pendaftaran->id_pendaftaran}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('Admin/RegistrationVerification/Show')
                ->has('registration')
        );
    }

    /** @test */
    public function admin_provinsi_can_verify_registration()
    {
        $response = $this->actingAs($this->adminProvinsi)
            ->post("/admin/registration-verification/{$this->pendaftaran->id_pendaftaran}/verify", [
                'action' => 'approve',
                'notes' => 'Registration approved'
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->pendaftaran->refresh();
        $this->assertEquals('verified', $this->pendaftaran->status_pendaftaran);
        $this->assertEquals($this->adminProvinsi->id_user, $this->pendaftaran->verified_by);
        $this->assertNotNull($this->pendaftaran->verified_at);
    }

    /** @test */
    public function admin_provinsi_can_reject_registration()
    {
        $response = $this->actingAs($this->adminProvinsi)
            ->post("/admin/registration-verification/{$this->pendaftaran->id_pendaftaran}/verify", [
                'action' => 'reject',
                'notes' => 'Registration rejected due to incomplete documents'
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->pendaftaran->refresh();
        $this->assertEquals('rejected', $this->pendaftaran->status_pendaftaran);
        $this->assertEquals($this->adminProvinsi->id_user, $this->pendaftaran->verified_by);
        $this->assertEquals('Registration rejected due to incomplete documents', $this->pendaftaran->catatan_verifikasi);
    }

    /** @test */
    public function admin_provinsi_can_bulk_verify_registrations()
    {
        // Create additional registrations
        $pendaftaran2 = Pendaftaran::factory()->create([
            'id_peserta' => $this->peserta->id_peserta,
            'id_golongan' => $this->pendaftaran->id_golongan,
            'status_pendaftaran' => 'submitted'
        ]);

        $response = $this->actingAs($this->adminProvinsi)
            ->post('/admin/registration-verification/bulk-verify', [
                'registration_ids' => [
                    $this->pendaftaran->id_pendaftaran,
                    $pendaftaran2->id_pendaftaran
                ],
                'action' => 'approve',
                'notes' => 'Bulk approval'
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->pendaftaran->refresh();
        $pendaftaran2->refresh();

        $this->assertEquals('verified', $this->pendaftaran->status_pendaftaran);
        $this->assertEquals('verified', $pendaftaran2->status_pendaftaran);
    }

    /** @test */
    public function admin_provinsi_can_verify_documents()
    {
        // Create document type and document
        $documentType = DocumentType::factory()->create(['is_active' => true]);
        $dokumen = DokumenPeserta::factory()->create([
            'id_pendaftaran' => $this->pendaftaran->id_pendaftaran,
            'document_type_id' => $documentType->id_document_type,
            'status_verifikasi' => 'pending'
        ]);

        $response = $this->actingAs($this->adminProvinsi)
            ->post("/admin/registration-verification/documents/{$dokumen->id_dokumen}/verify", [
                'status_verifikasi' => 'approved',
                'catatan_verifikasi' => 'Document approved'
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $dokumen->refresh();
        $this->assertEquals('approved', $dokumen->status_verifikasi);
        $this->assertEquals('Document approved', $dokumen->catatan_verifikasi);
        $this->assertEquals($this->adminProvinsi->id_user, $dokumen->verified_by);
    }

    /** @test */
    public function admin_provinsi_can_verify_participant_data()
    {
        // Create verification type
        $verificationType = VerificationType::factory()->create([
            'is_active' => true,
            'is_required' => true
        ]);

        $response = $this->actingAs($this->adminProvinsi)
            ->post("/admin/registration-verification/{$this->pendaftaran->id_pendaftaran}/verify-participant-data", [
                'verification_type_id' => $verificationType->id_verification_type,
                'status' => 'verified',
                'notes' => 'Participant data verified',
                'verification_data' => ['nik' => $this->peserta->nik]
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('participant_verifications', [
            'id_peserta' => $this->peserta->id_peserta,
            'verification_type_id' => $verificationType->id_verification_type,
            'status' => 'verified',
            'verified_by' => $this->adminProvinsi->id_user
        ]);
    }

    /** @test */
    public function verification_dashboard_shows_correct_statistics()
    {
        // Create additional test data for statistics
        $pendaftaran2 = Pendaftaran::factory()->create([
            'status_pendaftaran' => 'verified'
        ]);
        $pendaftaran3 = Pendaftaran::factory()->create([
            'status_pendaftaran' => 'rejected'
        ]);

        $response = $this->actingAs($this->adminProvinsi)
            ->get('/admin/registration-verification');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('stats')
                ->where('stats.total_registrations', 3)
                ->where('stats.pending_verification', 1) // Only our original submitted registration
                ->where('stats.verified', 1)
                ->where('stats.rejected', 1)
        );
    }

    /** @test */
    public function verification_dashboard_filters_work_correctly()
    {
        $response = $this->actingAs($this->adminProvinsi)
            ->get('/admin/registration-verification?status=submitted');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('registrations.data')
                ->where('filters.status', 'submitted')
        );
    }

    /** @test */
    public function verification_dashboard_search_works_correctly()
    {
        $response = $this->actingAs($this->adminProvinsi)
            ->get('/admin/registration-verification?search=' . $this->peserta->nama_lengkap);

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('registrations.data')
                ->where('filters.search', $this->peserta->nama_lengkap)
        );
    }

    /** @test */
    public function admin_daerah_cannot_verify_registrations()
    {
        $response = $this->actingAs($this->adminDaerah)
            ->post("/admin/registration-verification/{$this->pendaftaran->id_pendaftaran}/verify", [
                'action' => 'approve',
                'notes' => 'Should not work'
            ]);

        $response->assertStatus(403);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_verification()
    {
        $response = $this->get('/admin/registration-verification');
        $response->assertRedirect('/login');
    }
}
