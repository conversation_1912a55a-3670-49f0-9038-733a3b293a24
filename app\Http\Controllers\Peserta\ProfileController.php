<?php

namespace App\Http\Controllers\Peserta;

use App\Http\Controllers\Controller;
use App\Models\Wilayah;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class ProfileController extends Controller
{
    /**
     * Display the peserta profile.
     */
    public function show()
    {
        $user = Auth::user();
        $peserta = $user->peserta;

        if (!$peserta) {
            return redirect()->route('peserta.dashboard')
                ->with('error', 'Profil peserta belum dibuat. Silakan hubungi administrator.');
        }

        $peserta->load(['wilayah', 'registeredBy']);

        return Inertia::render('Peserta/Profile/Show', [
            'peserta' => $peserta,
            'wilayah' => Wilayah::aktif()->get()
        ]);
    }

    /**
     * Update the peserta profile.
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        $peserta = $user->peserta;

        if (!$peserta) {
            return back()->with('error', 'Profil peserta tidak ditemukan.');
        }

        // Only allow updates if status is draft or submitted
        if (!in_array($peserta->status_peserta, ['draft', 'submitted'])) {
            return back()->with('error', 'Profil tidak dapat diubah karena sudah diverifikasi atau disetujui.');
        }

        $validated = $request->validate([
            'nik' => ['required', 'string', 'size:16', Rule::unique('peserta')->ignore($peserta->id_peserta, 'id_peserta')],
            'nama_lengkap' => 'required|string|max:100',
            'tempat_lahir' => 'required|string|max:50',
            'tanggal_lahir' => 'required|date|before:today',
            'jenis_kelamin' => ['required', Rule::in(['L', 'P'])],
            'alamat' => 'required|string',
            'id_wilayah' => 'required|exists:wilayah,id_wilayah',
            'no_telepon' => 'nullable|string|max:20',
            'email' => ['nullable', 'email', 'max:100', Rule::unique('peserta')->ignore($peserta->id_peserta, 'id_peserta')],
            'nama_ayah' => 'nullable|string|max:100',
            'nama_ibu' => 'nullable|string|max:100',
            'pekerjaan' => 'nullable|string|max:100',
            'instansi_asal' => 'nullable|string|max:100',
            'nama_rekening' => 'nullable|string|max:100',
            'no_rekening' => 'nullable|string|max:40',
        ]);

        DB::transaction(function () use ($validated, $peserta, $user) {
            // Update peserta profile
            $peserta->update($validated);

            // Update user data as well
            $user->update([
                'nama_lengkap' => $validated['nama_lengkap'],
                'no_telepon' => $validated['no_telepon'],
                'email' => $validated['email'] ?? $user->email,
                'id_wilayah' => $validated['id_wilayah'],
            ]);

            // If profile was draft, change to submitted
            if ($peserta->status_peserta === 'draft') {
                $peserta->update(['status_peserta' => 'submitted']);
            }
        });

        return back()->with('success', 'Profil berhasil diperbarui.');
    }

    /**
     * Upload profile photo.
     */
    public function uploadPhoto(Request $request)
    {
        $user = Auth::user();
        $peserta = $user->peserta;

        if (!$peserta) {
            return back()->with('error', 'Profil peserta tidak ditemukan.');
        }

        $request->validate([
            'photo' => 'required|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        if ($request->hasFile('photo')) {
            // Delete old photo if exists
            if ($peserta->foto_path && Storage::disk('public')->exists($peserta->foto_path)) {
                Storage::disk('public')->delete($peserta->foto_path);
            }

            // Store new photo
            $path = $request->file('photo')->store('peserta/photos', 'public');

            $peserta->update(['foto_path' => $path]);

            return back()->with('success', 'Foto profil berhasil diupload.');
        }

        return back()->with('error', 'Gagal mengupload foto profil.');
    }

    /**
     * Submit profile for verification.
     */
    public function submit()
    {
        $user = Auth::user();
        $peserta = $user->peserta;

        if (!$peserta) {
            return back()->with('error', 'Profil peserta tidak ditemukan.');
        }

        if ($peserta->status_peserta !== 'draft') {
            return back()->with('error', 'Profil sudah disubmit atau dalam proses verifikasi.');
        }

        // Check if required fields are filled
        $requiredFields = ['nik', 'nama_lengkap', 'tempat_lahir', 'tanggal_lahir', 'jenis_kelamin', 'alamat', 'id_wilayah'];
        foreach ($requiredFields as $field) {
            if (empty($peserta->$field)) {
                return back()->with('error', 'Mohon lengkapi semua data yang diperlukan sebelum submit.');
            }
        }

        $peserta->update(['status_peserta' => 'submitted']);

        return back()->with('success', 'Profil berhasil disubmit untuk verifikasi.');
    }
}
