<?php

namespace Tests\Feature\Admin;

use App\Models\User;
use App\Models\Wilayah;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UserManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a superadmin user for testing
        $this->superadmin = User::factory()->create([
            'role' => 'superadmin',
            'status' => 'aktif'
        ]);
        
        // Create a wilayah for testing
        $this->wilayah = Wilayah::factory()->create([
            'status' => 'aktif'
        ]);
    }

    /** @test */
    public function superadmin_can_view_users_index()
    {
        $this->actingAs($this->superadmin);
        
        $response = $this->get(route('admin.users.index'));
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Admin/Users/<USER>'));
    }

    /** @test */
    public function superadmin_can_create_user()
    {
        $this->actingAs($this->superadmin);
        
        $userData = [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'admin_daerah',
            'nama_lengkap' => 'Test User',
            'no_telepon' => '081234567890',
            'id_wilayah' => $this->wilayah->id_wilayah,
            'status' => 'aktif'
        ];
        
        $response = $this->post(route('admin.users.store'), $userData);
        
        $response->assertRedirect(route('admin.users.index'));
        $this->assertDatabaseHas('users', [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'role' => 'admin_daerah'
        ]);
    }

    /** @test */
    public function superadmin_can_update_user()
    {
        $this->actingAs($this->superadmin);
        
        $user = User::factory()->create([
            'role' => 'admin_daerah',
            'id_wilayah' => $this->wilayah->id_wilayah
        ]);
        
        $updateData = [
            'username' => 'updateduser',
            'email' => '<EMAIL>',
            'role' => 'admin_daerah',
            'nama_lengkap' => 'Updated User',
            'no_telepon' => '081234567891',
            'id_wilayah' => $this->wilayah->id_wilayah,
            'status' => 'aktif'
        ];
        
        $response = $this->put(route('admin.users.update', $user), $updateData);
        
        $response->assertRedirect(route('admin.users.index'));
        $this->assertDatabaseHas('users', [
            'id_user' => $user->id_user,
            'username' => 'updateduser',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function superadmin_can_toggle_user_status()
    {
        $this->actingAs($this->superadmin);
        
        $user = User::factory()->create([
            'status' => 'aktif'
        ]);
        
        $response = $this->post(route('admin.users.toggle-status', $user));
        
        $response->assertRedirect();
        $this->assertDatabaseHas('users', [
            'id_user' => $user->id_user,
            'status' => 'non_aktif'
        ]);
    }

    /** @test */
    public function superadmin_can_reset_user_password()
    {
        $this->actingAs($this->superadmin);
        
        $user = User::factory()->create();
        $originalPassword = $user->password;
        
        $response = $this->post(route('admin.users.reset-password', $user));
        
        $response->assertRedirect();
        $user->refresh();
        $this->assertNotEquals($originalPassword, $user->password);
    }

    /** @test */
    public function superadmin_cannot_delete_user_with_related_data()
    {
        $this->actingAs($this->superadmin);
        
        // Create user with related data (peserta)
        $user = User::factory()->create(['role' => 'peserta']);
        
        $response = $this->delete(route('admin.users.destroy', $user));
        
        $response->assertRedirect();
        $response->assertSessionHas('error');
        $this->assertDatabaseHas('users', ['id_user' => $user->id_user]);
    }

    /** @test */
    public function non_superadmin_cannot_access_user_management()
    {
        $regularUser = User::factory()->create(['role' => 'admin_daerah']);
        $this->actingAs($regularUser);
        
        $response = $this->get(route('admin.users.index'));
        
        $response->assertStatus(403);
    }

    /** @test */
    public function user_creation_validates_required_fields()
    {
        $this->actingAs($this->superadmin);
        
        $response = $this->post(route('admin.users.store'), []);
        
        $response->assertSessionHasErrors([
            'username',
            'email',
            'password',
            'role',
            'nama_lengkap',
            'status'
        ]);
    }

    /** @test */
    public function user_creation_validates_unique_username_and_email()
    {
        $this->actingAs($this->superadmin);
        
        $existingUser = User::factory()->create();
        
        $response = $this->post(route('admin.users.store'), [
            'username' => $existingUser->username,
            'email' => $existingUser->email,
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'admin',
            'nama_lengkap' => 'Test User',
            'status' => 'aktif'
        ]);
        
        $response->assertSessionHasErrors(['username', 'email']);
    }

    /** @test */
    public function user_search_functionality_works()
    {
        $this->actingAs($this->superadmin);
        
        User::factory()->create(['nama_lengkap' => 'John Doe']);
        User::factory()->create(['nama_lengkap' => 'Jane Smith']);
        
        $response = $this->get(route('admin.users.index', ['search' => 'John']));
        
        $response->assertStatus(200);
        // Additional assertions can be added to check if search results are correct
    }

    /** @test */
    public function user_filtering_by_role_works()
    {
        $this->actingAs($this->superadmin);
        
        User::factory()->create(['role' => 'admin']);
        User::factory()->create(['role' => 'peserta']);
        
        $response = $this->get(route('admin.users.index', ['role' => 'admin']));
        
        $response->assertStatus(200);
        // Additional assertions can be added to check if filter results are correct
    }
}
