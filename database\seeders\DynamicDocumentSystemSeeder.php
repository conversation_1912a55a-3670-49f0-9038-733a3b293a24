<?php

namespace Database\Seeders;

use App\Models\DocumentType;
use App\Models\VerificationType;
use App\Models\Golongan;
use App\Models\GolonganDocumentRequirement;
use Illuminate\Database\Seeder;

class DynamicDocumentSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Seeding Dynamic Document System...');

        // 1. Seed Document Types (based on existing hardcoded types)
        $this->seedDocumentTypes();

        // 2. Seed Verification Types
        $this->seedVerificationTypes();

        // 3. Seed Golongan Document Requirements (all golongan require all documents by default)
        $this->seedGolonganDocumentRequirements();

        $this->command->info('✅ Dynamic Document System seeded successfully!');
    }

    private function seedDocumentTypes(): void
    {
        $this->command->info('📄 Seeding document types...');

        $documentTypes = [
            [
                'name' => 'Foto Peserta',
                'code' => 'foto',
                'description' => 'Foto peserta dengan latar belakang merah atau biru',
                'allowed_file_types' => ['jpg', 'jpeg', 'png'],
                'max_file_size' => 2048, // 2MB
                'is_required_default' => true,
                'sort_order' => 1
            ],
            [
                'name' => 'KTP/Identitas',
                'code' => 'ktp',
                'description' => 'Kartu Tanda Penduduk atau identitas resmi lainnya',
                'allowed_file_types' => ['jpg', 'jpeg', 'png', 'pdf'],
                'max_file_size' => 3072, // 3MB
                'is_required_default' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'Kartu Keluarga',
                'code' => 'kartu_keluarga',
                'description' => 'Kartu Keluarga peserta',
                'allowed_file_types' => ['jpg', 'jpeg', 'png', 'pdf'],
                'max_file_size' => 3072, // 3MB
                'is_required_default' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'Surat Rekomendasi',
                'code' => 'surat_rekomendasi',
                'description' => 'Surat rekomendasi dari instansi atau lembaga terkait',
                'allowed_file_types' => ['jpg', 'jpeg', 'png', 'pdf'],
                'max_file_size' => 5120, // 5MB
                'is_required_default' => false,
                'sort_order' => 4
            ],
            [
                'name' => 'Ijazah Terakhir',
                'code' => 'ijazah',
                'description' => 'Ijazah pendidikan terakhir peserta',
                'allowed_file_types' => ['jpg', 'jpeg', 'png', 'pdf'],
                'max_file_size' => 5120, // 5MB
                'is_required_default' => true,
                'sort_order' => 5
            ],
            [
                'name' => 'Sertifikat Pendukung',
                'code' => 'sertifikat',
                'description' => 'Sertifikat prestasi atau keahlian yang mendukung',
                'allowed_file_types' => ['jpg', 'jpeg', 'png', 'pdf'],
                'max_file_size' => 5120, // 5MB
                'is_required_default' => false, // Optional by default
                'sort_order' => 6
            ],
            [
                'name' => 'Dokumen Lainnya',
                'code' => 'lainnya',
                'description' => 'Dokumen pendukung lainnya',
                'allowed_file_types' => ['jpg', 'jpeg', 'png', 'pdf'],
                'max_file_size' => 5120, // 5MB
                'is_required_default' => false, // Optional by default
                'sort_order' => 7
            ]
        ];

        foreach ($documentTypes as $docType) {
            DocumentType::updateOrCreate(
                ['code' => $docType['code']],
                $docType
            );
        }

        $this->command->info('   ✓ Document types seeded');
    }

    private function seedVerificationTypes(): void
    {
        $this->command->info('🔍 Seeding verification types...');

        $verificationTypes = [
            [
                'name' => 'Verifikasi NIK',
                'code' => 'nik',
                'description' => 'Verifikasi kevalidan Nomor Induk Kependudukan peserta',
                'is_active' => true,
                'is_required' => true,
                'settings' => [
                    'verification_method' => 'manual_check',
                    'requires_notes' => false
                ],
                'sort_order' => 1
            ],
            [
                'name' => 'Verifikasi Dokumen',
                'code' => 'document',
                'description' => 'Verifikasi kelengkapan dan keabsahan dokumen peserta',
                'is_active' => true,
                'is_required' => true,
                'settings' => [
                    'verification_method' => 'document_review',
                    'requires_notes' => true
                ],
                'sort_order' => 2
            ]
        ];

        foreach ($verificationTypes as $verType) {
            VerificationType::updateOrCreate(
                ['code' => $verType['code']],
                $verType
            );
        }

        $this->command->info('   ✓ Verification types seeded');
    }

    private function seedGolonganDocumentRequirements(): void
    {
        $this->command->info('📋 Seeding golongan document requirements...');

        $golonganList = Golongan::all();
        $documentTypes = DocumentType::all();

        if ($golonganList->isEmpty()) {
            $this->command->warn('   ⚠️  No golongan found. Please run golongan seeder first.');
            return;
        }

        if ($documentTypes->isEmpty()) {
            $this->command->warn('   ⚠️  No document types found. Something went wrong with document type seeding.');
            return;
        }

        $count = 0;
        foreach ($golonganList as $golongan) {
            foreach ($documentTypes as $docType) {
                GolonganDocumentRequirement::updateOrCreate(
                    [
                        'id_golongan' => $golongan->id_golongan,
                        'document_type_id' => $docType->id_document_type
                    ],
                    [
                        'is_required' => $docType->is_required_default
                    ]
                );
                $count++;
            }
        }

        $this->command->info("   ✓ Created {$count} golongan document requirements");
    }
}
