<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('golongan_document_requirements', function (Blueprint $table) {
            $table->id('id_requirement');
            $table->unsignedBigInteger('id_golongan');
            $table->unsignedBigInteger('document_type_id');
            $table->boolean('is_required')->default(true); // Whether this document is required for this golongan
            $table->timestamps();

            // Foreign keys
            $table->foreign('id_golongan')->references('id_golongan')->on('golongan')->onDelete('cascade');
            $table->foreign('document_type_id')->references('id_document_type')->on('document_types')->onDelete('cascade');

            // Unique constraint to prevent duplicate requirements
            $table->unique(['id_golongan', 'document_type_id'], 'unique_golongan_document');

            // Indexes
            $table->index(['id_golongan']);
            $table->index(['document_type_id']);
            $table->index(['is_required']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('golongan_document_requirements');
    }
};
