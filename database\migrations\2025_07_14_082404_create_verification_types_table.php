<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('verification_types', function (Blueprint $table) {
            $table->id('id_verification_type');
            $table->string('name', 100); // Display name (e.g., "NIK Verification")
            $table->string('code', 50)->unique(); // Code for system use (e.g., "nik")
            $table->text('description')->nullable(); // Description of the verification type
            $table->boolean('is_active')->default(true); // Whether this verification type is active
            $table->boolean('is_required')->default(true); // Whether this verification is required
            $table->json('settings')->nullable(); // Additional settings for the verification type
            $table->integer('sort_order')->default(0); // For ordering in UI
            $table->timestamps();

            // Indexes
            $table->index(['is_active']);
            $table->index(['sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('verification_types');
    }
};
