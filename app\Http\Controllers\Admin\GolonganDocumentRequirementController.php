<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Golongan;
use App\Models\DocumentType;
use App\Models\GolonganDocumentRequirement;
use App\Traits\HasVerificationPermissions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class GolonganDocumentRequirementController extends Controller
{
    use HasVerificationPermissions;

    /**
     * Display golongan document requirements management
     */
    public function index(Request $request): Response
    {
        $this->requireAdminAccess();

        $query = Golongan::with(['cabangLomba', 'allDocumentTypes']);

        // Filter by cabang lomba
        if ($request->filled('cabang_lomba')) {
            $query->where('id_cabang', $request->cabang_lomba);
        }

        // Search by golongan name
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_golongan', 'like', "%{$search}%")
                  ->orWhere('kode_golongan', 'like', "%{$search}%");
            });
        }

        $golongan = $query->aktif()->paginate(20);
        $cabangLomba = \App\Models\CabangLomba::aktif()->get();
        $documentTypes = DocumentType::active()->ordered()->get();

        return Inertia::render('Admin/GolonganDocumentRequirements/Index', [
            'golongan' => $golongan,
            'cabangLomba' => $cabangLomba,
            'documentTypes' => $documentTypes,
            'filters' => $request->only(['cabang_lomba', 'search'])
        ]);
    }

    /**
     * Show document requirements for specific golongan
     */
    public function show(Golongan $golongan): Response
    {
        $this->requireAdminAccess();

        $golongan->load(['cabangLomba', 'allDocumentTypes']);
        $allDocumentTypes = DocumentType::active()->ordered()->get();

        return Inertia::render('Admin/GolonganDocumentRequirements/Show', [
            'golongan' => $golongan,
            'allDocumentTypes' => $allDocumentTypes
        ]);
    }

    /**
     * Update document requirements for specific golongan
     */
    public function update(Request $request, Golongan $golongan)
    {
        $this->requireAdminAccess();

        $validated = $request->validate([
            'requirements' => 'required|array',
            'requirements.*.document_type_id' => 'required|exists:document_types,id_document_type',
            'requirements.*.is_required' => 'required|boolean'
        ]);

        // Delete existing requirements for this golongan
        GolonganDocumentRequirement::where('id_golongan', $golongan->id_golongan)->delete();

        // Create new requirements
        foreach ($validated['requirements'] as $requirement) {
            GolonganDocumentRequirement::create([
                'id_golongan' => $golongan->id_golongan,
                'document_type_id' => $requirement['document_type_id'],
                'is_required' => $requirement['is_required']
            ]);
        }

        return back()->with('success', 'Persyaratan dokumen golongan berhasil diperbarui.');
    }

    /**
     * Bulk update requirements for multiple golongan
     */
    public function bulkUpdate(Request $request)
    {
        $this->requireAdminAccess();

        $validated = $request->validate([
            'golongan_ids' => 'required|array',
            'golongan_ids.*' => 'exists:golongan,id_golongan',
            'requirements' => 'required|array',
            'requirements.*.document_type_id' => 'required|exists:document_types,id_document_type',
            'requirements.*.is_required' => 'required|boolean'
        ]);

        foreach ($validated['golongan_ids'] as $golonganId) {
            // Delete existing requirements
            GolonganDocumentRequirement::where('id_golongan', $golonganId)->delete();

            // Create new requirements
            foreach ($validated['requirements'] as $requirement) {
                GolonganDocumentRequirement::create([
                    'id_golongan' => $golonganId,
                    'document_type_id' => $requirement['document_type_id'],
                    'is_required' => $requirement['is_required']
                ]);
            }
        }

        $count = count($validated['golongan_ids']);
        return back()->with('success', "Persyaratan dokumen berhasil diperbarui untuk {$count} golongan.");
    }
}
