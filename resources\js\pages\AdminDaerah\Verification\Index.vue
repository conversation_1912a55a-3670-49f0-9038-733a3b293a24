<script setup lang="ts">
import { Head, router } from '@inertiajs/vue3'
import { ref, computed } from 'vue'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/ui/Heading.vue'
import Card from '@/components/ui/Card.vue'
import CardContent from '@/components/ui/CardContent.vue'
import Button from '@/components/ui/Button.vue'
import TextLink from '@/components/ui/TextLink.vue'
import Icon from '@/components/ui/Icon.vue'
import Badge from '@/components/ui/Badge.vue'
import Input from '@/components/ui/Input.vue'
import Select from '@/components/ui/Select.vue'
import Checkbox from '@/components/ui/Checkbox.vue'
import { useToast } from '@/composables/useToast'

interface User {
  username: string
  email: string
}

interface Wilayah {
  nama_wilayah: string
}

interface Participant {
  id_peserta: number
  nama_lengkap: string
  nik: string
  jenis_kelamin: string
  tanggal_lahir: string
  status_peserta: string
  registration_type: string
  regional_verification_status: string
  regional_verified_at: string | null
  created_at: string
  user: User
  wilayah: Wilayah
  pendaftaran: Array<{
    nomor_pendaftaran: string
    status_pendaftaran: string
    golongan: {
      nama_golongan: string
      cabang_lomba: {
        nama_cabang: string
      }
    }
  }>
  regional_verified_by?: {
    nama_lengkap: string
  }
}

interface Stats {
  total_pending: number
  total_verified: number
  total_rejected: number
}

interface PaginationData {
  data: Participant[]
  current_page: number
  last_page: number
  per_page: number
  total: number
  from: number
  to: number
}

interface Props {
  participants: PaginationData
  stats: Stats
  filters: {
    status?: string
    search?: string
  }
}

const props = defineProps<Props>()
const { showToast } = useToast()

// Reactive data
const selectedParticipants = ref<number[]>([])
const isSubmitting = ref(false)
const searchQuery = ref(props.filters.search || '')
const statusFilter = ref(props.filters.status || '')

// Computed
const allSelected = computed(() => {
  return props.participants.data.length > 0 && 
         selectedParticipants.value.length === props.participants.data.length
})

const someSelected = computed(() => {
  return selectedParticipants.value.length > 0 && 
         selectedParticipants.value.length < props.participants.data.length
})

// Methods
const toggleAll = () => {
  if (allSelected.value) {
    selectedParticipants.value = []
  } else {
    selectedParticipants.value = props.participants.data.map(p => p.id_peserta)
  }
}

const applyFilters = () => {
  router.get(route('admin-daerah.verification.index'), {
    search: searchQuery.value,
    status: statusFilter.value
  }, {
    preserveState: true,
    replace: true
  })
}

const clearFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  router.get(route('admin-daerah.verification.index'), {}, {
    preserveState: true,
    replace: true
  })
}

const bulkVerify = async (action: 'verify' | 'reject') => {
  if (selectedParticipants.value.length === 0) {
    showToast('Pilih peserta terlebih dahulu', 'error')
    return
  }

  const notes = action === 'reject' 
    ? prompt('Masukkan alasan penolakan:')
    : 'Verifikasi regional berhasil'

  if (action === 'reject' && !notes) return

  isSubmitting.value = true

  try {
    await router.post(route('admin-daerah.verification.bulk-verify'), {
      participant_ids: selectedParticipants.value,
      action,
      notes
    }, {
      onSuccess: () => {
        selectedParticipants.value = []
        showToast(
          action === 'verify' 
            ? 'Peserta berhasil diverifikasi' 
            : 'Peserta berhasil ditolak',
          'success'
        )
      },
      onError: () => {
        showToast('Terjadi kesalahan', 'error')
      }
    })
  } finally {
    isSubmitting.value = false
  }
}

const getStatusColor = (status: string): string => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    verified: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

const getParticipantStatusColor = (status: string): string => {
  const colors = {
    draft: 'bg-gray-100 text-gray-800',
    submitted: 'bg-blue-100 text-blue-800',
    regional_verified: 'bg-indigo-100 text-indigo-800',
    verified: 'bg-green-100 text-green-800',
    approved: 'bg-emerald-100 text-emerald-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

const breadcrumbItems = [
  { label: 'Dashboard', href: route('admin-daerah.dashboard') },
  { label: 'Verifikasi Regional', href: route('admin-daerah.verification.index') }
]
</script>

<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Verifikasi Regional - Admin Daerah" />
    
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <Heading title="Verifikasi Regional" />
        <div class="flex space-x-2">
          <Button 
            v-if="selectedParticipants.length > 0"
            @click="bulkVerify('verify')"
            :disabled="isSubmitting"
            class="bg-green-600 hover:bg-green-700"
          >
            <Icon name="check" class="w-4 h-4 mr-2" />
            Verifikasi Terpilih ({{ selectedParticipants.length }})
          </Button>
          <Button 
            v-if="selectedParticipants.length > 0"
            @click="bulkVerify('reject')"
            :disabled="isSubmitting"
            variant="destructive"
          >
            <Icon name="x" class="w-4 h-4 mr-2" />
            Tolak Terpilih ({{ selectedParticipants.length }})
          </Button>
        </div>
      </div>

      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-yellow-500 rounded-full">
                <Icon name="clock" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-yellow-700">{{ stats.total_pending }}</p>
                <p class="text-sm text-yellow-600">Menunggu Verifikasi</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-green-500 rounded-full">
                <Icon name="check" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-green-700">{{ stats.total_verified }}</p>
                <p class="text-sm text-green-600">Terverifikasi</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-red-500 rounded-full">
                <Icon name="x" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-red-700">{{ stats.total_rejected }}</p>
                <p class="text-sm text-red-600">Ditolak</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Filters -->
      <Card>
        <CardContent class="p-6">
          <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
              <Input
                v-model="searchQuery"
                placeholder="Cari nama, NIK, atau email..."
                @keyup.enter="applyFilters"
              />
            </div>
            <div class="w-full md:w-48">
              <Select v-model="statusFilter">
                <option value="">Semua Status</option>
                <option value="draft">Draft</option>
                <option value="submitted">Submitted</option>
                <option value="regional_verified">Regional Verified</option>
              </Select>
            </div>
            <div class="flex space-x-2">
              <Button @click="applyFilters">
                <Icon name="search" class="w-4 h-4 mr-2" />
                Filter
              </Button>
              <Button @click="clearFilters" variant="outline">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Participants Table -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left">
                    <Checkbox 
                      :checked="allSelected"
                      :indeterminate="someSelected"
                      @update:checked="toggleAll"
                    />
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Peserta
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status Peserta
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status Verifikasi
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pendaftaran
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tanggal Daftar
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="participant in participants.data" :key="participant.id_peserta">
                  <td class="px-6 py-4">
                    <Checkbox 
                      :checked="selectedParticipants.includes(participant.id_peserta)"
                      @update:checked="(checked) => {
                        if (checked) {
                          selectedParticipants.push(participant.id_peserta)
                        } else {
                          const index = selectedParticipants.indexOf(participant.id_peserta)
                          if (index > -1) selectedParticipants.splice(index, 1)
                        }
                      }"
                    />
                  </td>
                  <td class="px-6 py-4">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ participant.nama_lengkap }}
                      </div>
                      <div class="text-sm text-gray-500">
                        NIK: {{ participant.nik }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ participant.user.email }}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <Badge :class="getParticipantStatusColor(participant.status_peserta)">
                      {{ participant.status_peserta }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4">
                    <Badge :class="getStatusColor(participant.regional_verification_status)">
                      {{ participant.regional_verification_status }}
                    </Badge>
                    <div v-if="participant.regional_verified_by" class="text-xs text-gray-500 mt-1">
                      oleh {{ participant.regional_verified_by.nama_lengkap }}
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div v-for="registration in participant.pendaftaran" :key="registration.nomor_pendaftaran" class="text-sm">
                      <div class="font-medium">{{ registration.golongan.cabang_lomba.nama_cabang }}</div>
                      <div class="text-gray-500">{{ registration.golongan.nama_golongan }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-500">
                    {{ new Date(participant.created_at).toLocaleDateString('id-ID') }}
                  </td>
                  <td class="px-6 py-4">
                    <TextLink 
                      :href="route('admin-daerah.verification.show', participant.id_peserta)"
                      class="text-blue-600 hover:text-blue-900"
                    >
                      <Icon name="eye" class="w-4 h-4 mr-1" />
                      Detail
                    </TextLink>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <!-- Pagination would go here -->
          <div v-if="participants.data.length === 0" class="text-center py-8 text-gray-500">
            Tidak ada peserta yang perlu diverifikasi
          </div>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>
