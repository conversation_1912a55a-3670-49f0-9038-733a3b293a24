<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Tambah Jenis Verifikasi" />
    <Heading title="Tambah Jenis Verifikasi" />

    <div class="max-w-4xl mx-auto">
      <Card>
        <CardContent class="p-6">
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label for="name" class="required"><PERSON><PERSON></Label>
                <Input
                  id="name"
                  v-model="form.name"
                  placeholder="e.g., Verifikasi NIK"
                  :class="{ 'border-red-500': form.errors.name }"
                />
                <InputError :message="form.errors.name" />
              </div>

              <div>
                <Label for="code" class="required">Kode</Label>
                <Input
                  id="code"
                  v-model="form.code"
                  placeholder="e.g., nik"
                  :class="{ 'border-red-500': form.errors.code }"
                />
                <InputError :message="form.errors.code" />
                <p class="text-sm text-gray-500 mt-1">Kode unik untuk sistem (huruf kecil, tanpa spasi)</p>
              </div>
            </div>

            <!-- Description -->
            <div>
              <Label for="description">Deskripsi</Label>
              <Textarea
                id="description"
                v-model="form.description"
                placeholder="Deskripsi jenis verifikasi..."
                rows="3"
                :class="{ 'border-red-500': form.errors.description }"
              />
              <InputError :message="form.errors.description" />
            </div>

            <!-- Settings -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <Label for="sort_order">Urutan Tampil</Label>
                <Input
                  id="sort_order"
                  v-model.number="form.sort_order"
                  type="number"
                  min="0"
                  placeholder="0"
                  :class="{ 'border-red-500': form.errors.sort_order }"
                />
                <InputError :message="form.errors.sort_order" />
              </div>

              <div class="flex items-center space-x-2">
                <Checkbox
                  id="is_required"
                  v-model:checked="form.is_required"
                />
                <Label for="is_required">Wajib diverifikasi</Label>
              </div>

              <div class="flex items-center space-x-2">
                <Checkbox
                  id="is_active"
                  v-model:checked="form.is_active"
                />
                <Label for="is_active">Aktif</Label>
              </div>
            </div>

            <!-- Advanced Settings -->
            <div>
              <Label>Pengaturan Lanjutan</Label>
              <div class="mt-2 space-y-4 p-4 border rounded-lg bg-gray-50">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label for="verification_method">Metode Verifikasi</Label>
                    <Select v-model="form.settings.verification_method">
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih metode" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="manual_check">Manual Check</SelectItem>
                        <SelectItem value="document_review">Document Review</SelectItem>
                        <SelectItem value="api_validation">API Validation</SelectItem>
                        <SelectItem value="external_system">External System</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div class="flex items-center space-x-2">
                    <Checkbox
                      id="requires_notes"
                      v-model:checked="form.settings.requires_notes"
                    />
                    <Label for="requires_notes">Wajib ada catatan</Label>
                  </div>
                </div>

                <div v-if="form.settings.verification_method === 'api_validation'">
                  <Label for="api_endpoint">API Endpoint</Label>
                  <Input
                    id="api_endpoint"
                    v-model="form.settings.api_endpoint"
                    placeholder="https://api.example.com/verify"
                  />
                </div>

                <div v-if="form.settings.verification_method === 'external_system'">
                  <Label for="system_name">Nama Sistem External</Label>
                  <Input
                    id="system_name"
                    v-model="form.settings.system_name"
                    placeholder="Nama sistem external"
                  />
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-between pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.verification-types.index'))"
              >
                <Icon name="arrow-left" class="w-4 h-4 mr-2" />
                Kembali
              </Button>

              <Button type="submit" :disabled="form.processing">
                <Icon name="save" class="w-4 h-4 mr-2" />
                {{ form.processing ? 'Menyimpan...' : 'Simpan' }}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup>
import { reactive } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import InputError from '@/components/InputError.vue'
import Icon from '@/components/Icon.vue'

const breadcrumbItems = [
  { label: 'Dashboard', href: route('admin.dashboard') },
  { label: 'Manajemen Jenis Verifikasi', href: route('admin.verification-types.index') },
  { label: 'Tambah Jenis Verifikasi', href: null }
]

const form = useForm({
  name: '',
  code: '',
  description: '',
  is_active: true,
  is_required: true,
  sort_order: 0,
  settings: {
    verification_method: 'manual_check',
    requires_notes: false,
    api_endpoint: '',
    system_name: ''
  }
})

const submit = () => {
  form.post(route('admin.verification-types.store'))
}
</script>

<style scoped>
.required::after {
  content: ' *';
  color: #ef4444;
}
</style>
