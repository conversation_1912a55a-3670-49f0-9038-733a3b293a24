<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Peserta;
use App\Models\Pendaftaran;
use App\Models\User;
use App\Models\Wilayah;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index()
    {
        $stats = [
            'total_peserta' => Peserta::count(),
            'total_pendaftaran' => Pendaftaran::count(),
            'total_users' => User::count(),
            'total_wilayah' => Wilayah::count(),
            'pendaftaran_pending' => Pendaftaran::where('status_pendaftaran', 'draft')->count(),
            'pendaftaran_approved' => Pendaftaran::where('status_pendaftaran', 'approved')->count(),
        ];

        $calonPeserta = Peserta::all();

        $recent_pendaftaran = Pendaftaran::with(['peserta', 'golongan.cabangLomba'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'recent_pendaftaran' => $recent_pendaftaran,
            'calon_peserta' => $calonPeserta
        ]);
    }
}
