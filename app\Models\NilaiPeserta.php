<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NilaiPeserta extends Model
{
    protected $table = 'nilai_peserta';
    protected $primaryKey = 'id_nilai';

    protected $fillable = [
        'id_pendaftaran',
        'id_jenis_nilai',
        'id_dewan_hakim',
        'nilai',
        'catatan'
    ];

    protected $casts = [
        'nilai' => 'decimal:2'
    ];

    // Relationships
    public function pendaftaran(): BelongsTo
    {
        return $this->belongsTo(Pendaftaran::class, 'id_pendaftaran', 'id_pendaftaran');
    }

    public function jenisNilai(): BelongsTo
    {
        return $this->belongsTo(JenisNilai::class, 'id_jenis_nilai', 'id_jenis_nilai');
    }

    public function dewaHakim(): BelongsTo
    {
        return $this->belongsTo(DewaHakim::class, 'id_dewan_hakim', 'id_dewan_hakim');
    }

    // Scopes
    public function scopeByPendaftaran($query, $idPendaftaran)
    {
        return $query->where('id_pendaftaran', $idPendaftaran);
    }

    public function scopeByJenisNilai($query, $idJenisNilai)
    {
        return $query->where('id_jenis_nilai', $idJenisNilai);
    }

    public function scopeByDewaHakim($query, $idDewaHakim)
    {
        return $query->where('id_dewan_hakim', $idDewaHakim);
    }

    // Helper methods
    public function getWeightedScore(): float
    {
        return $this->jenisNilai ? $this->jenisNilai->calculateWeightedScore($this->nilai) : 0;
    }

    public function isValidScore(): bool
    {
        return $this->jenisNilai ? $this->jenisNilai->validateNilai($this->nilai) : false;
    }

    public function getFormattedNilaiAttribute(): string
    {
        return number_format($this->nilai, 2);
    }

    public function getPercentageAttribute(): float
    {
        if (!$this->jenisNilai) {
            return 0;
        }

        $range = $this->jenisNilai->nilai_maksimum - $this->jenisNilai->nilai_minimum;
        if ($range == 0) {
            return 0;
        }

        return (($this->nilai - $this->jenisNilai->nilai_minimum) / $range) * 100;
    }

    public function getGradeAttribute(): string
    {
        $percentage = $this->percentage;
        
        return match(true) {
            $percentage >= 90 => 'A',
            $percentage >= 80 => 'B',
            $percentage >= 70 => 'C',
            $percentage >= 60 => 'D',
            default => 'E'
        };
    }
}
