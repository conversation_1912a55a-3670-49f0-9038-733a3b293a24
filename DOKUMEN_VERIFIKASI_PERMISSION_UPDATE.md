# Update Sistem Permission Verifikasi Dokumen

## Ringkasan <PERSON>bahan

Sistem verifikasi dokumen telah diperbarui untuk membatasi akses verifikasi hanya kepada **admin provinsi** (admin dan superadmin). Admin daerah sekarang hanya dapat mendaftarkan peserta dan tidak dapat melakukan verifikasi dokumen.

## Perubahan yang Dilakukan

### 1. Perbaikan Storage dan Folder Dokumen
- ✅ Membuat symbolic link storage dengan `php artisan storage:link`
- ✅ Memastikan folder `dokumen-peserta` dapat diakses di `storage/app/public/dokumen-peserta`
- ✅ File dokumen sekarang dapat diakses melalui URL `/storage/dokumen-peserta/`

### 2. Update User Model
**File**: `app/Models/User.php`

Menambahkan method baru untuk permission checking:
```php
/**
 * Check if user can verify documents
 * Only admin and superadmin can verify documents
 */
public function canVerifyDocuments(): bool
{
    return in_array($this->role, ['admin', 'superadmin']);
}

/**
 * Check if user is admin provinsi (admin or superadmin)
 */
public function isAdminProvinsi(): bool
{
    return in_array($this->role, ['admin', 'superadmin']);
}
```

### 3. Update DokumenVerifikasiController
**File**: `app/Http/Controllers/Admin/DokumenVerifikasiController.php`

Perubahan utama:
- Menambahkan method `checkVerificationPermission()` untuk validasi akses
- Menghapus logika permission untuk admin daerah
- Semua method (index, show, verify, download, bulkVerify) sekarang menggunakan permission check yang sama
- Hanya admin dan superadmin yang dapat mengakses fitur verifikasi dokumen

### 4. Update Routes
**File**: `routes/web.php`

- Menghapus route verifikasi dokumen dari grup admin daerah
- Route verifikasi dokumen sekarang hanya tersedia di grup admin (admin/superadmin)

### 5. Update UI Navigation
**File**: `resources/js/components/AppSidebar.vue`

- Menghapus menu "Verifikasi Dokumen" dari navigation admin daerah
- Menu verifikasi dokumen hanya muncul untuk admin dan superadmin

**File**: `FRONTEND_MENU_STRUCTURE.md`

- Update dokumentasi menu structure untuk mencerminkan perubahan permission

## Sistem Permission Baru

### Admin Provinsi (admin/superadmin)
- ✅ Dapat mengakses halaman verifikasi dokumen
- ✅ Dapat melihat semua dokumen dari seluruh wilayah
- ✅ Dapat melakukan approve/reject dokumen
- ✅ Dapat download dokumen untuk verifikasi
- ✅ Dapat melakukan bulk verification

### Admin Daerah
- ✅ Dapat mendaftarkan peserta di wilayahnya
- ✅ Dapat melihat status verifikasi dokumen (read-only)
- ✅ Dapat upload/delete dokumen peserta
- ❌ **TIDAK DAPAT** melakukan verifikasi dokumen
- ❌ **TIDAK DAPAT** mengakses halaman admin verifikasi dokumen

### Peserta
- ✅ Dapat upload dokumen sendiri
- ✅ Dapat melihat status verifikasi dokumen sendiri
- ❌ **TIDAK DAPAT** melakukan verifikasi dokumen

## Testing

Telah dibuat test untuk memastikan sistem permission bekerja dengan benar:

**File**: `tests/Feature/DokumenVerifikasiPermissionTest.php`

Test yang dilakukan:
- ✅ Method `canVerifyDocuments()` bekerja dengan benar untuk semua role
- ✅ Method `isAdminProvinsi()` bekerja dengan benar untuk semua role
- ✅ Admin dan superadmin dapat melakukan verifikasi
- ✅ Admin daerah dan peserta tidak dapat melakukan verifikasi

## Cara Menjalankan Test

```bash
php artisan test tests/Feature/DokumenVerifikasiPermissionTest.php
```

## Dampak Perubahan

### Positif
1. **Keamanan**: Hanya admin provinsi yang dapat melakukan verifikasi dokumen
2. **Konsistensi**: Sistem permission yang jelas dan terdokumentasi
3. **Skalabilitas**: Mudah untuk menambah role atau permission baru
4. **Storage**: Folder dokumen sekarang dapat diakses dengan benar

### Yang Perlu Diperhatikan
1. Admin daerah yang sebelumnya bisa verifikasi sekarang tidak bisa lagi
2. Perlu komunikasi kepada admin daerah tentang perubahan ini
3. Proses verifikasi sekarang terpusat di admin provinsi

## Rekomendasi Selanjutnya

1. **Notifikasi**: Buat sistem notifikasi ketika dokumen baru diupload
2. **Dashboard**: Tambahkan dashboard khusus untuk admin provinsi untuk monitoring dokumen
3. **Audit Log**: Tambahkan logging untuk tracking siapa yang melakukan verifikasi
4. **Bulk Operations**: Tingkatkan fitur bulk verification untuk efisiensi

## Rollback Plan

Jika perlu rollback, langkah yang harus dilakukan:
1. Kembalikan route verifikasi dokumen ke grup admin daerah
2. Kembalikan logika permission di DokumenVerifikasiController
3. Kembalikan menu verifikasi dokumen di AppSidebar untuk admin daerah
4. Update dokumentasi

---

**Tanggal Update**: 2025-01-14  
**Status**: ✅ Completed dan Tested  
**Next Review**: Setelah feedback dari user admin daerah
