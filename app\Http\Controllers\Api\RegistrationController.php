<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\Peserta;
use App\Models\Golongan;
use App\Services\RegistrationNumberService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class RegistrationController extends Controller
{
    /**
     * Display a listing of registrations
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        $query = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'mimbar',
            'pembayaran',
            'dokumenPeserta'
        ]);

        // Filter based on user role
        if ($user->role === 'peserta') {
            $query->whereHas('peserta', function ($q) use ($user) {
                $q->where('id_user', $user->id_user);
            });
        } elseif ($user->role === 'admin_daerah') {
            $query->whereHas('peserta', function ($q) use ($user) {
                $q->where('id_wilayah', $user->id_wilayah);
            });
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nomor_peserta', 'like', "%{$search}%")
                  ->orWhereHas('peserta', function ($q) use ($search) {
                      $q->where('nama_lengkap', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status_pendaftaran', $request->status);
        }

        $registrations = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $registrations
        ]);
    }

    /**
     * Store a newly created registration
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'id_peserta' => 'required|exists:peserta,id_peserta',
            'id_golongan' => 'required|exists:golongan,id_golongan',
            'id_mimbar' => 'nullable|exists:mimbar,id_mimbar',
        ]);

        $peserta = Peserta::findOrFail($validated['id_peserta']);
        $golongan = Golongan::findOrFail($validated['id_golongan']);

        // Check if user can register this participant
        $user = Auth::user();
        if ($user->role === 'peserta' && $peserta->id_user !== $user->id_user) {
            return response()->json([
                'success' => false,
                'message' => 'You can only register yourself'
            ], 403);
        }

        if ($user->role === 'admin_daerah' && $peserta->id_wilayah !== $user->id_wilayah) {
            return response()->json([
                'success' => false,
                'message' => 'You can only register participants from your region'
            ], 403);
        }

        // Check if participant already registered for this category
        $existingRegistration = Pendaftaran::where('id_peserta', $validated['id_peserta'])
            ->where('id_golongan', $validated['id_golongan'])
            ->whereNotIn('status_pendaftaran', ['cancelled', 'rejected'])
            ->first();

        if ($existingRegistration) {
            return response()->json([
                'success' => false,
                'message' => 'Participant already registered for this category'
            ], 422);
        }

        // Generate all registration numbers
        $tahun = date('Y');
        $numbers = RegistrationNumberService::generateAllNumbers($golongan, $tahun);

        $pendaftaran = Pendaftaran::create([
            'id_peserta' => $validated['id_peserta'],
            'id_golongan' => $validated['id_golongan'],
            'id_mimbar' => $validated['id_mimbar'],
            'nomor_pendaftaran' => $numbers['nomor_pendaftaran'],
            'nomor_peserta' => $numbers['nomor_peserta'],
            'nomor_urut' => $numbers['nomor_urut'],
            'tahun_pendaftaran' => $tahun,
            'tanggal_daftar' => now(),
            'status_pendaftaran' => 'draft',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Registration created successfully',
            'data' => $pendaftaran->load([
                'peserta.user',
                'peserta.wilayah',
                'golongan.cabangLomba',
                'mimbar'
            ])
        ], 201);
    }

    /**
     * Display the specified registration
     */
    public function show(string $id): JsonResponse
    {
        $pendaftaran = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'mimbar',
            'pembayaran',
            'dokumenPeserta',
            'nilaiPeserta.dewaHakim'
        ])->findOrFail($id);

        // Check access permissions
        $user = Auth::user();
        if ($user->role === 'peserta' && $pendaftaran->peserta->id_user !== $user->id_user) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if ($user->role === 'admin_daerah' && $pendaftaran->peserta->id_wilayah !== $user->id_wilayah) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $pendaftaran
        ]);
    }

    /**
     * Update the specified registration
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $pendaftaran = Pendaftaran::findOrFail($id);

        // Check if registration can be updated
        if (in_array($pendaftaran->status_pendaftaran, ['approved', 'verified', 'cancelled'])) {
            return response()->json([
                'success' => false,
                'message' => 'Registration cannot be updated in current status'
            ], 422);
        }

        $validated = $request->validate([
            'id_mimbar' => 'nullable|exists:mimbar,id_mimbar',
        ]);

        $pendaftaran->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Registration updated successfully',
            'data' => $pendaftaran->load([
                'peserta.user',
                'peserta.wilayah',
                'golongan.cabangLomba',
                'mimbar'
            ])
        ]);
    }

    /**
     * Submit registration for approval
     */
    public function submit(string $id): JsonResponse
    {
        $pendaftaran = Pendaftaran::findOrFail($id);

        if ($pendaftaran->status_pendaftaran !== 'draft') {
            return response()->json([
                'success' => false,
                'message' => 'Registration can only be submitted from draft status'
            ], 422);
        }

        $pendaftaran->update([
            'status_pendaftaran' => 'submitted',
            'tanggal_submit' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Registration submitted successfully',
            'data' => $pendaftaran->load([
                'peserta.user',
                'peserta.wilayah',
                'golongan.cabangLomba'
            ])
        ]);
    }

    /**
     * Cancel registration
     */
    public function cancel(string $id): JsonResponse
    {
        $pendaftaran = Pendaftaran::findOrFail($id);

        if (in_array($pendaftaran->status_pendaftaran, ['approved', 'verified'])) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot cancel approved or verified registration'
            ], 422);
        }

        $pendaftaran->update([
            'status_pendaftaran' => 'cancelled',
            'tanggal_cancel' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Registration cancelled successfully'
        ]);
    }


}
