<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Peserta;
use App\Models\Pendaftaran;
use App\Models\Wilayah;
use App\Models\Golongan;
use App\Models\CabangLomba;
use App\Models\DokumenPeserta;
use App\Models\DocumentType;
use App\Models\GolonganDocumentRequirement;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class VerificationWorkflowTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->createTestData();
    }

    private function createTestData(): void
    {
        // Use existing data or create simple test data
        $this->wilayah = Wilayah::first() ?: Wilayah::create([
            'nama_wilayah' => 'Test Wilayah',
            'kode_wilayah' => 'TW01'
        ]);

        $this->cabangLomba = CabangLomba::first() ?: CabangLomba::create([
            'nama_cabang' => 'Test Cabang',
            'deskripsi' => 'Test Description'
        ]);

        $this->golongan = Golongan::first() ?: Golongan::create([
            'id_cabang_lomba' => $this->cabangLomba->id_cabang_lomba,
            'nama_golongan' => 'Test Golongan',
            'deskripsi' => 'Test Description',
            'biaya_pendaftaran' => 100000
        ]);

        $this->documentType = DocumentType::first() ?: DocumentType::create([
            'name' => 'Test Document',
            'description' => 'Test Document Description'
        ]);

        GolonganDocumentRequirement::firstOrCreate([
            'id_golongan' => $this->golongan->id_golongan,
            'document_type_id' => $this->documentType->id_document_type,
        ], [
            'is_required' => true
        ]);

        // Create test users
        $this->adminDaerah = User::create([
            'username' => 'admin_daerah_test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin_daerah',
            'id_wilayah' => $this->wilayah->id_wilayah
        ]);

        $this->adminProvinsi = User::create([
            'username' => 'admin_provinsi_test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin'
        ]);

        $this->pesertaUser = User::create([
            'username' => 'peserta_test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'peserta'
        ]);
    }

    /** @test */
    public function self_registered_participant_requires_regional_verification()
    {
        // Create self-registered participant
        $peserta = Peserta::create([
            'id_user' => $this->pesertaUser->id_user,
            'nik' => '1234567890123456',
            'nama_lengkap' => 'Test Peserta',
            'tempat_lahir' => 'Test City',
            'tanggal_lahir' => '1990-01-01',
            'jenis_kelamin' => 'L',
            'alamat' => 'Test Address',
            'id_wilayah' => $this->wilayah->id_wilayah,
            'registration_type' => 'mandiri',
            'regional_verification_status' => 'pending',
            'documents_complete' => false
        ]);

        // Should not be visible to provincial admin
        $this->assertFalse($peserta->canBeSeenByProvincialAdmin());

        // Should be visible to regional admin
        $this->assertTrue($peserta->needsRegionalVerification());
    }

    /** @test */
    public function admin_registered_participant_bypasses_regional_verification()
    {
        // Create admin-registered participant
        $peserta = Peserta::factory()->create([
            'id_user' => $this->pesertaUser->id_user,
            'id_wilayah' => $this->wilayah->id_wilayah,
            'registration_type' => 'admin_daerah',
            'regional_verification_status' => 'verified',
            'documents_complete' => true
        ]);

        // Should be visible to provincial admin (bypasses regional verification)
        $this->assertTrue($peserta->canBeSeenByProvincialAdmin());

        // Should not need regional verification
        $this->assertFalse($peserta->needsRegionalVerification());
    }

    /** @test */
    public function regional_admin_can_verify_self_registered_participants()
    {
        $this->actingAs($this->adminDaerah);

        // Create self-registered participant
        $peserta = Peserta::factory()->create([
            'id_user' => $this->pesertaUser->id_user,
            'id_wilayah' => $this->wilayah->id_wilayah,
            'registration_type' => 'mandiri',
            'regional_verification_status' => 'pending',
            'documents_complete' => false
        ]);

        // Regional admin verifies participant
        $response = $this->post(route('admin-daerah.verification.verify', $peserta->id_peserta), [
            'action' => 'verify',
            'notes' => 'Data peserta valid'
        ]);

        $response->assertRedirect();

        $peserta->refresh();
        $this->assertEquals('verified', $peserta->regional_verification_status);
        $this->assertEquals($this->adminDaerah->id_user, $peserta->regional_verified_by);
        $this->assertNotNull($peserta->regional_verified_at);
    }

    /** @test */
    public function provincial_admin_only_sees_verified_participants_with_complete_documents()
    {
        $this->actingAs($this->adminProvinsi);

        // Create participants with different statuses
        $selfRegisteredPending = Peserta::factory()->create([
            'registration_type' => 'mandiri',
            'regional_verification_status' => 'pending',
            'documents_complete' => false
        ]);

        $selfRegisteredVerifiedIncomplete = Peserta::factory()->create([
            'registration_type' => 'mandiri',
            'regional_verification_status' => 'verified',
            'documents_complete' => false
        ]);

        $selfRegisteredVerifiedComplete = Peserta::factory()->create([
            'registration_type' => 'mandiri',
            'regional_verification_status' => 'verified',
            'documents_complete' => true
        ]);

        $adminRegisteredComplete = Peserta::factory()->create([
            'registration_type' => 'admin_daerah',
            'regional_verification_status' => 'verified',
            'documents_complete' => true
        ]);

        // Test visibility
        $visibleParticipants = Peserta::visibleToProvincialAdmin()->get();

        $this->assertFalse($visibleParticipants->contains($selfRegisteredPending));
        $this->assertFalse($visibleParticipants->contains($selfRegisteredVerifiedIncomplete));
        $this->assertTrue($visibleParticipants->contains($selfRegisteredVerifiedComplete));
        $this->assertTrue($visibleParticipants->contains($adminRegisteredComplete));
    }

    /** @test */
    public function document_completion_status_updates_automatically()
    {
        // Create participant with registration
        $peserta = Peserta::factory()->create([
            'registration_type' => 'mandiri',
            'regional_verification_status' => 'verified',
            'documents_complete' => false
        ]);

        $pendaftaran = Pendaftaran::factory()->create([
            'id_peserta' => $peserta->id_peserta,
            'id_golongan' => $this->golongan->id_golongan
        ]);

        // Initially no documents
        $this->assertFalse($peserta->documents_complete);

        // Upload required document
        DokumenPeserta::factory()->create([
            'id_pendaftaran' => $pendaftaran->id_pendaftaran,
            'document_type_id' => $this->documentType->id_document_type
        ]);

        // Document completion should be updated automatically via observer
        $peserta->refresh();
        $this->assertTrue($peserta->documents_complete);
    }

    /** @test */
    public function bulk_verification_works_correctly()
    {
        $this->actingAs($this->adminDaerah);

        // Create multiple self-registered participants
        $participants = Peserta::factory()->count(3)->create([
            'id_wilayah' => $this->wilayah->id_wilayah,
            'registration_type' => 'mandiri',
            'regional_verification_status' => 'pending'
        ]);

        $participantIds = $participants->pluck('id_peserta')->toArray();

        // Bulk verify
        $response = $this->post(route('admin-daerah.verification.bulk-verify'), [
            'participant_ids' => $participantIds,
            'action' => 'verify',
            'notes' => 'Bulk verification'
        ]);

        $response->assertRedirect();

        // Check all participants are verified
        foreach ($participants as $participant) {
            $participant->refresh();
            $this->assertEquals('verified', $participant->regional_verification_status);
        }
    }

    /** @test */
    public function payment_functionality_is_disabled()
    {
        $this->actingAs($this->pesertaUser);

        // Test payment routes are disabled
        $response = $this->get('/peserta/pembayaran');
        $response->assertOk();
        $response->assertInertia(fn ($page) =>
            $page->has('paymentDisabled')
                 ->where('paymentDisabled', true)
        );
    }

    /** @test */
    public function verification_workflow_maintains_backward_compatibility()
    {
        // Test that existing verification system still works
        $this->actingAs($this->adminProvinsi);

        $peserta = Peserta::factory()->create([
            'registration_type' => 'admin_daerah',
            'regional_verification_status' => 'verified',
            'documents_complete' => true
        ]);

        $pendaftaran = Pendaftaran::factory()->create([
            'id_peserta' => $peserta->id_peserta,
            'id_golongan' => $this->golongan->id_golongan,
            'status_pendaftaran' => 'regional_verified'
        ]);

        // Provincial admin can still verify
        $response = $this->post(route('admin.registration-verification.verify', $pendaftaran->id_pendaftaran), [
            'action' => 'approve',
            'notes' => 'Approved by provincial admin'
        ]);

        $response->assertRedirect();

        $pendaftaran->refresh();
        $this->assertEquals('verified', $pendaftaran->status_pendaftaran);
    }

    /** @test */
    public function default_verification_reasons_work()
    {
        $this->actingAs($this->adminProvinsi);

        $peserta = Peserta::factory()->create([
            'registration_type' => 'admin_daerah',
            'regional_verification_status' => 'verified',
            'documents_complete' => true
        ]);

        $pendaftaran = Pendaftaran::factory()->create([
            'id_peserta' => $peserta->id_peserta,
            'id_golongan' => $this->golongan->id_golongan,
            'status_pendaftaran' => 'regional_verified'
        ]);

        // Test default approval reason
        $response = $this->post(route('admin.registration-verification.verify', $pendaftaran->id_pendaftaran), [
            'action' => 'approve',
            'use_default_reason' => true
        ]);

        $response->assertRedirect();

        $pendaftaran->refresh();
        $this->assertEquals('Data valid dan lengkap', $pendaftaran->catatan_verifikasi);
    }
}
