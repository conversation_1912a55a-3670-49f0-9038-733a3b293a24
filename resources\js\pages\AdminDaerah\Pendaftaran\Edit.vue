<template>
  <AppLayout>
    <Head title="Edit Pendaftaran" />

    <div class="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Edit Pendaftaran Lomba</CardTitle>
          <CardDescription>
            Edit pendaftaran peserta untuk golongan lomba yang tersedia
          </CardDescription>
        </CardHeader>
        <CardContent class="space-y-6">
          <form @submit.prevent="submit">
            <!-- Peserta Info (Read-only) -->
            <div class="space-y-2">
              <Label>Peserta</Label>
              <div class="p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-4">
                  <Avatar class="h-12 w-12">
                    <AvatarFallback>
                      {{ pendaftaran.peserta.nama_lengkap.charAt(0) }}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 class="font-medium">{{ pendaftaran.peserta.nama_lengkap }}</h3>
                    <p class="text-sm text-gray-500">{{ pendaftaran.peserta.user?.email }}</p>
                    <p class="text-sm text-gray-500">{{ pendaftaran.peserta.nik }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Golongan Selection -->
            <div class="space-y-2">
              <Label for="id_golongan">Pilih Golongan *</Label>
              <Select v-model="form.id_golongan" required>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih golongan lomba" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem 
                    v-for="g in eligibleGolongan" 
                    :key="g.id_golongan" 
                    :value="g.id_golongan.toString()"
                  >
                    <div class="flex flex-col">
                      <span class="font-medium">{{ g.nama_golongan }}</span>
                      <span class="text-sm text-gray-500">{{ g.cabang_lomba.nama_cabang }}</span>
                      <span class="text-xs text-gray-400">
                        {{ g.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }} • 
                        {{ g.batas_umur_min }}-{{ g.batas_umur_max }} tahun • 
                        {{ formatCurrency(g.biaya_pendaftaran) }}
                      </span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <InputError :message="form.errors.id_golongan" />
            </div>

            <!-- Nomor Urut -->
            <div class="space-y-2">
              <Label for="nomor_urut">Nomor Urut (Opsional)</Label>
              <Input
                id="nomor_urut"
                v-model="form.nomor_urut"
                type="number"
                min="1"
                placeholder="Kosongkan untuk otomatis"
              />
              <p class="text-xs text-gray-500">
                Kosongkan untuk mendapatkan nomor urut otomatis
              </p>
              <InputError :message="form.errors.nomor_urut" />
            </div>

            <!-- Current Registration Info -->
            <div class="space-y-2">
              <Label>Informasi Pendaftaran Saat Ini</Label>
              <div class="p-4 bg-blue-50 rounded-lg space-y-2">
                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span class="font-medium">Nomor Pendaftaran:</span>
                    <span class="ml-2">{{ pendaftaran.nomor_pendaftaran }}</span>
                  </div>
                  <div>
                    <span class="font-medium">Nomor Peserta:</span>
                    <span class="ml-2">{{ pendaftaran.nomor_peserta }}</span>
                  </div>
                  <div>
                    <span class="font-medium">Nomor Urut:</span>
                    <span class="ml-2">{{ pendaftaran.nomor_urut }}</span>
                  </div>
                  <div>
                    <span class="font-medium">Status:</span>
                    <Badge :variant="getStatusVariant(pendaftaran.status_pendaftaran)" class="ml-2">
                      {{ getStatusLabel(pendaftaran.status_pendaftaran) }}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin-daerah.pendaftaran.index'))"
              >
                <Icon name="arrow-left" class="w-4 h-4 mr-2" />
                Kembali
              </Button>

              <div class="flex space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  @click="$inertia.visit(route('admin-daerah.pendaftaran.show', pendaftaran.id_pendaftaran))"
                >
                  <Icon name="eye" class="w-4 h-4 mr-2" />
                  Lihat Detail
                </Button>
                
                <Button 
                  type="submit" 
                  :disabled="form.processing"
                  class="min-w-[120px]"
                >
                  <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                  <Icon v-else name="save" class="w-4 h-4 mr-2" />
                  {{ form.processing ? 'Menyimpan...' : 'Simpan Perubahan' }}
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import Avatar from '@/components/ui/avatar/Avatar.vue'
import AvatarFallback from '@/components/ui/avatar/AvatarFallback.vue'
import InputError from '@/components/InputError.vue'
import Icon from '@/components/Icon.vue'

interface CabangLomba {
  nama_cabang: string
  deskripsi: string
}

interface Golongan {
  id_golongan: number
  nama_golongan: string
  jenis_kelamin: string
  batas_umur_min: number
  batas_umur_max: number
  kuota_max: number
  biaya_pendaftaran: number
  cabang_lomba: CabangLomba
}

interface User {
  email: string
}

interface Peserta {
  nama_lengkap: string
  nik: string
  jenis_kelamin: string
  tanggal_lahir: string
  user?: User
}

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  nomor_peserta: string
  nomor_urut: number
  status_pendaftaran: string
  id_golongan: number
  peserta: Peserta
  golongan: Golongan
}

const props = defineProps<{
  pendaftaran: Pendaftaran
  golongan: Golongan[]
}>()

const form = useForm({
  id_golongan: props.pendaftaran.id_golongan.toString(),
  nomor_urut: props.pendaftaran.nomor_urut || ''
})

// Filter eligible golongan based on participant
const eligibleGolongan = computed(() => {
  return props.golongan.filter(g => {
    // Check gender
    if (props.pendaftaran.peserta.jenis_kelamin !== g.jenis_kelamin) return false
    
    // Check age
    const age = calculateAge(props.pendaftaran.peserta.tanggal_lahir)
    if (age < g.batas_umur_min || age > g.batas_umur_max) return false
    
    return true
  })
})

function calculateAge(birthDate: string): number {
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  
  return age
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(amount)
}

function getStatusVariant(status: string) {
  const variants = {
    'draft': 'secondary',
    'submitted': 'default',
    'payment_pending': 'warning',
    'paid': 'success',
    'verified': 'success',
    'approved': 'success',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

function getStatusLabel(status: string) {
  const labels = {
    'draft': 'Draft',
    'submitted': 'Disubmit',
    'payment_pending': 'Menunggu Pembayaran',
    'paid': 'Sudah Bayar',
    'verified': 'Terverifikasi',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

function submit() {
  form.put(route('admin-daerah.pendaftaran.update', props.pendaftaran.id_pendaftaran))
}
</script>
