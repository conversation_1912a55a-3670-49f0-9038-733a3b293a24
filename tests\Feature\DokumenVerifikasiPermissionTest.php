<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DokumenVerifikasiPermissionTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function user_model_permission_methods_work_correctly()
    {
        $admin = User::create([
            'username' => 'admin_test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'nama_lengkap' => 'Admin Test',
            'status' => 'aktif'
        ]);

        $superadmin = User::create([
            'username' => 'superadmin_test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'superadmin',
            'nama_lengkap' => 'Super Admin Test',
            'status' => 'aktif'
        ]);

        $adminDaerah = User::create([
            'username' => 'admin_daerah_test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin_daerah',
            'nama_lengkap' => 'Admin Daerah Test',
            'status' => 'aktif'
        ]);

        $peserta = User::create([
            'username' => 'peserta_test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'peserta',
            'nama_lengkap' => 'Peserta Test',
            'status' => 'aktif'
        ]);

        // Test canVerifyDocuments method
        $this->assertTrue($admin->canVerifyDocuments());
        $this->assertTrue($superadmin->canVerifyDocuments());
        $this->assertFalse($adminDaerah->canVerifyDocuments());
        $this->assertFalse($peserta->canVerifyDocuments());

        // Test isAdminProvinsi method
        $this->assertTrue($admin->isAdminProvinsi());
        $this->assertTrue($superadmin->isAdminProvinsi());
        $this->assertFalse($adminDaerah->isAdminProvinsi());
        $this->assertFalse($peserta->isAdminProvinsi());
    }
}
