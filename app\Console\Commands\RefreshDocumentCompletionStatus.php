<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\DocumentCompletionService;

class RefreshDocumentCompletionStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mtq:refresh-document-status 
                            {--dry-run : Show what would be updated without making changes}
                            {--participant= : Refresh specific participant by ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refresh document completion status for all participants or a specific participant';

    protected DocumentCompletionService $documentCompletionService;

    public function __construct(DocumentCompletionService $documentCompletionService)
    {
        parent::__construct();
        $this->documentCompletionService = $documentCompletionService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('MTQ Document Completion Status Refresh');
        $this->info('=====================================');

        $dryRun = $this->option('dry-run');
        $participantId = $this->option('participant');

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        try {
            if ($participantId) {
                $this->refreshSingleParticipant($participantId, $dryRun);
            } else {
                $this->refreshAllParticipants($dryRun);
            }

            $this->info('Document completion status refresh completed successfully!');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Error refreshing document completion status: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Refresh document completion status for a single participant
     */
    private function refreshSingleParticipant(int $participantId, bool $dryRun): void
    {
        $participant = \App\Models\Peserta::with('pendaftaran.dokumenPeserta')->find($participantId);

        if (!$participant) {
            $this->error("Participant with ID {$participantId} not found");
            return;
        }

        $this->info("Processing participant: {$participant->nama_lengkap} (ID: {$participant->id_peserta})");

        $details = $this->documentCompletionService->getParticipantDocumentDetails($participant);
        
        $this->table(
            ['Registration', 'Required Docs', 'Uploaded Docs', 'Complete'],
            collect($details['registrations'])->map(function($regData) {
                return [
                    $regData['registration']->golongan->nama_golongan ?? 'N/A',
                    $regData['details']['required_count'],
                    $regData['details']['uploaded_count'],
                    $regData['details']['is_complete'] ? 'Yes' : 'No'
                ];
            })->toArray()
        );

        $this->info("Overall completion: {$details['completion_percentage']}%");
        $this->info("Current status: " . ($participant->documents_complete ? 'Complete' : 'Incomplete'));
        $this->info("New status: " . ($details['overall_complete'] ? 'Complete' : 'Incomplete'));

        if (!$dryRun) {
            $this->documentCompletionService->updateParticipantDocumentStatus($participant);
            $this->info("✓ Updated participant document status");
        }
    }

    /**
     * Refresh document completion status for all participants
     */
    private function refreshAllParticipants(bool $dryRun): void
    {
        $participants = \App\Models\Peserta::with('pendaftaran.dokumenPeserta')->get();
        
        $this->info("Found {$participants->count()} participants to process");

        $progressBar = $this->output->createProgressBar($participants->count());
        $progressBar->start();

        $updatedCount = 0;
        $errorCount = 0;

        foreach ($participants as $participant) {
            try {
                $oldStatus = $participant->documents_complete;
                
                if (!$dryRun) {
                    $this->documentCompletionService->updateParticipantDocumentStatus($participant);
                    $newStatus = $participant->fresh()->documents_complete;
                } else {
                    $details = $this->documentCompletionService->getParticipantDocumentDetails($participant);
                    $newStatus = $details['overall_complete'];
                }

                if ($oldStatus !== $newStatus) {
                    $updatedCount++;
                    
                    if ($this->output->isVerbose()) {
                        $this->newLine();
                        $this->info("Updated: {$participant->nama_lengkap} - {$oldStatus} → {$newStatus}");
                    }
                }

            } catch (\Exception $e) {
                $errorCount++;
                
                if ($this->output->isVerbose()) {
                    $this->newLine();
                    $this->error("Error processing {$participant->nama_lengkap}: {$e->getMessage()}");
                }
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        $this->info("Summary:");
        $this->info("- Total participants: {$participants->count()}");
        $this->info("- Updated: {$updatedCount}");
        $this->info("- Errors: {$errorCount}");
        $this->info("- Unchanged: " . ($participants->count() - $updatedCount - $errorCount));
    }
}
