<template>
  <Dialog :open="show" @update:open="$emit('update:show', $event)">
    <DialogContent class="sm:max-w-6xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>V<PERSON><PERSON><PERSON><PERSON></DialogTitle>
        <DialogDescription>
          {{ registration?.nomor_pendaftaran }} - {{ registration?.peserta?.nama_lengkap }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- Document Navigation -->
        <div v-if="documents.length > 1" class="flex items-center space-x-2 overflow-x-auto pb-2">
          <Button
            v-for="(doc, index) in documents"
            :key="doc.id_dokumen"
            @click="currentDocumentIndex = index"
            size="sm"
            :variant="currentDocumentIndex === index ? 'default' : 'outline'"
            class="flex-shrink-0"
          >
            <Icon name="file" class="w-4 h-4 mr-2" />
            {{ doc.document_type?.name || `Dokumen ${index + 1}` }}
          </Button>
        </div>

        <!-- Current Document -->
        <div v-if="currentDocument" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Document Preview -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <h3 class="font-medium">Preview Dokumen</h3>
              <div class="flex items-center space-x-2">
                <Badge :variant="getDocumentStatusVariant(currentDocument.status_verifikasi)">
                  {{ getDocumentStatusLabel(currentDocument.status_verifikasi) }}
                </Badge>
                <Button @click="downloadDocument(currentDocument)" size="sm" variant="outline">
                  <Icon name="download" class="w-4 h-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>

            <!-- Document Preview Area -->
            <div class="border rounded-lg p-4 bg-gray-50 min-h-[400px]">
              <!-- Image Preview -->
              <div v-if="isImageFile(currentDocument)" class="text-center">
                <img 
                  :src="getDocumentUrl(currentDocument)" 
                  :alt="currentDocument.nama_file"
                  class="max-w-full max-h-96 mx-auto rounded border shadow"
                  @error="imageLoadError = true"
                />
                <div v-if="imageLoadError" class="p-8 text-gray-500">
                  <Icon name="imageOff" class="w-12 h-12 mx-auto mb-2" />
                  <p>Tidak dapat memuat preview gambar</p>
                </div>
              </div>

              <!-- PDF Preview -->
              <div v-else-if="isPdfFile(currentDocument)" class="text-center">
                <iframe 
                  :src="getDocumentUrl(currentDocument)" 
                  class="w-full h-96 border rounded"
                  @error="pdfLoadError = true"
                ></iframe>
                <div v-if="pdfLoadError" class="p-8 text-gray-500">
                  <Icon name="fileX" class="w-12 h-12 mx-auto mb-2" />
                  <p>Tidak dapat memuat preview PDF</p>
                </div>
              </div>

              <!-- Other File Types -->
              <div v-else class="text-center p-8 text-gray-500">
                <Icon name="file" class="w-12 h-12 mx-auto mb-2" />
                <p>Preview tidak tersedia untuk jenis file ini</p>
                <Button @click="downloadDocument(currentDocument)" variant="outline" class="mt-2">
                  <Icon name="download" class="w-4 h-4 mr-2" />
                  Download File
                </Button>
              </div>
            </div>

            <!-- Document Information -->
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-600">Nama File:</span>
                <p class="font-medium">{{ currentDocument.nama_file }}</p>
              </div>
              <div>
                <span class="text-gray-600">Ukuran:</span>
                <p class="font-medium">{{ formatFileSize(currentDocument.ukuran_file) }}</p>
              </div>
              <div>
                <span class="text-gray-600">Jenis:</span>
                <p class="font-medium">{{ currentDocument.mime_type }}</p>
              </div>
              <div>
                <span class="text-gray-600">Upload:</span>
                <p class="font-medium">{{ formatDate(currentDocument.created_at) }}</p>
              </div>
            </div>
          </div>

          <!-- Verification Panel -->
          <div class="space-y-4">
            <h3 class="font-medium">Verifikasi Dokumen</h3>

            <!-- Current Status -->
            <div v-if="currentDocument.status_verifikasi !== 'pending'" class="p-4 bg-blue-50 rounded border border-blue-200">
              <h4 class="font-medium text-blue-800 mb-2">Status Verifikasi Saat Ini</h4>
              <div class="space-y-1 text-sm text-blue-700">
                <p><strong>Status:</strong> {{ getDocumentStatusLabel(currentDocument.status_verifikasi) }}</p>
                <p v-if="currentDocument.verified_by"><strong>Diverifikasi oleh:</strong> {{ currentDocument.verified_by.nama_lengkap }}</p>
                <p v-if="currentDocument.verified_at"><strong>Tanggal:</strong> {{ formatDate(currentDocument.verified_at) }}</p>
                <p v-if="currentDocument.catatan_verifikasi"><strong>Catatan:</strong> {{ currentDocument.catatan_verifikasi }}</p>
              </div>
            </div>

            <!-- Verification Form -->
            <form @submit.prevent="handleDocumentVerification" class="space-y-4">
              <!-- Action Selection -->
              <div class="space-y-2">
                <Label>Aksi Verifikasi</Label>
                <div class="grid grid-cols-2 gap-3">
                  <Button
                    type="button"
                    @click="documentForm.action = 'approved'"
                    :variant="documentForm.action === 'approved' ? 'default' : 'outline'"
                    class="justify-center"
                    :class="{
                      'bg-green-600 hover:bg-green-700 text-white': documentForm.action === 'approved',
                      'border-green-600 text-green-600 hover:bg-green-50': documentForm.action !== 'approved'
                    }"
                  >
                    <Icon name="check" class="w-4 h-4 mr-2" />
                    Setujui
                  </Button>
                  <Button
                    type="button"
                    @click="documentForm.action = 'rejected'"
                    :variant="documentForm.action === 'rejected' ? 'destructive' : 'outline'"
                    class="justify-center"
                    :class="{
                      'border-red-600 text-red-600 hover:bg-red-50': documentForm.action !== 'rejected'
                    }"
                  >
                    <Icon name="x" class="w-4 h-4 mr-2" />
                    Tolak
                  </Button>
                </div>
              </div>

              <!-- Quality Checklist (for approved documents) -->
              <div v-if="documentForm.action === 'approved'" class="space-y-3">
                <Label>Checklist Kualitas Dokumen</Label>
                <div class="space-y-2">
                  <div class="flex items-center space-x-2">
                    <Checkbox v-model="documentForm.quality_checks.readable" />
                    <Label class="text-sm">Dokumen dapat dibaca dengan jelas</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox v-model="documentForm.quality_checks.complete" />
                    <Label class="text-sm">Informasi dalam dokumen lengkap</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox v-model="documentForm.quality_checks.authentic" />
                    <Label class="text-sm">Dokumen terlihat asli/tidak dimanipulasi</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox v-model="documentForm.quality_checks.relevant" />
                    <Label class="text-sm">Dokumen sesuai dengan jenis yang diminta</Label>
                  </div>
                </div>
              </div>

              <!-- Rejection Reasons (for rejected documents) -->
              <div v-if="documentForm.action === 'rejected'" class="space-y-3">
                <Label>Alasan Penolakan</Label>
                <div class="space-y-2">
                  <div class="flex items-center space-x-2">
                    <Checkbox v-model="documentForm.rejection_reasons.unclear" />
                    <Label class="text-sm">Dokumen tidak jelas/buram</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox v-model="documentForm.rejection_reasons.incomplete" />
                    <Label class="text-sm">Informasi tidak lengkap</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox v-model="documentForm.rejection_reasons.wrong_type" />
                    <Label class="text-sm">Jenis dokumen tidak sesuai</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox v-model="documentForm.rejection_reasons.expired" />
                    <Label class="text-sm">Dokumen sudah kadaluarsa</Label>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Checkbox v-model="documentForm.rejection_reasons.suspicious" />
                    <Label class="text-sm">Dokumen terindikasi palsu/dimanipulasi</Label>
                  </div>
                </div>
              </div>

              <!-- Verification Notes -->
              <div class="space-y-2">
                <Label for="notes">
                  Catatan Verifikasi
                  <span v-if="documentForm.action === 'rejected'" class="text-red-500">*</span>
                </Label>
                <Textarea
                  id="notes"
                  v-model="documentForm.notes"
                  :placeholder="documentForm.action === 'approved' ? 'Tambahkan catatan persetujuan (opsional)' : 'Jelaskan alasan penolakan (wajib)'"
                  rows="3"
                  :required="documentForm.action === 'rejected'"
                />
              </div>

              <!-- Quick Rejection Reasons -->
              <div v-if="documentForm.action === 'rejected'" class="space-y-2">
                <Label>Alasan Umum (Klik untuk mengisi otomatis)</Label>
                <div class="grid grid-cols-1 gap-2">
                  <Button
                    v-for="reason in rejectionReasons"
                    :key="reason.value"
                    type="button"
                    @click="documentForm.notes = reason.text"
                    variant="outline"
                    size="sm"
                    class="justify-start text-left"
                  >
                    {{ reason.label }}
                  </Button>
                </div>
              </div>

              <!-- Submit Button -->
              <Button 
                type="submit" 
                :disabled="!documentForm.action || isSubmitting || (documentForm.action === 'rejected' && !documentForm.notes.trim())"
                class="w-full"
                :class="{
                  'bg-green-600 hover:bg-green-700': documentForm.action === 'approved',
                  'bg-red-600 hover:bg-red-700': documentForm.action === 'rejected'
                }"
              >
                <Icon 
                  v-if="isSubmitting" 
                  name="loader" 
                  class="w-4 h-4 mr-2 animate-spin" 
                />
                <Icon 
                  v-else-if="documentForm.action === 'approved'" 
                  name="check" 
                  class="w-4 h-4 mr-2" 
                />
                <Icon 
                  v-else-if="documentForm.action === 'rejected'" 
                  name="x" 
                  class="w-4 h-4 mr-2" 
                />
                {{ getSubmitButtonText() }}
              </Button>
            </form>
          </div>
        </div>

        <!-- No Documents -->
        <div v-else class="text-center py-12 text-gray-500">
          <Icon name="fileX" class="w-16 h-16 mx-auto mb-4 text-gray-300" />
          <p class="text-lg font-medium">Belum ada dokumen</p>
          <p class="text-sm">Peserta belum mengunggah dokumen untuk pendaftaran ini.</p>
        </div>
      </div>

      <!-- Footer Actions -->
      <DialogFooter>
        <Button 
          type="button" 
          variant="outline" 
          @click="$emit('update:show', false)"
        >
          Tutup
        </Button>
        <Button 
          v-if="documents.length > 1 && hasUnverifiedDocuments"
          @click="verifyAllDocuments"
          class="bg-blue-600 hover:bg-blue-700"
        >
          <Icon name="checkCircle" class="w-4 h-4 mr-2" />
          Verifikasi Semua
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  show: boolean
  registration: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'verify': [data: { registrationId: number; documentId: number; action: string; notes: string; metadata?: any }]
}>()

// Reactive data
const currentDocumentIndex = ref(0)
const isSubmitting = ref(false)
const imageLoadError = ref(false)
const pdfLoadError = ref(false)
const documentForm = ref({
  action: '',
  notes: '',
  quality_checks: {
    readable: false,
    complete: false,
    authentic: false,
    relevant: false
  },
  rejection_reasons: {
    unclear: false,
    incomplete: false,
    wrong_type: false,
    expired: false,
    suspicious: false
  }
})

const rejectionReasons = [
  {
    value: 'unclear',
    label: 'Dokumen tidak jelas',
    text: 'Dokumen yang diunggah tidak jelas atau tidak dapat dibaca dengan baik. Silakan unggah ulang dengan kualitas yang lebih baik.'
  },
  {
    value: 'incomplete',
    label: 'Informasi tidak lengkap',
    text: 'Informasi dalam dokumen tidak lengkap. Pastikan semua data yang diperlukan tercantum dengan jelas.'
  },
  {
    value: 'wrong_type',
    label: 'Jenis dokumen salah',
    text: 'Jenis dokumen yang diunggah tidak sesuai dengan yang diminta. Silakan unggah dokumen yang benar.'
  },
  {
    value: 'expired',
    label: 'Dokumen kadaluarsa',
    text: 'Dokumen yang diunggah sudah kadaluarsa. Silakan unggah dokumen yang masih berlaku.'
  },
  {
    value: 'suspicious',
    label: 'Dokumen mencurigakan',
    text: 'Dokumen terindikasi telah dimanipulasi atau tidak asli. Silakan unggah dokumen asli.'
  }
]

// Computed
const documents = computed(() => {
  return props.registration?.documents || []
})

const currentDocument = computed(() => {
  return documents.value[currentDocumentIndex.value] || null
})

const hasUnverifiedDocuments = computed(() => {
  return documents.value.some((doc: any) => doc.status_verifikasi === 'pending')
})

// Watch for modal close to reset form
watch(() => props.show, (newValue) => {
  if (!newValue) {
    currentDocumentIndex.value = 0
    resetForm()
  }
})

// Methods
const resetForm = () => {
  documentForm.value = {
    action: '',
    notes: '',
    quality_checks: {
      readable: false,
      complete: false,
      authentic: false,
      relevant: false
    },
    rejection_reasons: {
      unclear: false,
      incomplete: false,
      wrong_type: false,
      expired: false,
      suspicious: false
    }
  }
  imageLoadError.value = false
  pdfLoadError.value = false
}

const handleDocumentVerification = async () => {
  if (!documentForm.value.action || !currentDocument.value) return
  if (documentForm.value.action === 'rejected' && !documentForm.value.notes.trim()) return

  isSubmitting.value = true
  
  try {
    const metadata = {
      quality_checks: documentForm.value.action === 'approved' ? documentForm.value.quality_checks : null,
      rejection_reasons: documentForm.value.action === 'rejected' ? documentForm.value.rejection_reasons : null
    }
    
    emit('verify', {
      registrationId: props.registration.id_pendaftaran,
      documentId: currentDocument.value.id_dokumen,
      action: documentForm.value.action,
      notes: documentForm.value.notes,
      metadata
    })
    
    // Move to next unverified document or close modal
    const nextUnverifiedIndex = documents.value.findIndex((doc: any, index: number) => 
      index > currentDocumentIndex.value && doc.status_verifikasi === 'pending'
    )
    
    if (nextUnverifiedIndex !== -1) {
      currentDocumentIndex.value = nextUnverifiedIndex
      resetForm()
    } else {
      emit('update:show', false)
    }
  } finally {
    isSubmitting.value = false
  }
}

const verifyAllDocuments = () => {
  // This would open a bulk verification modal
  // For now, we'll just close this modal
  emit('update:show', false)
}

const downloadDocument = (document: any) => {
  window.open(`/admin/registration-verification/documents/${document.id_dokumen}/download`, '_blank')
}

const getDocumentUrl = (document: any) => {
  return `/storage/${document.path_file}`
}

const isImageFile = (document: any) => {
  return document.mime_type?.startsWith('image/')
}

const isPdfFile = (document: any) => {
  return document.mime_type === 'application/pdf'
}

const getDocumentStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'pending': 'outline',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getDocumentStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'pending': 'Pending',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getSubmitButtonText = () => {
  if (isSubmitting.value) {
    return 'Memproses...'
  }
  
  if (documentForm.value.action === 'approved') {
    return 'Setujui Dokumen'
  } else if (documentForm.value.action === 'rejected') {
    return 'Tolak Dokumen'
  }
  
  return 'Verifikasi Dokumen'
}
</script>
