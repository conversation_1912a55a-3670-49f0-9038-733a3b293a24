<script setup lang="ts">
import { ref } from 'vue'
import { useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import InputError from '@/components/InputError.vue'
import Icon from '@/components/Icon.vue'

interface CabangLomba {
    id_cabang: number
    nama_cabang: string
    deskripsi: string
}

interface Golongan {
    id_golongan: number
    nama_golongan: string
    jenis_kelamin: string
    batas_umur_min: number
    batas_umur_max: number
    kuota_max: number
    biaya_pendaftaran: number
    cabang_lomba: CabangLomba
}

interface Peserta {
    id_peserta: number
    nama_lengkap: string
    jenis_kelamin: string
    tanggal_lahir: string
}

const props = defineProps<{
    golongan: Golongan | null
    availableGolongan: Golongan[]
    peserta: Peserta
}>()

const form = useForm({
    id_golongan: props.golongan?.id_golongan || '',
    keterangan: ''
})

const selectedGolongan = ref<Golongan | null>(props.golongan)

function selectGolongan(golongan: Golongan) {
    selectedGolongan.value = golongan
    form.id_golongan = golongan.id_golongan
}

function submit() {
    form.post(route('peserta.pendaftaran.store'))
}

function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0
    }).format(amount)
}

function calculateAge(birthDate: string): number {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--
    }

    return age
}
</script>

<template>
    <AppLayout>
        <template #header>
            <div class="flex items-center space-x-4">
                <Button
                    as="link"
                    :href="route('peserta.pendaftaran.index')"
                    variant="ghost"
                    size="sm"
                >
                    <Icon name="arrow-left" class="w-4 h-4 mr-2" />
                    Kembali
                </Button>
                <Heading>Daftar Lomba Baru</Heading>
            </div>
        </template>

        <Head title="Daftar Lomba Baru" />

        <div class="space-y-6">
            <!-- Peserta Info -->
            <Card>
                <CardHeader>
                    <CardTitle>Data Peserta</CardTitle>
                    <CardDescription>Informasi peserta yang akan didaftarkan</CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Nama Lengkap</p>
                            <p class="text-lg">{{ peserta.nama_lengkap }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Jenis Kelamin</p>
                            <p class="text-lg">{{ peserta.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Usia</p>
                            <p class="text-lg">{{ calculateAge(peserta.tanggal_lahir) }} tahun</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Golongan Selection -->
            <Card v-if="!golongan">
                <CardHeader>
                    <CardTitle>Pilih Golongan</CardTitle>
                    <CardDescription>Pilih golongan lomba yang sesuai dengan kriteria Anda</CardDescription>
                </CardHeader>
                <CardContent>
                    <div v-if="availableGolongan.length === 0" class="text-center py-8">
                        <Icon name="alert-circle" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak Ada Golongan yang Tersedia</h3>
                        <p class="text-gray-600 mb-4">Tidak ada golongan lomba yang sesuai dengan profil Anda saat ini.</p>
                        <Button as-child>
                            <TextLink :href="route('competition.index')" variant="outline">
                                Lihat Semua Lomba
                            </TextLink>
                        </Button>
                    </div>

                    <div v-else class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <div
                            v-for="g in availableGolongan"
                            :key="g.id_golongan"
                            @click="selectGolongan(g)"
                            class="p-4 border rounded-lg cursor-pointer transition-all hover:border-blue-500 hover:shadow-md"
                            :class="selectedGolongan?.id_golongan === g.id_golongan ? 'border-blue-500 bg-blue-50' : ''"
                        >
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium">{{ g.nama_golongan }}</h4>
                                <Badge :class="g.jenis_kelamin === 'L' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800'">
                                    {{ g.jenis_kelamin === 'L' ? 'Putra' : 'Putri' }}
                                </Badge>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">{{ g.cabang_lomba.nama_cabang }}</p>
                            <div class="text-sm space-y-1">
                                <p>Usia: {{ g.batas_umur_min }} - {{ g.batas_umur_max }} tahun</p>
                                <p class="font-medium text-green-600">{{ formatCurrency(g.biaya_pendaftaran) }}</p>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Selected Golongan Info -->
            <Card v-if="selectedGolongan">
                <CardHeader>
                    <CardTitle>Golongan Terpilih</CardTitle>
                    <CardDescription>Detail golongan yang akan Anda ikuti</CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-lg font-semibold">{{ selectedGolongan.nama_golongan }}</h3>
                            <Badge :class="selectedGolongan.jenis_kelamin === 'L' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800'">
                                {{ selectedGolongan.jenis_kelamin === 'L' ? 'Putra' : 'Putri' }}
                            </Badge>
                        </div>
                        <p class="text-gray-700 mb-3">{{ selectedGolongan.cabang_lomba.nama_cabang }}</p>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <p class="font-medium">Batas Usia</p>
                                <p>{{ selectedGolongan.batas_umur_min }} - {{ selectedGolongan.batas_umur_max }} tahun</p>
                            </div>
                            <div>
                                <p class="font-medium">Kuota Maksimal</p>
                                <p>{{ selectedGolongan.kuota_max }} peserta</p>
                            </div>
                            <div>
                                <p class="font-medium">Biaya Pendaftaran</p>
                                <p class="text-lg font-bold text-green-600">{{ formatCurrency(selectedGolongan.biaya_pendaftaran) }}</p>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Registration Form -->
            <Card v-if="selectedGolongan">
                <CardHeader>
                    <CardTitle>Form Pendaftaran</CardTitle>
                    <CardDescription>Lengkapi informasi pendaftaran Anda</CardDescription>
                </CardHeader>
                <CardContent>
                    <form @submit.prevent="submit" class="space-y-6">
                        <div class="grid gap-2">
                            <Label for="keterangan">Keterangan (Opsional)</Label>
                            <Textarea
                                id="keterangan"
                                v-model="form.keterangan"
                                placeholder="Tambahkan keterangan atau catatan khusus untuk pendaftaran ini..."
                                rows="4"
                            />
                            <InputError :message="form.errors.keterangan" />
                        </div>

                        <Alert>
                            <Icon name="info" class="h-4 w-4" />
                            <AlertDescription>
                                Setelah mendaftar, Anda perlu melengkapi dokumen yang diperlukan dan melakukan pembayaran untuk menyelesaikan proses pendaftaran.
                            </AlertDescription>
                        </Alert>

                        <div class="flex justify-end space-x-4 pt-6 border-t">
                            <Button
                                as="link"
                                :href="route('peserta.pendaftaran.index')"
                                variant="outline"
                            >
                                Batal
                            </Button>
                            <Button
                                type="submit"
                                :disabled="form.processing || !selectedGolongan"
                            >
                                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                                {{ form.processing ? 'Mendaftar...' : 'Daftar Sekarang' }}
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
