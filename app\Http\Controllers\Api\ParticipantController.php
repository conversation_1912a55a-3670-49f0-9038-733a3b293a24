<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Peserta;
use App\Models\User;
use App\Rules\UniqueNikAcrossTables;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;


class ParticipantController extends Controller
{
    /**
     * Display a listing of participants
     */
    public function index(Request $request): JsonResponse
    {
        $query = Peserta::with(['user', 'wilayah', 'pendaftaran.golongan.cabangLomba']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($q) use ($search) {
                      $q->where('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by region
        if ($request->has('id_wilayah') && $request->id_wilayah) {
            $query->where('id_wilayah', $request->id_wilayah);
        }

        // Filter by gender
        if ($request->has('jenis_kelamin') && $request->jenis_kelamin !== 'all') {
            $query->where('jenis_kelamin', $request->jenis_kelamin);
        }

        $participants = $query->orderBy('nama_lengkap')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $participants
        ]);
    }

    /**
     * Store a newly created participant
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            // User data
            'username' => 'required|string|max:50|unique:users',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'nama_lengkap_user' => 'required|string|max:100',
            'no_telepon_user' => 'nullable|string|max:20',
            'id_wilayah' => 'required|exists:wilayah,id_wilayah',

            // Participant data
            'nama_lengkap' => 'required|string|max:100',
            'nik' => ['required', 'string', 'size:16', new UniqueNikAcrossTables()],
            'tempat_lahir' => 'required|string|max:50',
            'tanggal_lahir' => 'required|date',
            'jenis_kelamin' => 'required|in:L,P',
            'alamat' => 'required|string',
            'no_telepon' => 'nullable|string|max:20',
            'pekerjaan' => 'nullable|string|max:100',
            'nama_ayah' => 'nullable|string|max:100',
            'nama_ibu' => 'nullable|string|max:100',
        ]);

        // Create user first
        $user = User::create([
            'username' => $validated['username'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'nama_lengkap' => $validated['nama_lengkap_user'],
            'no_telepon' => $validated['no_telepon_user'],
            'id_wilayah' => $validated['id_wilayah'],
            'role' => 'peserta',
            'status' => 'aktif',
        ]);

        // Create participant
        $peserta = Peserta::create([
            'id_user' => $user->id_user,
            'nama_lengkap' => $validated['nama_lengkap'],
            'nik' => $validated['nik'],
            'tempat_lahir' => $validated['tempat_lahir'],
            'tanggal_lahir' => $validated['tanggal_lahir'],
            'jenis_kelamin' => $validated['jenis_kelamin'],
            'alamat' => $validated['alamat'],
            'no_telepon' => $validated['no_telepon'],
            'pekerjaan' => $validated['pekerjaan'],
            'nama_ayah' => $validated['nama_ayah'],
            'nama_ibu' => $validated['nama_ibu'],
            'id_wilayah' => $validated['id_wilayah'],
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Participant created successfully',
            'data' => $peserta->load(['user', 'wilayah'])
        ], 201);
    }

    /**
     * Display the specified participant
     */
    public function show(string $id): JsonResponse
    {
        $peserta = Peserta::with([
            'user',
            'wilayah',
            'pendaftaran.golongan.cabangLomba',
            'pendaftaran.pembayaran',
            'pendaftaran.dokumenPeserta'
        ])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $peserta
        ]);
    }

    /**
     * Update the specified participant
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $peserta = Peserta::findOrFail($id);

        $validated = $request->validate([
            'nama_lengkap' => 'required|string|max:100',
            'nik' => [
                'required',
                'string',
                'size:16',
                new UniqueNikAcrossTables('peserta', $peserta->id_peserta, 'id_peserta')
            ],
            'tempat_lahir' => 'required|string|max:50',
            'tanggal_lahir' => 'required|date',
            'jenis_kelamin' => 'required|in:L,P',
            'alamat' => 'required|string',
            'no_telepon' => 'nullable|string|max:20',
            'pekerjaan' => 'nullable|string|max:100',
            'nama_ayah' => 'nullable|string|max:100',
            'nama_ibu' => 'nullable|string|max:100',
            'id_wilayah' => 'required|exists:wilayah,id_wilayah',
        ]);

        $peserta->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Participant updated successfully',
            'data' => $peserta->load(['user', 'wilayah'])
        ]);
    }

    /**
     * Remove the specified participant
     */
    public function destroy(string $id): JsonResponse
    {
        $peserta = Peserta::findOrFail($id);

        // Check if participant has active registrations
        if ($peserta->pendaftaran()->whereIn('status_pendaftaran', ['approved', 'verified'])->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete participant with active registrations'
            ], 422);
        }

        $peserta->delete();

        return response()->json([
            'success' => true,
            'message' => 'Participant deleted successfully'
        ]);
    }
}
