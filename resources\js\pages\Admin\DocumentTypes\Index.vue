<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Manajemen Jenis Dokumen" />
    <Heading title="Manajemen Jenis Dokumen" />

    <div class="space-y-6">
      <!-- Filters and Search -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama jenis dokumen, kode..."
                @input="search"
              />
            </div>
            <div>
              <Label for="status">Status</Label>
              <Select v-model="filters.status" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="active">Aktif</SelectItem>
                  <SelectItem value="inactive">Non Aktif</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end">
              <Button @click="clearFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          Menampilkan {{ documentTypes.from }} - {{ documentTypes.to }} dari {{ documentTypes.total }} jenis dokumen
        </div>
        <Button @click="$inertia.visit(route('admin.document-types.create'))">
          <Icon name="plus" class="w-4 h-4 mr-2" />
          Tambah Jenis Dokumen
        </Button>
      </div>

      <!-- Document Types Table -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Jenis Dokumen
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Kode
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    File Types
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Max Size
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Required Default
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="docType in documentTypes.data" :key="docType.id_document_type" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ docType.name }}</div>
                      <div class="text-sm text-gray-500">{{ docType.description }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {{ docType.code }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-wrap gap-1">
                      <span 
                        v-for="fileType in docType.allowed_file_types" 
                        :key="fileType"
                        class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {{ fileType.toUpperCase() }}
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ formatFileSize(docType.max_file_size) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="docType.is_required_default ? 'default' : 'secondary'">
                      {{ docType.is_required_default ? 'Required' : 'Optional' }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="docType.is_active ? 'default' : 'destructive'">
                      {{ docType.is_active ? 'Aktif' : 'Non Aktif' }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-2">
                      <Button
                        @click="$inertia.visit(route('admin.document-types.show', docType.id_document_type))"
                        variant="ghost"
                        size="sm"
                      >
                        <Icon name="eye" class="w-4 h-4" />
                      </Button>
                      <Button
                        @click="$inertia.visit(route('admin.document-types.edit', docType.id_document_type))"
                        variant="ghost"
                        size="sm"
                      >
                        <Icon name="edit" class="w-4 h-4" />
                      </Button>
                      <Button
                        @click="toggleStatus(docType)"
                        variant="ghost"
                        size="sm"
                        :class="docType.is_active ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'"
                      >
                        <Icon :name="docType.is_active ? 'x' : 'check'" class="w-4 h-4" />
                      </Button>
                      <Button
                        @click="confirmDelete(docType)"
                        variant="ghost"
                        size="sm"
                        class="text-red-600 hover:text-red-700"
                      >
                        <Icon name="trash" class="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Menampilkan {{ documentTypes.from }} sampai {{ documentTypes.to }} dari {{ documentTypes.total }} hasil
        </div>
        <Pagination
          :current-page="documentTypes.current_page"
          :last-page="documentTypes.last_page"
          :links="documentTypes.links"
        />
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <AlertDialog v-model:open="deleteDialog.open">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Hapus Jenis Dokumen</AlertDialogTitle>
          <AlertDialogDescription>
            Apakah Anda yakin ingin menghapus jenis dokumen "{{ deleteDialog.docType?.name }}"?
            Tindakan ini tidak dapat dibatalkan.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Batal</AlertDialogCancel>
          <AlertDialogAction @click="deleteDocumentType" class="bg-red-600 hover:bg-red-700">
            Hapus
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </AppLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import { debounce } from 'lodash'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import Pagination from '@/components/Pagination.vue'
import Icon from '@/components/Icon.vue'

const props = defineProps({
  documentTypes: Object,
  filters: Object
})

const breadcrumbItems = [
  { label: 'Dashboard', href: route('admin.dashboard') },
  { label: 'Manajemen Jenis Dokumen', href: null }
]

const filters = reactive({
  search: props.filters?.search || '',
  status: props.filters?.status || 'all'
})

const deleteDialog = reactive({
  open: false,
  docType: null
})

const search = debounce(() => {
  router.get(route('admin.document-types.index'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const clearFilters = () => {
  filters.search = ''
  filters.status = 'all'
  search()
}

const formatFileSize = (sizeInKB) => {
  if (sizeInKB < 1024) {
    return `${sizeInKB} KB`
  }
  return `${(sizeInKB / 1024).toFixed(1)} MB`
}

const toggleStatus = (docType) => {
  router.post(route('admin.document-types.toggle-status', docType.id_document_type), {}, {
    preserveScroll: true
  })
}

const confirmDelete = (docType) => {
  deleteDialog.docType = docType
  deleteDialog.open = true
}

const deleteDocumentType = () => {
  router.delete(route('admin.document-types.destroy', deleteDialog.docType.id_document_type), {
    onSuccess: () => {
      deleteDialog.open = false
      deleteDialog.docType = null
    }
  })
}
</script>
