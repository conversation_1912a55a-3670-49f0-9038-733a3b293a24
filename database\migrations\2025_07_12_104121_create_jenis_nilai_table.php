<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jenis_nilai', function (Blueprint $table) {
            $table->id('id_jenis_nilai');
            $table->string('nama_jenis', 100);
            $table->string('kode_jenis', 20)->unique();
            $table->text('keterangan')->nullable();
            $table->decimal('bobot_nilai', 5, 2)->default(1.00);
            $table->decimal('nilai_minimum', 5, 2)->default(0);
            $table->decimal('nilai_maksimum', 5, 2)->default(100);
            $table->enum('status', ['aktif', 'non_aktif'])->default('aktif');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jenis_nilai');
    }
};
