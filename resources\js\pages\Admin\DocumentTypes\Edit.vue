<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Edit Jenis <PERSON>" />
    <Heading title="Edit Jenis <PERSON>" />

    <div class="max-w-4xl mx-auto">
      <Card>
        <CardContent class="p-6">
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label for="name" class="required">Nama Jenis Dokumen</Label>
                <Input
                  id="name"
                  v-model="form.name"
                  placeholder="e.g., Foto Peserta"
                  :class="{ 'border-red-500': form.errors.name }"
                />
                <InputError :message="form.errors.name" />
              </div>

              <div>
                <Label for="code" class="required">Kode</Label>
                <Input
                  id="code"
                  v-model="form.code"
                  placeholder="e.g., foto"
                  :class="{ 'border-red-500': form.errors.code }"
                />
                <InputError :message="form.errors.code" />
                <p class="text-sm text-gray-500 mt-1">Kode unik untuk sistem (huruf kecil, tanpa spasi)</p>
              </div>
            </div>

            <!-- Description -->
            <div>
              <Label for="description">Deskripsi</Label>
              <Textarea
                id="description"
                v-model="form.description"
                placeholder="Deskripsi jenis dokumen..."
                rows="3"
                :class="{ 'border-red-500': form.errors.description }"
              />
              <InputError :message="form.errors.description" />
            </div>

            <!-- File Settings -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label class="required">Jenis File yang Diizinkan</Label>
                <div class="grid grid-cols-2 gap-2 mt-2">
                  <div v-for="fileType in availableFileTypes" :key="fileType.value" class="flex items-center space-x-2">
                    <Checkbox
                      :id="fileType.value"
                      :checked="form.allowed_file_types.includes(fileType.value)"
                      @update:checked="toggleFileType(fileType.value)"
                    />
                    <Label :for="fileType.value" class="text-sm">{{ fileType.label }}</Label>
                  </div>
                </div>
                <InputError :message="form.errors.allowed_file_types" />
              </div>

              <div>
                <Label for="max_file_size" class="required">Ukuran File Maksimal (KB)</Label>
                <Input
                  id="max_file_size"
                  v-model.number="form.max_file_size"
                  type="number"
                  min="100"
                  max="10240"
                  placeholder="5120"
                  :class="{ 'border-red-500': form.errors.max_file_size }"
                />
                <InputError :message="form.errors.max_file_size" />
                <p class="text-sm text-gray-500 mt-1">{{ formatFileSize(form.max_file_size) }}</p>
              </div>
            </div>

            <!-- Settings -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <Label for="sort_order">Urutan Tampil</Label>
                <Input
                  id="sort_order"
                  v-model.number="form.sort_order"
                  type="number"
                  min="0"
                  placeholder="0"
                  :class="{ 'border-red-500': form.errors.sort_order }"
                />
                <InputError :message="form.errors.sort_order" />
              </div>

              <div class="flex items-center space-x-2">
                <Checkbox
                  id="is_required_default"
                  v-model:checked="form.is_required_default"
                />
                <Label for="is_required_default">Wajib secara default</Label>
              </div>

              <div class="flex items-center space-x-2">
                <Checkbox
                  id="is_active"
                  v-model:checked="form.is_active"
                />
                <Label for="is_active">Aktif</Label>
              </div>
            </div>

            <!-- Usage Information -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 class="text-sm font-medium text-blue-900 mb-2">Informasi Penggunaan</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                <div>
                  <span class="font-medium">Digunakan di {{ documentType.golongan_requirements?.length || 0 }} golongan</span>
                </div>
                <div>
                  <span class="font-medium">{{ documentType.dokumen_peserta?.length || 0 }} dokumen peserta</span>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-between pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.document-types.index'))"
              >
                <Icon name="arrow-left" class="w-4 h-4 mr-2" />
                Kembali
              </Button>

              <div class="flex items-center space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  @click="$inertia.visit(route('admin.document-types.show', documentType.id_document_type))"
                >
                  <Icon name="eye" class="w-4 h-4 mr-2" />
                  Lihat Detail
                </Button>

                <Button type="submit" :disabled="form.processing">
                  <Icon name="save" class="w-4 h-4 mr-2" />
                  {{ form.processing ? 'Menyimpan...' : 'Simpan Perubahan' }}
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup>
import { reactive } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import InputError from '@/components/InputError.vue'
import Icon from '@/components/Icon.vue'

const props = defineProps({
  documentType: Object
})

const breadcrumbItems = [
  { label: 'Dashboard', href: route('admin.dashboard') },
  { label: 'Manajemen Jenis Dokumen', href: route('admin.document-types.index') },
  { label: 'Edit Jenis Dokumen', href: null }
]

const availableFileTypes = [
  { value: 'jpg', label: 'JPG' },
  { value: 'jpeg', label: 'JPEG' },
  { value: 'png', label: 'PNG' },
  { value: 'pdf', label: 'PDF' },
  { value: 'doc', label: 'DOC' },
  { value: 'docx', label: 'DOCX' }
]

const form = useForm({
  name: props.documentType.name,
  code: props.documentType.code,
  description: props.documentType.description,
  allowed_file_types: props.documentType.allowed_file_types || [],
  max_file_size: props.documentType.max_file_size,
  is_required_default: props.documentType.is_required_default,
  is_active: props.documentType.is_active,
  sort_order: props.documentType.sort_order
})

const toggleFileType = (fileType) => {
  const index = form.allowed_file_types.indexOf(fileType)
  if (index > -1) {
    form.allowed_file_types.splice(index, 1)
  } else {
    form.allowed_file_types.push(fileType)
  }
}

const formatFileSize = (sizeInKB) => {
  if (!sizeInKB) return ''
  if (sizeInKB < 1024) {
    return `${sizeInKB} KB`
  }
  return `${(sizeInKB / 1024).toFixed(1)} MB`
}

const submit = () => {
  form.put(route('admin.document-types.update', props.documentType.id_document_type))
}
</script>

<style scoped>
.required::after {
  content: ' *';
  color: #ef4444;
}
</style>
