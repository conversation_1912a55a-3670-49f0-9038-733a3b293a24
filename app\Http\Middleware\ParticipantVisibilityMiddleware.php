<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class ParticipantVisibilityMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        // Apply visibility rules based on user role
        switch ($user->role) {
            case 'admin':
            case 'superadmin':
                // Provincial admin can only see participants that meet visibility criteria
                $this->applyProvincialAdminVisibility($request);
                break;
                
            case 'admin_daerah':
                // Regional admin can see all participants in their region
                $this->applyRegionalAdminVisibility($request, $user);
                break;
                
            case 'peserta':
                // Participants can only see their own data
                $this->applyParticipantVisibility($request, $user);
                break;
        }

        return $next($request);
    }

    /**
     * Apply visibility rules for provincial admin
     */
    private function applyProvincialAdminVisibility(Request $request): void
    {
        // Provincial admin can only see:
        // 1. Admin-registered participants with complete documents
        // 2. Self-registered participants that are regionally verified with complete documents
        
        $request->merge([
            '_visibility_constraint' => 'provincial_admin',
            '_visibility_rules' => [
                'admin_registered_complete' => true,
                'self_registered_verified_complete' => true
            ]
        ]);
    }

    /**
     * Apply visibility rules for regional admin
     */
    private function applyRegionalAdminVisibility(Request $request, $user): void
    {
        // Regional admin can see all participants in their region regardless of verification status
        
        $request->merge([
            '_visibility_constraint' => 'regional_admin',
            '_visibility_rules' => [
                'region_filter' => $user->id_wilayah,
                'all_statuses' => true
            ]
        ]);
    }

    /**
     * Apply visibility rules for participants
     */
    private function applyParticipantVisibility(Request $request, $user): void
    {
        // Participants can only see their own data
        
        $request->merge([
            '_visibility_constraint' => 'participant',
            '_visibility_rules' => [
                'user_filter' => $user->id_user,
                'own_data_only' => true
            ]
        ]);
    }
}
