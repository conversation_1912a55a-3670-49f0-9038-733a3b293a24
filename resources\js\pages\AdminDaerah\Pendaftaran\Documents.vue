<template>
  <AppLayout>
    <Head title="Kelola Dokumen Pendaftaran" />

    <div class="max-w-6xl mx-auto space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div>
          <Heading title="Kelola Dokumen Pendaftaran" />
          <p class="text-gray-600 mt-1">
            Kelola dokumen untuk pendaftaran {{ pendaftaran.nomor_pendaftaran }}
          </p>
        </div>
        <Button
          variant="outline"
          @click="$inertia.visit(route('admin-daerah.pendaftaran.show', pendaftaran.id_pendaftaran))"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Ke<PERSON><PERSON> ke Detail
        </Button>
      </div>

      <!-- Participant Info -->
      <Card>
        <CardHeader>
          <CardTitle>Informasi Peserta</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="flex items-center space-x-4">
            <Avatar class="h-16 w-16">
              <AvatarFallback class="text-lg">
                {{ pendaftaran.peserta.nama_lengkap.charAt(0) }}
              </AvatarFallback>
            </Avatar>
            <div class="flex-1">
              <h3 class="text-lg font-semibold">{{ pendaftaran.peserta.nama_lengkap }}</h3>
              <p class="text-gray-600">{{ pendaftaran.peserta.user?.email }}</p>
              <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                <span>NIK: {{ pendaftaran.peserta.nik }}</span>
                <span>•</span>
                <span>{{ pendaftaran.golongan.nama_golongan }}</span>
                <span>•</span>
                <span>{{ pendaftaran.golongan.cabang_lomba.nama_cabang }}</span>
              </div>
            </div>
            <Badge :variant="getStatusVariant(pendaftaran.status_pendaftaran)">
              {{ getStatusLabel(pendaftaran.status_pendaftaran) }}
            </Badge>
          </div>
        </CardContent>
      </Card>

      <!-- Upload New Document -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle>Upload Dokumen Baru</CardTitle>
            <Dialog v-model:open="showUploadDialog">
              <DialogTrigger as-child>
                <Button v-if="canEdit() && Object.keys(getAvailableDocumentTypes()).length > 0">
                  <Icon name="plus" class="w-4 h-4 mr-2" />
                  Upload Dokumen
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Upload Dokumen Baru</DialogTitle>
                  <DialogDescription>
                    Pilih jenis dokumen dan upload file yang diperlukan.
                  </DialogDescription>
                </DialogHeader>
                <form @submit.prevent="uploadDocument" class="space-y-4">
                  <div class="grid gap-2">
                    <Label for="jenis_dokumen">Jenis Dokumen</Label>
                    <select
                      id="jenis_dokumen"
                      v-model="uploadForm.jenis_dokumen"
                      @change="onDocumentTypeChange"
                      required
                      class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="all">Pilih jenis dokumen</option>
                      <option v-for="(label, key) in getAvailableDocumentTypes()" :key="key" :value="key">
                        {{ label }}
                      </option>
                    </select>
                    <InputError :message="uploadForm.errors.jenis_dokumen" />
                  </div>

                  <div class="grid gap-2">
                    <Label for="file">File</Label>
                    <Input
                      id="file"
                      type="file"
                      :accept="getFileAcceptTypes()"
                      @change="uploadForm.file = $event.target.files[0]"
                      required
                    />
                    <p class="text-xs text-gray-500">{{ getFileHelpText() }}</p>
                    <InputError :message="uploadForm.errors.file" />
                  </div>

                  <div class="grid gap-2">
                    <Label for="keterangan">Keterangan (Opsional)</Label>
                    <Textarea
                      id="keterangan"
                      v-model="uploadForm.keterangan"
                      placeholder="Tambahkan keterangan jika diperlukan..."
                      rows="3"
                    />
                    <InputError :message="uploadForm.errors.keterangan" />
                  </div>

                  <DialogFooter>
                    <Button type="button" variant="outline" @click="showUploadDialog = false">
                      Batal
                    </Button>
                    <Button type="submit" :disabled="uploadForm.processing">
                      <Icon v-if="uploadForm.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                      {{ uploadForm.processing ? 'Mengupload...' : 'Upload' }}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
          <CardDescription v-if="Object.keys(getAvailableDocumentTypes()).length === 0">
            Semua jenis dokumen sudah diupload.
          </CardDescription>
        </CardHeader>
      </Card>

      <!-- Document List -->
      <Card>
        <CardHeader>
          <CardTitle>Dokumen yang Diupload</CardTitle>
          <CardDescription>
            Daftar dokumen yang telah diupload untuk pendaftaran ini
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div v-if="pendaftaran.dokumen_peserta.length === 0" class="text-center py-8 text-gray-500">
            <Icon name="file-x" class="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>Belum ada dokumen yang diupload</p>
            <p class="text-sm">Klik tombol "Upload Dokumen" untuk menambahkan dokumen</p>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="doc in pendaftaran.dokumen_peserta"
              :key="doc.id_dokumen"
              class="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
            >
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <Icon
                    :name="getDocumentIcon(doc.mime_type)"
                    class="w-8 h-8 text-gray-400"
                  />
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-gray-900">
                    {{ getDocumentTypeLabel(doc.jenis_dokumen) }}
                  </h4>
                  <p class="text-sm text-gray-500">{{ doc.nama_file }}</p>
                  <div class="flex items-center space-x-4 mt-1 text-xs text-gray-400">
                    <span>{{ formatFileSize(doc.ukuran_file) }}</span>
                    <span>•</span>
                    <span>{{ formatDate(doc.created_at) }}</span>
                    <span v-if="doc.keterangan">•</span>
                    <span v-if="doc.keterangan">{{ doc.keterangan }}</span>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <Badge :variant="getVerificationVariant(doc.status_verifikasi)">
                    {{ getVerificationLabel(doc.status_verifikasi) }}
                  </Badge>
                  <Button variant="outline" size="sm" @click="downloadDocument(doc.path_file)">
                    <Icon name="download" class="w-4 h-4" />
                  </Button>
                  <Button
                    v-if="canEdit() && doc.status_verifikasi !== 'approved'"
                    variant="outline"
                    size="sm"
                    @click="deleteDocument(doc.id_dokumen)"
                    class="text-red-600 hover:text-red-700"
                  >
                    <Icon name="trash" class="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Head, useForm, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import Avatar from '@/components/ui/avatar/Avatar.vue'
import AvatarFallback from '@/components/ui/avatar/AvatarFallback.vue'
import InputError from '@/components/InputError.vue'
import Icon from '@/components/Icon.vue'

interface User {
  email: string
}

interface CabangLomba {
  nama_cabang: string
}

interface Golongan {
  nama_golongan: string
  cabang_lomba: CabangLomba
}

interface Peserta {
  nama_lengkap: string
  nik: string
  user?: User
}

interface DokumenPeserta {
  id_dokumen: number
  jenis_dokumen: string
  nama_file: string
  path_file: string
  ukuran_file: number
  mime_type: string
  status_verifikasi: string
  catatan_verifikasi: string | null
  verified_at: string | null
  keterangan: string | null
  created_at: string
}

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  status_pendaftaran: string
  peserta: Peserta
  golongan: Golongan
  dokumen_peserta: DokumenPeserta[]
}

interface DocumentRequirement {
  id: number
  code: string
  name: string
  description: string
  is_required: boolean
  allowed_file_types: string[]
  max_file_size: number
}

const props = defineProps<{
  pendaftaran: Pendaftaran
  requiredDocuments: Record<string, string>
  documentRequirements?: DocumentRequirement[]
}>()

const showUploadDialog = ref(false)

const uploadForm = useForm({
  jenis_dokumen: '',
  document_type_id: null as number | null,
  file: null as File | null,
  keterangan: ''
})

function uploadDocument() {
  uploadForm.post(route('admin-daerah.pendaftaran.upload-document', props.pendaftaran.id_pendaftaran), {
    onSuccess: () => {
      uploadForm.reset()
      showUploadDialog.value = false
    }
  })
}

function deleteDocument(documentId: number) {
  if (confirm('Yakin ingin menghapus dokumen ini?')) {
    router.delete(route('admin-daerah.pendaftaran.delete-document', [props.pendaftaran.id_pendaftaran, documentId]))
  }
}

function downloadDocument(filePath: string) {
  window.open(`/storage/${filePath}`, '_blank')
}

function canEdit(): boolean {
  return ['draft', 'submitted', 'payment_pending'].includes(props.pendaftaran.status_pendaftaran)
}

function onDocumentTypeChange() {
  // Set document_type_id based on selected jenis_dokumen
  if (props.documentRequirements && uploadForm.jenis_dokumen) {
    const requirement = props.documentRequirements.find((req: DocumentRequirement) => req.code === uploadForm.jenis_dokumen)
    uploadForm.document_type_id = requirement ? requirement.id : null
  }
}

function getFileAcceptTypes(): string {
  if (props.documentRequirements && uploadForm.jenis_dokumen) {
    const requirement = props.documentRequirements.find((req: DocumentRequirement) => req.code === uploadForm.jenis_dokumen)
    if (requirement) {
      return requirement.allowed_file_types.map(type => `.${type}`).join(',')
    }
  }
  return '.jpg,.jpeg,.png,.pdf' // Default fallback
}

function getFileHelpText(): string {
  if (props.documentRequirements && uploadForm.jenis_dokumen) {
    const requirement = props.documentRequirements.find((req: DocumentRequirement) => req.code === uploadForm.jenis_dokumen)
    if (requirement) {
      const types = requirement.allowed_file_types.join(', ').toUpperCase()
      const maxSize = requirement.max_file_size < 1024
        ? `${requirement.max_file_size} KB`
        : `${(requirement.max_file_size / 1024).toFixed(1)} MB`
      return `Format: ${types}. Maksimal ${maxSize}.`
    }
  }
  return 'Format: JPG, PNG, PDF. Maksimal 5MB.' // Default fallback
}

function getAvailableDocumentTypes() {
  // Use dynamic document requirements if available, otherwise fallback to hardcoded
  if (props.documentRequirements && props.documentRequirements.length > 0) {
    const uploaded = props.pendaftaran.dokumen_peserta.map(doc => doc.jenis_dokumen)
    const available: Record<string, string> = {}

    for (const requirement of props.documentRequirements) {
      if (!uploaded.includes(requirement.code)) {
        available[requirement.code] = requirement.name
      }
    }

    return available
  } else {
    // Fallback to hardcoded document types
    const uploaded = props.pendaftaran.dokumen_peserta.map(doc => doc.jenis_dokumen)
    const available: Record<string, string> = {}

    for (const [key, label] of Object.entries(props.requiredDocuments)) {
      if (!uploaded.includes(key)) {
        available[key] = label
      }
    }

    return available
  }
}

function getDocumentTypeLabel(type: string): string {
  return props.requiredDocuments[type] || type
}

function getDocumentIcon(mimeType: string): string {
  if (mimeType.includes('pdf')) return 'file-text'
  if (mimeType.includes('image')) return 'image'
  return 'file'
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function formatDate(date: string): string {
  return new Date(date).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function getStatusVariant(status: string) {
  const variants: Record<string, string> = {
    'draft': 'secondary',
    'submitted': 'default',
    'payment_pending': 'warning',
    'paid': 'success',
    'verified': 'success',
    'approved': 'success',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

function getStatusLabel(status: string) {
  const labels: Record<string, string> = {
    'draft': 'Draft',
    'submitted': 'Disubmit',
    'payment_pending': 'Menunggu Pembayaran',
    'paid': 'Sudah Bayar',
    'verified': 'Terverifikasi',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

function getVerificationVariant(status: string) {
  const variants: Record<string, string> = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

function getVerificationLabel(status: string) {
  const labels: Record<string, string> = {
    'pending': 'Menunggu Verifikasi',
    'approved': 'Diverifikasi',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}
</script>
