<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Manajemen Jenis Verifikasi" />
    <Heading title="Manajemen Jenis Verifikasi" />

    <div class="space-y-6">
      <!-- Filters and Search -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama jenis verifikasi, kode..."
                @input="search"
              />
            </div>
            <div>
              <Label for="status">Status</Label>
              <Select v-model="filters.status" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="active">Aktif</SelectItem>
                  <SelectItem value="inactive">Non Aktif</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end">
              <Button @click="clearFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          Menampilkan {{ verificationTypes.from }} - {{ verificationTypes.to }} dari {{ verificationTypes.total }} jenis verifikasi
        </div>
        <Button @click="$inertia.visit(route('admin.verification-types.create'))">
          <Icon name="plus" class="w-4 h-4 mr-2" />
          Tambah Jenis Verifikasi
        </Button>
      </div>

      <!-- Verification Types Table -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Jenis Verifikasi
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Kode
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Required
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Verifikasi
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="verType in verificationTypes.data" :key="verType.id_verification_type" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ verType.name }}</div>
                      <div class="text-sm text-gray-500">{{ verType.description }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {{ verType.code }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="verType.is_required ? 'destructive' : 'secondary'">
                      {{ verType.is_required ? 'Wajib' : 'Opsional' }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="verType.is_active ? 'default' : 'destructive'">
                      {{ verType.is_active ? 'Aktif' : 'Non Aktif' }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ verType.participant_verifications_count || 0 }} peserta
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-2">
                      <Button
                        @click="$inertia.visit(route('admin.verification-types.show', verType.id_verification_type))"
                        variant="ghost"
                        size="sm"
                      >
                        <Icon name="eye" class="w-4 h-4" />
                      </Button>
                      <Button
                        @click="$inertia.visit(route('admin.verification-types.edit', verType.id_verification_type))"
                        variant="ghost"
                        size="sm"
                      >
                        <Icon name="edit" class="w-4 h-4" />
                      </Button>
                      <Button
                        @click="toggleStatus(verType)"
                        variant="ghost"
                        size="sm"
                        :class="verType.is_active ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'"
                      >
                        <Icon :name="verType.is_active ? 'x' : 'check'" class="w-4 h-4" />
                      </Button>
                      <Button
                        @click="confirmDelete(verType)"
                        variant="ghost"
                        size="sm"
                        class="text-red-600 hover:text-red-700"
                      >
                        <Icon name="trash" class="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Menampilkan {{ verificationTypes.from }} sampai {{ verificationTypes.to }} dari {{ verificationTypes.total }} hasil
        </div>
        <Pagination
          :current-page="verificationTypes.current_page"
          :last-page="verificationTypes.last_page"
          :links="verificationTypes.links"
        />
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <AlertDialog v-model:open="deleteDialog.open">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Hapus Jenis Verifikasi</AlertDialogTitle>
          <AlertDialogDescription>
            Apakah Anda yakin ingin menghapus jenis verifikasi "{{ deleteDialog.verType?.name }}"?
            Tindakan ini tidak dapat dibatalkan.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Batal</AlertDialogCancel>
          <AlertDialogAction @click="deleteVerificationType" class="bg-red-600 hover:bg-red-700">
            Hapus
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </AppLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import { debounce } from 'lodash'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import Pagination from '@/components/Pagination.vue'
import Icon from '@/components/Icon.vue'

const props = defineProps({
  verificationTypes: Object,
  filters: Object
})

const breadcrumbItems = [
  { label: 'Dashboard', href: route('admin.dashboard') },
  { label: 'Manajemen Jenis Verifikasi', href: null }
]

const filters = reactive({
  search: props.filters?.search || '',
  status: props.filters?.status || 'all'
})

const deleteDialog = reactive({
  open: false,
  verType: null
})

const search = debounce(() => {
  router.get(route('admin.verification-types.index'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const clearFilters = () => {
  filters.search = ''
  filters.status = 'all'
  search()
}

const toggleStatus = (verType) => {
  router.post(route('admin.verification-types.toggle-status', verType.id_verification_type), {}, {
    preserveScroll: true
  })
}

const confirmDelete = (verType) => {
  deleteDialog.verType = verType
  deleteDialog.open = true
}

const deleteVerificationType = () => {
  router.delete(route('admin.verification-types.destroy', deleteDialog.verType.id_verification_type), {
    onSuccess: () => {
      deleteDialog.open = false
      deleteDialog.verType = null
    }
  })
}
</script>
