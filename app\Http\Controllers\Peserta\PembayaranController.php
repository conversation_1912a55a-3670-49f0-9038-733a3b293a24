<?php

namespace App\Http\Controllers\Peserta;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PembayaranController extends Controller
{
    public function index()
    {
        return Inertia::render('Peserta/Pembayaran/Index', [
            'message' => 'Pendaftaran MTQ saat ini GRATIS! Tidak ada biaya yang perlu dibayar.',
            'paymentDisabled' => true,
            'disabledMessage' => 'Fitur pembayaran sementara dinonaktifkan. Pendaftaran saat ini gratis untuk semua peserta.'
        ]);
    }

    public function show($id)
    {
        return Inertia::render('Peserta/Pembayaran/Show', [
            'paymentDisabled' => true,
            'message' => 'Fitur pembayaran sementara dinonaktifkan. Pendaftaran saat ini gratis untuk semua peserta.'
        ]);
    }

    public function store(Request $request)
    {
        return back()->with('error', 'Fitur pembayaran sementara dinonaktifkan. Pendaftaran saat ini gratis.');
    }

    public function pay(Request $request, $id)
    {
        return back()->with('error', 'Fitur pembayaran sementara dinonaktifkan. Pendaftaran saat ini gratis.');
    }

    public function uploadProof(Request $request, $id)
    {
        return back()->with('error', 'Fitur pembayaran sementara dinonaktifkan. Pendaftaran saat ini gratis.');
    }
}
