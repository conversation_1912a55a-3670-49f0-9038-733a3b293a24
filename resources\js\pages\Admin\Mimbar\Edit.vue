<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Edit Mimbar" />
    <Heading :title="`Edit Mimbar: ${mimbar.nama_mimbar}`" />

    <div class="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Edit Informasi Mimbar</CardTitle>
          <CardDescription>
            Perbarui informasi mimbar di bawah ini
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form @submit.prevent="submit" class="space-y-6">
            <!-- Basic Information -->
            <div class="space-y-4">
              <h3 class="text-lg font-medium">Informasi Dasar</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label for="kode_mimbar">Kode Mimbar *</Label>
                  <Input
                    id="kode_mimbar"
                    v-model="form.kode_mimbar"
                    type="text"
                    required
                    placeholder="Contoh: A, B, M001"
                    :class="{ 'border-red-500': form.errors.kode_mimbar }"
                  />
                  <p v-if="form.errors.kode_mimbar" class="text-sm text-red-600 mt-1">
                    {{ form.errors.kode_mimbar }}
                  </p>
                </div>

                <div>
                  <Label for="status">Status *</Label>
                  <Select v-model="form.status" required>
                    <SelectTrigger :class="{ 'border-red-500': form.errors.status }">
                      <SelectValue placeholder="Pilih Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aktif">Aktif</SelectItem>
                      <SelectItem value="non_aktif">Non Aktif</SelectItem>
                    </SelectContent>
                  </Select>
                  <p v-if="form.errors.status" class="text-sm text-red-600 mt-1">
                    {{ form.errors.status }}
                  </p>
                </div>
              </div>

              <div>
                <Label for="nama_mimbar">Nama Mimbar *</Label>
                <Input
                  id="nama_mimbar"
                  v-model="form.nama_mimbar"
                  type="text"
                  required
                  placeholder="Contoh: Mimbar Utama, Aula Serbaguna"
                  :class="{ 'border-red-500': form.errors.nama_mimbar }"
                />
                <p v-if="form.errors.nama_mimbar" class="text-sm text-red-600 mt-1">
                  {{ form.errors.nama_mimbar }}
                </p>
              </div>

              <div>
                <Label for="kapasitas">Kapasitas *</Label>
                <Input
                  id="kapasitas"
                  v-model="form.kapasitas"
                  type="number"
                  required
                  min="1"
                  placeholder="Contoh: 100"
                  :class="{ 'border-red-500': form.errors.kapasitas }"
                />
                <p v-if="form.errors.kapasitas" class="text-sm text-red-600 mt-1">
                  {{ form.errors.kapasitas }}
                </p>
                <p class="text-sm text-gray-500 mt-1">
                  Jumlah maksimum peserta yang dapat menggunakan mimbar ini
                </p>
              </div>

              <div>
                <Label for="keterangan">Keterangan</Label>
                <textarea
                  id="keterangan"
                  v-model="form.keterangan"
                  rows="4"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Deskripsi mimbar, fasilitas yang tersedia, lokasi, dll..."
                  :class="{ 'border-red-500': form.errors.keterangan }"
                ></textarea>
                <p v-if="form.errors.keterangan" class="text-sm text-red-600 mt-1">
                  {{ form.errors.keterangan }}
                </p>
              </div>
            </div>

            <!-- Current Info -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <h4 class="font-medium text-gray-900 mb-2">Informasi Saat Ini</h4>
              <div class="text-sm text-gray-600 space-y-1">
                <p><strong>Dibuat:</strong> {{ formatDate(mimbar.created_at) }}</p>
                <p><strong>Diperbarui:</strong> {{ formatDate(mimbar.updated_at) }}</p>
                <p><strong>Jumlah Pendaftaran:</strong> {{ mimbar.pendaftaran?.length || 0 }} pendaftaran</p>
                <p><strong>Penggunaan:</strong> {{ getUsagePercentage() }}% dari kapasitas</p>
              </div>
            </div>

            <!-- Warning for changes -->
            <div v-if="hasPendaftaran" class="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
              <div class="flex">
                <Icon name="alert-triangle" class="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
                <div>
                  <h4 class="font-medium text-yellow-800">Perhatian</h4>
                  <p class="text-sm text-yellow-700 mt-1">
                    Mimbar ini memiliki {{ mimbar.pendaftaran?.length || 0 }} pendaftaran. 
                    Perubahan kapasitas atau status dapat mempengaruhi peserta yang sudah terdaftar.
                  </p>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin.mimbar.index'))"
              >
                Batal
              </Button>
              <Button type="submit" :disabled="form.processing">
                <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                Simpan Perubahan
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'
import { type BreadcrumbItem } from '@/types'

interface Mimbar {
  id_mimbar: number
  kode_mimbar: string
  nama_mimbar: string
  keterangan?: string
  kapasitas: number
  status: string
  created_at: string
  updated_at: string
  pendaftaran?: any[]
}

const props = defineProps<{
  mimbar: Mimbar
}>()

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Mimbar', href: '/admin/mimbar' },
  { title: 'Edit Mimbar', href: `/admin/mimbar/${props.mimbar.id_mimbar}/edit` }
]

const form = useForm({
  kode_mimbar: props.mimbar.kode_mimbar,
  nama_mimbar: props.mimbar.nama_mimbar,
  keterangan: props.mimbar.keterangan || '',
  kapasitas: props.mimbar.kapasitas.toString(),
  status: props.mimbar.status
})

const hasPendaftaran = computed(() => {
  return props.mimbar.pendaftaran && props.mimbar.pendaftaran.length > 0
})

const getUsagePercentage = () => {
  if (props.mimbar.kapasitas === 0) return 0
  const usage = props.mimbar.pendaftaran?.length || 0
  return Math.round((usage / props.mimbar.kapasitas) * 100)
}

const submit = () => {
  form.put(route('admin.mimbar.update', props.mimbar.id_mimbar), {
    onSuccess: () => {
      // Redirect handled by controller
    }
  })
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
