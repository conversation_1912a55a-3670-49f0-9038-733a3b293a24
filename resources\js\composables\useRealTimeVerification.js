import { ref, onMounted, onUnmounted } from 'vue'
import { router } from '@inertiajs/vue3'

export function useRealTimeVerification(verificationType = null, searchFilters = {}) {
  const stats = ref({
    total_participants: 0,
    pending_verification: 0,
    verified_count: 0,
    rejected_count: 0
  })
  
  const isLoading = ref(false)
  const error = ref(null)
  const lastUpdated = ref(null)
  
  let pollInterval = null
  const POLL_INTERVAL = 5000 // 5 seconds

  const fetchStats = async () => {
    if (isLoading.value) return
    
    try {
      isLoading.value = true
      error.value = null
      
      const params = new URLSearchParams()
      
      if (verificationType?.id_verification_type) {
        params.append('verification_type_id', verificationType.id_verification_type)
      }
      
      if (searchFilters.search) {
        params.append('search', searchFilters.search)
      }
      
      const response = await fetch(
        route('admin.participant-verifications.stats') + '?' + params.toString(),
        {
          headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
          }
        }
      )
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.success) {
        stats.value = data.data
        lastUpdated.value = new Date(data.timestamp)
      } else {
        throw new Error(data.message || 'Failed to fetch stats')
      }
      
    } catch (err) {
      error.value = err.message
      console.error('Error fetching verification stats:', err)
    } finally {
      isLoading.value = false
    }
  }

  const startPolling = () => {
    if (pollInterval) return
    
    // Initial fetch
    fetchStats()
    
    // Set up polling
    pollInterval = setInterval(fetchStats, POLL_INTERVAL)
  }

  const stopPolling = () => {
    if (pollInterval) {
      clearInterval(pollInterval)
      pollInterval = null
    }
  }

  const refreshStats = () => {
    fetchStats()
  }

  // Auto-start polling when component mounts
  onMounted(() => {
    startPolling()
  })

  // Clean up when component unmounts
  onUnmounted(() => {
    stopPolling()
  })

  return {
    stats,
    isLoading,
    error,
    lastUpdated,
    refreshStats,
    startPolling,
    stopPolling
  }
}

export function useOptimisticUpdates() {
  const pendingOperations = ref(new Map())
  
  const addPendingOperation = (participantId, operation) => {
    pendingOperations.value.set(participantId, {
      operation,
      timestamp: Date.now()
    })
  }
  
  const removePendingOperation = (participantId) => {
    pendingOperations.value.delete(participantId)
  }
  
  const clearPendingOperations = () => {
    pendingOperations.value.clear()
  }
  
  const isPending = (participantId) => {
    return pendingOperations.value.has(participantId)
  }
  
  const getPendingOperation = (participantId) => {
    return pendingOperations.value.get(participantId)
  }
  
  return {
    pendingOperations,
    addPendingOperation,
    removePendingOperation,
    clearPendingOperations,
    isPending,
    getPendingOperation
  }
}
