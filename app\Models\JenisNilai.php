<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class JenisNilai extends Model
{
    protected $table = 'jenis_nilai';
    protected $primaryKey = 'id_jenis_nilai';

    protected $fillable = [
        'nama_jenis',
        'kode_jenis',
        'keterangan',
        'bobot_nilai',
        'nilai_minimum',
        'nilai_maksimum',
        'status'
    ];

    protected $casts = [
        'bobot_nilai' => 'decimal:2',
        'nilai_minimum' => 'decimal:2',
        'nilai_maksimum' => 'decimal:2',
        'status' => 'string'
    ];

    // Relationships
    public function nilaiPeserta(): HasMany
    {
        return $this->hasMany(NilaiPeserta::class, 'id_jenis_nilai', 'id_jenis_nilai');
    }

    // Scopes
    public function scopeAktif($query)
    {
        return $query->where('status', 'aktif');
    }

    public function scopeByKode($query, $kode)
    {
        return $query->where('kode_jenis', $kode);
    }

    // Helper methods
    public function isAktif(): bool
    {
        return $this->status === 'aktif';
    }

    public function validateNilai(float $nilai): bool
    {
        return $nilai >= $this->nilai_minimum && $nilai <= $this->nilai_maksimum;
    }

    public function calculateWeightedScore(float $nilai): float
    {
        if (!$this->validateNilai($nilai)) {
            return 0;
        }
        
        return $nilai * $this->bobot_nilai;
    }

    public function getFormattedRangeAttribute(): string
    {
        return "{$this->nilai_minimum} - {$this->nilai_maksimum}";
    }

    public function getBobotPercentageAttribute(): string
    {
        return number_format($this->bobot_nilai * 100, 1) . '%';
    }
}
