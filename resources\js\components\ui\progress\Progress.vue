<template>
  <div class="relative w-full overflow-hidden rounded-full bg-gray-200" :class="cn('h-2', $attrs.class)">
    <div
      class="h-full w-full flex-1 bg-primary transition-all duration-300 ease-in-out"
      :style="{ transform: `translateX(-${100 - (value || 0)}%)` }"
    />
  </div>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'

interface ProgressProps {
  value?: number
  class?: string
}

defineProps<ProgressProps>()
</script>
