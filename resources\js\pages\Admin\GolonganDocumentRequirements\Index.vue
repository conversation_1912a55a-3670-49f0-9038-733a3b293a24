<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Persyaratan Dokumen Golongan" />
    <Heading title="Persyaratan Dokumen Golongan" />

    <div class="space-y-6">
      <!-- Filters and Search -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama golongan, kode..."
                @input="search"
              />
            </div>
            <div>
              <Label for="cabang_lomba">Cabang Lomba</Label>
              <Select v-model="filters.cabang_lomba" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Cabang Lomba" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Cabang Lomba</SelectItem>
                  <SelectItem v-for="cabang in cabangLomba" :key="cabang.id_cabang" :value="cabang.id_cabang.toString()">
                    {{ cabang.nama_cabang }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end">
              <Button @click="clearFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Bulk Actions -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-600">
              Menampilkan {{ golongan.from }} - {{ golongan.to }} dari {{ golongan.total }} golongan
            </div>
            <div class="flex items-center space-x-2">
              <Button @click="openBulkUpdateModal" variant="outline">
                <Icon name="settings" class="w-4 h-4 mr-2" />
                Bulk Update
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Golongan List -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card v-for="item in golongan.data" :key="item.id_golongan" class="hover:shadow-md transition-shadow">
          <CardContent class="p-6">
            <div class="flex items-center justify-between mb-4">
              <div>
                <h3 class="text-lg font-medium text-gray-900">{{ item.nama_golongan }}</h3>
                <p class="text-sm text-gray-500">{{ item.kode_golongan }} • {{ item.cabang_lomba?.nama_cabang }}</p>
              </div>
              <Badge :variant="item.status === 'aktif' ? 'default' : 'secondary'">
                {{ item.status }}
              </Badge>
            </div>

            <!-- Document Requirements Summary -->
            <div class="mb-4">
              <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Persyaratan Dokumen</span>
                <span>{{ getRequiredCount(item) }}/{{ getTotalCount(item) }} wajib</span>
              </div>
              <div class="flex flex-wrap gap-1">
                <span 
                  v-for="docType in item.all_document_types" 
                  :key="docType.id_document_type"
                  :class="[
                    'inline-flex items-center px-2 py-1 rounded text-xs font-medium',
                    docType.pivot.is_required 
                      ? 'bg-red-100 text-red-800' 
                      : 'bg-gray-100 text-gray-800'
                  ]"
                >
                  {{ docType.name }}
                  <Icon 
                    :name="docType.pivot.is_required ? 'star' : 'star-outline'" 
                    class="w-3 h-3 ml-1" 
                  />
                </span>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-between pt-4 border-t">
              <div class="text-xs text-gray-500">
                {{ item.jenis_kelamin === 'L' ? 'Putra' : 'Putri' }} • 
                {{ item.batas_umur_min }}-{{ item.batas_umur_max }} tahun
              </div>
              <Button
                @click="$inertia.visit(route('admin.golongan-document-requirements.show', item.id_golongan))"
                size="sm"
              >
                <Icon name="settings" class="w-4 h-4 mr-2" />
                Kelola
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Pagination -->
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Menampilkan {{ golongan.from }} sampai {{ golongan.to }} dari {{ golongan.total }} hasil
        </div>
        <Pagination
          :current-page="golongan.current_page"
          :last-page="golongan.last_page"
          :links="golongan.links"
        />
      </div>
    </div>

    <!-- Bulk Update Modal -->
    <Dialog v-model:open="bulkUpdateModal.open">
      <DialogContent class="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Bulk Update Persyaratan Dokumen</DialogTitle>
          <DialogDescription>
            Atur persyaratan dokumen untuk beberapa golongan sekaligus
          </DialogDescription>
        </DialogHeader>

        <div class="space-y-4">
          <!-- Golongan Selection -->
          <div>
            <Label>Pilih Golongan</Label>
            <div class="max-h-40 overflow-y-auto border rounded-md p-2 space-y-2">
              <div v-for="item in golongan.data" :key="item.id_golongan" class="flex items-center space-x-2">
                <Checkbox
                  :id="`golongan-${item.id_golongan}`"
                  :checked="bulkUpdateModal.selectedGolongan.includes(item.id_golongan)"
                  @update:checked="toggleGolonganSelection(item.id_golongan)"
                />
                <Label :for="`golongan-${item.id_golongan}`" class="text-sm">
                  {{ item.nama_golongan }} ({{ item.cabang_lomba?.nama_cabang }})
                </Label>
              </div>
            </div>
          </div>

          <!-- Document Requirements -->
          <div>
            <Label>Persyaratan Dokumen</Label>
            <div class="space-y-2">
              <div v-for="docType in documentTypes" :key="docType.id_document_type" class="flex items-center justify-between p-2 border rounded">
                <div>
                  <span class="text-sm font-medium">{{ docType.name }}</span>
                  <p class="text-xs text-gray-500">{{ docType.description }}</p>
                </div>
                <Select v-model="bulkUpdateModal.requirements[docType.id_document_type]">
                  <SelectTrigger class="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="required">Wajib</SelectItem>
                    <SelectItem value="optional">Opsional</SelectItem>
                    <SelectItem value="exclude">Tidak Perlu</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="closeBulkUpdateModal">Batal</Button>
          <Button @click="submitBulkUpdate" :disabled="bulkUpdateForm.processing">
            {{ bulkUpdateForm.processing ? 'Menyimpan...' : 'Simpan' }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </AppLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Head, router, useForm } from '@inertiajs/vue3'
import { debounce } from 'lodash'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import Pagination from '@/components/Pagination.vue'
import Icon from '@/components/Icon.vue'

const props = defineProps({
  golongan: Object,
  cabangLomba: Array,
  documentTypes: Array,
  filters: Object
})

const breadcrumbItems = [
  { label: 'Dashboard', href: route('admin.dashboard') },
  { label: 'Persyaratan Dokumen Golongan', href: null }
]

const filters = reactive({
  search: props.filters?.search || '',
  cabang_lomba: props.filters?.cabang_lomba || 'all'
})

const bulkUpdateModal = reactive({
  open: false,
  selectedGolongan: [],
  requirements: {}
})

const bulkUpdateForm = useForm({
  golongan_ids: [],
  requirements: []
})

const search = debounce(() => {
  router.get(route('admin.golongan-document-requirements.index'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const clearFilters = () => {
  filters.search = ''
  filters.cabang_lomba = 'all'
  search()
}

const getRequiredCount = (golongan) => {
  return golongan.all_document_types?.filter(dt => dt.pivot.is_required).length || 0
}

const getTotalCount = (golongan) => {
  return golongan.all_document_types?.length || 0
}

const openBulkUpdateModal = () => {
  // Initialize requirements with default values
  props.documentTypes.forEach(docType => {
    bulkUpdateModal.requirements[docType.id_document_type] = docType.is_required_default ? 'required' : 'optional'
  })
  bulkUpdateModal.open = true
}

const closeBulkUpdateModal = () => {
  bulkUpdateModal.open = false
  bulkUpdateModal.selectedGolongan = []
  bulkUpdateModal.requirements = {}
}

const toggleGolonganSelection = (golonganId) => {
  const index = bulkUpdateModal.selectedGolongan.indexOf(golonganId)
  if (index > -1) {
    bulkUpdateModal.selectedGolongan.splice(index, 1)
  } else {
    bulkUpdateModal.selectedGolongan.push(golonganId)
  }
}

const submitBulkUpdate = () => {
  const requirements = Object.entries(bulkUpdateModal.requirements)
    .filter(([_, status]) => status !== 'exclude')
    .map(([docTypeId, status]) => ({
      document_type_id: parseInt(docTypeId),
      is_required: status === 'required'
    }))

  bulkUpdateForm.golongan_ids = bulkUpdateModal.selectedGolongan
  bulkUpdateForm.requirements = requirements

  bulkUpdateForm.post(route('admin.golongan-document-requirements.bulk-update'), {
    onSuccess: () => {
      closeBulkUpdateModal()
    }
  })
}
</script>
